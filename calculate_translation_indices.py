import mysql.connector
from mysql.connector import Error
import csv
import pandas as pd
import numpy as np
from collections import defaultdict
import os

# 数据库配置信息
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def create_connection():
    """创建数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        print(f"数据库连接错误: {e}")
        return None

def get_strategy_types(connection, project_id):
    """获取特定项目的所有策略类型"""
    query = """
    SELECT DISTINCT strategy 
    FROM gseMatch 
    WHERE projectId = %s
    """
    
    cursor = connection.cursor()
    cursor.execute(query, (project_id,))
    results = cursor.fetchall()
    
    return [result[0] for result in results]

def get_tpm_data(connection, project_id, strategy, transcript_id, sra_acc_list=None):
    """获取特定策略下转录本的TPM值及SRA数量"""
    if sra_acc_list and len(sra_acc_list) > 0:
        # 将SRA编号列表格式化为SQL IN语句
        sra_placeholders = ','.join(['%s'] * len(sra_acc_list))
        query = f"""
        SELECT g.sraAccession, t.tpm 
        FROM gseMatch g
        JOIN tpmData t ON g.sraAccession = t.sraAccession
        WHERE g.projectId = %s
        AND g.strategy = %s
        AND t.transcriptId = %s
        AND g.sraAccession IN ({sra_placeholders})
        """
        
        # 创建参数列表
        params = [project_id, strategy, transcript_id] + sra_acc_list
        
        cursor = connection.cursor()
        cursor.execute(query, params)
    else:
        query = """
        SELECT g.sraAccession, t.tpm 
        FROM gseMatch g
        JOIN tpmData t ON g.sraAccession = t.sraAccession
        WHERE g.projectId = %s
        AND g.strategy = %s
        AND t.transcriptId = %s
        """
        
        cursor = connection.cursor()
        cursor.execute(query, (project_id, strategy, transcript_id))
    
    results = cursor.fetchall()
    
    # 收集有效的TPM值
    valid_tpm_values = []
    for result in results:
        print(f"SRA: {result[0]}, TPM: {result[1]}")
        valid_tpm_values.append(result[1])
    
    # 获取实际有转录本记录的SRA数量
    valid_sra_count = len(set([result[0] for result in results]))
    
    return valid_tpm_values, valid_sra_count

def calculate_strategy_value(connection, project_id, strategy, transcript_id, sra_acc_list=None):
    """计算特定策略的值"""
    tpm_values, valid_sra_count = get_tpm_data(connection, project_id, strategy, transcript_id, sra_acc_list)
    
    if not tpm_values or valid_sra_count == 0:
        return None, 0, 0
    
    # 返回: TPM平均值, 有效SRA数量, TPM值总和
    return sum(tpm_values) / valid_sra_count, valid_sra_count, sum(tpm_values)

def calculate_indices(p_value, m_value, r_value):
    """计算翻译指标"""
    indices = {}
    
    # 检查哪些指标可以计算
    can_calc_tr = r_value is not None and m_value is not None and m_value != 0
    can_calc_evi = r_value is not None and m_value is not None and p_value is not None and m_value != 0 and p_value != 0
    can_calc_te = p_value is not None and m_value is not None and m_value != 0
    
    # 计算指标
    if can_calc_tr:
        indices['TR'] = r_value / m_value
    
    if can_calc_evi:
        indices['EVI'] = (r_value ** 2) / (m_value * p_value)
    
    if can_calc_te:
        indices['TE'] = p_value / m_value
    
    return indices

def read_csv_file(file_path):
    """读取CSV文件并过滤出GSE46613项目的行"""
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        # 过滤出Project ID为GSE46613的行
        filtered_df = df[df['Project ID'] == 'GSE46613']
        
        # 将空字符串和'NA'替换为NaN，方便分组处理
        filtered_df = filtered_df.replace(['NA', ''], np.nan)
        
        print(f"找到GSE46613项目的行数: {len(filtered_df)}")
        print("前5行数据:")
        print(filtered_df.head())
        
        return filtered_df
    except Exception as e:
        print(f"读取CSV文件错误: {e}")
        return None

def group_by_attributes(df):
    """按照Tissue, Cell Type, Cell line, Healthy Condition进行分组"""
    # 打印各列有多少种不同的值
    print("\n各列的唯一值:")
    print(f"Tissue唯一值: {df['Tissue'].unique()}")
    print(f"Cell Type唯一值: {df['Cell Type'].unique()}")
    print(f"CelI line唯一值: {df['CelI line'].unique()}")
    print(f"Healthy Condition唯一值: {df['Healthy Condition'].unique()}")
    
    # 使用fillna将NaN值替换为一个特殊标记，以便于分组
    # 否则，pandas会将NaN视为不同的值
    df_for_grouping = df.copy()
    sentinel = "__NA__"  # 特殊标记
    
    # 替换NaN值
    df_for_grouping['Tissue'] = df_for_grouping['Tissue'].fillna(sentinel)
    df_for_grouping['Cell Type'] = df_for_grouping['Cell Type'].fillna(sentinel)
    df_for_grouping['CelI line'] = df_for_grouping['CelI line'].fillna(sentinel)
    df_for_grouping['Healthy Condition'] = df_for_grouping['Healthy Condition'].fillna(sentinel)
    
    # 创建分组键
    grouped = df_for_grouping.groupby(['Tissue', 'Cell Type', 'CelI line', 'Healthy Condition'])
    
    # 打印分组数量和每个分组的大小
    print(f"\n分组总数: {len(grouped)}")
    for group_key, group_data in grouped:
        print(f"分组 {group_key}: {len(group_data)}行")
    
    return grouped

def process_group(group_data, transcript_id, connection, original_df):
    """处理每个分组的数据"""
    # 确保每个分组至少有相应策略类型的数据
    project_id = 'GSE46613'  # 固定为GSE46613
    
    # 获取该分组的属性（需要处理特殊标记）
    sentinel = "__NA__"
    tissue = group_data['Tissue'].iloc[0]
    cell_type = group_data['Cell Type'].iloc[0]
    cell_line = group_data['CelI line'].iloc[0]
    healthy_condition = group_data['Healthy Condition'].iloc[0]
    
    # 将特殊标记替换回NaN
    tissue = None if tissue == sentinel else tissue
    cell_type = None if cell_type == sentinel else cell_type
    cell_line = None if cell_line == sentinel else cell_line
    healthy_condition = None if healthy_condition == sentinel else healthy_condition
    
    # 在原始DataFrame中找到对应行
    mask = (
        (pd.isna(original_df['Tissue']) if tissue is None else original_df['Tissue'] == tissue) & 
        (pd.isna(original_df['Cell Type']) if cell_type is None else original_df['Cell Type'] == cell_type) & 
        (pd.isna(original_df['CelI line']) if cell_line is None else original_df['CelI line'] == cell_line) & 
        (pd.isna(original_df['Healthy Condition']) if healthy_condition is None else original_df['Healthy Condition'] == healthy_condition)
    )
    original_group_data = original_df[mask]
    
    # 输出分组信息
    print(f"\n处理分组: Tissue={tissue}, Cell Type={cell_type}, Cell Line={cell_line}, Healthy Condition={healthy_condition}")
    print(f"该分组中的行数: {len(original_group_data)}")
    
    # 策略映射
    strategy_map = {
        'Ribo': 'P',
        'RNA': 'M',
        'RNC': 'R'
    }
    
    # 获取该分组中的所有策略类型
    strategies = original_group_data['Strategy'].unique()
    print(f"该分组中的策略类型: {', '.join(strategies)}")
    
    # 获取每种策略的值
    values = {}
    for strategy in strategies:
        if strategy in strategy_map:
            # 仅处理该分组下的数据
            sra_acc_list = original_group_data[original_group_data['Strategy'] == strategy]['SRA Accession'].tolist()
            if not sra_acc_list:
                continue
            
            print(f"\n处理策略 {strategy} 下的 {len(sra_acc_list)} 个SRA编号:")
            print(', '.join(sra_acc_list))
            
            # 使用数据库查询的函数获取TPM值
            value, valid_count, tpm_sum = calculate_strategy_value(connection, project_id, strategy, transcript_id, sra_acc_list)
            if value is not None:
                values[strategy_map[strategy]] = value
                print(f"{strategy} ({strategy_map[strategy]}) 值: {value:.4f}")
                print(f"  - 有效SRA数量: {valid_count}")
                print(f"  - TPM总和: {tpm_sum:.4f}")
                print(f"  - 计算公式: {tpm_sum:.4f} / {valid_count} = {value:.4f}")
            else:
                print(f"策略 {strategy} 没有找到有效的TPM值")
    
    # 检查是否至少有两种策略
    results = {}
    results['transcript_id'] = transcript_id
    results['Project ID'] = project_id
    results['Tissue'] = tissue
    results['Cell Type'] = cell_type
    results['Cell line'] = cell_line
    results['Healthy Condition'] = healthy_condition
    
    print(f"\n该分组中有 {len(values)} 种策略有有效值: {', '.join(values.keys())}")
    
    if len(values) >= 2:
        # 计算指标
        p_value = values.get('P')
        m_value = values.get('M')
        r_value = values.get('R')
        
        indices = calculate_indices(p_value, m_value, r_value)
        
        # 打印结果
        print("\n计算结果:")
        if 'TR' in indices:
            results['TR'] = indices['TR']
            print(f"Translation Ratio (TR) = R/M = {r_value:.4f}/{m_value:.4f} = {indices['TR']:.4f}")
        else:
            results['TR'] = None
            print("无法计算TR: 缺少R或M值")
            
        if 'EVI' in indices:
            results['EVI'] = indices['EVI']
            print(f"Elongation Velocity Index (EVI) = R²/(M*P) = {r_value:.4f}²/({m_value:.4f}*{p_value:.4f}) = {indices['EVI']:.4f}")
        else:
            results['EVI'] = None
            print("无法计算EVI: 缺少R, M或P值")
            
        if 'TE' in indices:
            results['TE'] = indices['TE']
            print(f"Translational Efficiency (TE) = P/M = {p_value:.4f}/{m_value:.4f} = {indices['TE']:.4f}")
        else:
            results['TE'] = None
            print("无法计算TE: 缺少P或M值")
    else:
        print(f"无法计算指标: 至少需要两种策略类型的数据，但只有 {len(values)} 种。")
        results['TR'] = None
        results['EVI'] = None
        results['TE'] = None
    
    return results

def main():
    # 设置查询参数
    transcript_id = "ENST00000554222"
    csv_file_path = "GSE_match_new.csv"
    
    # 创建数据库连接
    connection = create_connection()
    if not connection:
        return
    
    try:
        # 读取CSV文件
        df = read_csv_file(csv_file_path)
        if df is None or df.empty:
            print("找不到GSE46613项目的数据")
            return
        
        # 按照属性分组
        grouped = group_by_attributes(df)
        
        # 存储所有结果
        all_results = []
        
        # 为每个分组计算翻译指标
        for group_key, group_data in grouped:
            # 处理该分组的数据
            result = process_group(group_data, transcript_id, connection, df)
            all_results.append(result)
        
        # 检查是否有结果
        if not all_results:
            print("\n没有找到任何结果!")
            return
        
        # 输出所有结果
        print("\n所有分组的结果:")
        print(f"{'transcript_id':<20} {'Project ID':<15} {'Tissue':<20} {'Cell Type':<30} {'Cell line':<20} {'Healthy Condition':<20} {'Strategy':<10} {'TR':<10} {'EVI':<10} {'TE':<10}")
        print("-" * 160)
        
        for result in all_results:
            tissue = result['Tissue'] if result['Tissue'] is not None else 'NA'
            cell_type = result['Cell Type'] if result['Cell Type'] is not None else 'NA'
            cell_line = result['Cell line'] if result['Cell line'] is not None else 'NA'
            healthy_condition = result['Healthy Condition'] if result['Healthy Condition'] is not None else 'NA'
            tr = f"{result['TR']:.4f}" if result['TR'] is not None else 'NA'
            evi = f"{result['EVI']:.4f}" if result['EVI'] is not None else 'NA'
            te = f"{result['TE']:.4f}" if result['TE'] is not None else 'NA'
            
            # 获取该分组的策略类型
            mask = (
                (pd.isna(df['Tissue']) if tissue == 'NA' else df['Tissue'] == tissue) & 
                (pd.isna(df['Cell Type']) if cell_type == 'NA' else df['Cell Type'] == cell_type) & 
                (pd.isna(df['CelI line']) if cell_line == 'NA' else df['CelI line'] == cell_line) & 
                (pd.isna(df['Healthy Condition']) if healthy_condition == 'NA' else df['Healthy Condition'] == healthy_condition)
            )
            strategies = df[mask]['Strategy'].unique()
            
            strategy_str = '/'.join(strategies)
            
            print(f"{result['transcript_id']:<20} {result['Project ID']:<15} {tissue:<20} {cell_type:<30} {cell_line:<20} {healthy_condition:<20} {strategy_str:<10} {tr:<10} {evi:<10} {te:<10}")
        
        # 将结果写入CSV文件
        output_file = f"{transcript_id}_GSE46613_results.csv"
        if os.path.exists(output_file):
            os.remove(output_file)
            print(f"已删除已存在的旧结果文件: {output_file}")
        with open(output_file, 'w', newline='') as csvfile:
            fieldnames = ['transcript_id', 'Project ID', 'Tissue', 'Cell Type', 'Cell line', 'Healthy Condition', 'Strategy', 'TR', 'EVI', 'TE']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in all_results:
                # 获取该分组的策略类型
                tissue = result['Tissue'] if result['Tissue'] is not None else 'NA'
                cell_type = result['Cell Type'] if result['Cell Type'] is not None else 'NA'
                cell_line = result['Cell line'] if result['Cell line'] is not None else 'NA'
                healthy_condition = result['Healthy Condition'] if result['Healthy Condition'] is not None else 'NA'
                
                mask = (
                    (pd.isna(df['Tissue']) if tissue == 'NA' else df['Tissue'] == tissue) & 
                    (pd.isna(df['Cell Type']) if cell_type == 'NA' else df['Cell Type'] == cell_type) & 
                    (pd.isna(df['CelI line']) if cell_line == 'NA' else df['CelI line'] == cell_line) & 
                    (pd.isna(df['Healthy Condition']) if healthy_condition == 'NA' else df['Healthy Condition'] == healthy_condition)
                )
                strategies = df[mask]['Strategy'].unique()
                
                strategy_str = '/'.join(strategies)
                
                row = {
                    'transcript_id': result['transcript_id'],
                    'Project ID': result['Project ID'],
                    'Tissue': tissue,
                    'Cell Type': cell_type,
                    'Cell line': cell_line,
                    'Healthy Condition': healthy_condition,
                    'Strategy': strategy_str,
                    'TR': result['TR'],
                    'EVI': result['EVI'],
                    'TE': result['TE']
                }
                writer.writerow(row)
        
        print(f"\n结果已写入文件: {output_file}")
    
    except Error as e:
        print(f"查询错误: {e}")
    except Exception as e:
        print(f"程序错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if connection and connection.is_connected():
            connection.close()
            print("数据库连接已关闭")

if __name__ == "__main__":
    main() 