#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import json
import logging
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('add_gene_info.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def load_gene_info_json(json_file):
    """加载基因信息JSON文件并创建查找字典"""
    logging.info(f"加载基因信息文件: {json_file}")
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            gene_data = json.load(f)
        
        # 创建以gene_id为键的字典
        gene_lookup = {}
        for gene_info in gene_data:
            gene_id = gene_info.get('gene_id')
            if gene_id:
                gene_lookup[gene_id] = gene_info
        
        logging.info(f"成功加载 {len(gene_lookup)} 个基因的信息")
        return gene_lookup
    
    except Exception as e:
        logging.error(f"加载JSON文件时出错: {e}")
        return {}

def extract_gene_fields(gene_info):
    """从基因信息中提取所需字段"""
    # 提取 Official Symbol 的 main_text
    gene_symbol = ""
    if 'Official Symbol' in gene_info and isinstance(gene_info['Official Symbol'], dict):
        gene_symbol = gene_info['Official Symbol'].get('main_text', '')
    
    # 提取 Official Full Name 的 main_text
    approved_name = ""
    if 'Official Full Name' in gene_info and isinstance(gene_info['Official Full Name'], dict):
        approved_name = gene_info['Official Full Name'].get('main_text', '')
    
    # 提取 Gene type 的 text
    locus_type = ""
    if 'Gene type' in gene_info and isinstance(gene_info['Gene type'], dict):
        locus_type = gene_info['Gene type'].get('text', '')
    
    # 提取 Location 的 text
    chromosome = ""
    if 'Location' in gene_info and isinstance(gene_info['Location'], dict):
        chromosome = gene_info['Location'].get('text', '')
    
    # 提取 Summary 的 text
    gene_summary = ""
    if 'Summary' in gene_info and isinstance(gene_info['Summary'], dict):
        gene_summary = gene_info['Summary'].get('text', '')
    
    return {
        'GENE symbol': gene_symbol,
        'Approved Name': approved_name,
        'Locus Type': locus_type,
        'Chromosome': chromosome,
        'Gene Summary': gene_summary
    }

def process_csv_file(csv_file, json_file, output_file):
    """处理CSV文件，添加基因信息"""
    # 加载基因信息
    gene_lookup = load_gene_info_json(json_file)
    
    if not gene_lookup:
        logging.error("无法加载基因信息，程序终止")
        return False
    
    # 读取CSV文件
    logging.info(f"读取CSV文件: {csv_file}")
    try:
        df = pd.read_csv(csv_file)
        logging.info(f"CSV文件包含 {len(df)} 行数据")
    except Exception as e:
        logging.error(f"读取CSV文件时出错: {e}")
        return False
    
    # 初始化新列
    df['GENE symbol'] = ''
    df['Approved Name'] = ''
    df['Locus Type'] = ''
    df['Chromosome'] = ''
    df['Gene Summary'] = ''
    
    # 统计信息
    found_count = 0
    missing_count = 0
    missing_genes = []
    
    # 处理每一行
    for index, row in df.iterrows():
        gene_id = row['ensembl_gene_id']
        
        if gene_id in gene_lookup:
            # 提取基因信息
            gene_fields = extract_gene_fields(gene_lookup[gene_id])
            
            # 更新DataFrame
            for field_name, field_value in gene_fields.items():
                df.at[index, field_name] = field_value
            
            found_count += 1
        else:
            missing_count += 1
            missing_genes.append(gene_id)
            logging.warning(f"未找到基因信息: {gene_id}")
        
        # 每处理1000个基因显示一次进度
        if (index + 1) % 1000 == 0:
            logging.info(f"已处理 {index + 1}/{len(df)} 个基因")
    
    # 保存结果
    logging.info(f"保存结果到: {output_file}")
    try:
        df.to_csv(output_file, index=False)
        logging.info("文件保存成功")
    except Exception as e:
        logging.error(f"保存文件时出错: {e}")
        return False
    
    # 显示统计信息
    logging.info("\n处理完成统计:")
    logging.info(f"  总基因数: {len(df)}")
    logging.info(f"  找到信息的基因: {found_count}")
    logging.info(f"  缺失信息的基因: {missing_count}")
    logging.info(f"  成功率: {(found_count/len(df)*100):.2f}%")
    
    # 如果有缺失的基因，保存到文件
    if missing_genes:
        missing_file = "missing_gene_info.txt"
        with open(missing_file, 'w') as f:
            for gene_id in missing_genes:
                f.write(f"{gene_id}\n")
        logging.info(f"缺失信息的基因ID已保存到: {missing_file}")
    
    # 显示前几行示例
    logging.info("\n前5行结果示例:")
    for i in range(min(5, len(df))):
        row = df.iloc[i]
        logging.info(f"  {row['ensembl_gene_id']} -> {row['GENE symbol']} | {row['Locus Type']} | {row['Chromosome']}")
    
    return True

def main():
    """主函数"""
    csv_file = "gene_info/gene_ids/ensembl_gene_id_unique.csv"
    json_file = "gene_info/gene_info_results_merged.json"
    output_file = "ensembl_gene_id_with_info.csv"
    
    # 检查文件是否存在
    import os
    if not os.path.exists(csv_file):
        logging.error(f"CSV文件不存在: {csv_file}")
        return
    
    if not os.path.exists(json_file):
        logging.error(f"JSON文件不存在: {json_file}")
        return
    
    # 处理文件
    success = process_csv_file(csv_file, json_file, output_file)
    
    if success:
        logging.info("基因信息添加任务完成！")
    else:
        logging.error("基因信息添加任务失败！")

if __name__ == "__main__":
    main() 