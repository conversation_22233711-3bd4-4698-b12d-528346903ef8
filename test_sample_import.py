#!/usr/bin/env python3
import csv

print("模拟Sample.csv导入过程...")

csv_path = 'Browse-Sample/Sample.csv'

# 模拟导入脚本的逻辑
samples = []
row_count = 0
valid_count = 0
translated_transcripts_count = 0

with open(csv_path, 'r', encoding='utf-8') as f:
    reader = csv.DictReader(f)
    for row in reader:
        row_count += 1
        
        # 跳过前几行看看数据
        if row_count <= 5:
            print(f"\n第{row_count}行原始数据:")
            for key, value in row.items():
                print(f"  {key}: '{value}' (类型: {type(value)})")
        
        # 跳过空行或SRA Accession为空的行
        if not row or not row.get('SRA Accession') or row.get('SRA Accession') == 'NA':
            print(f"跳过第 {row_count} 行: SRA Accession为空或NA")
            continue
            
        # 处理TRANSLATED TRANSCRIPTS NUMBER为数值类型
        translated_transcripts = None
        raw_value = row.get('TRANSLATED TRANSCRIPTS NUMBER')
        print(f"第{row_count}行 TRANSLATED TRANSCRIPTS NUMBER 原始值: '{raw_value}' (类型: {type(raw_value)})")
        
        if raw_value and raw_value != 'NA':
            try:
                translated_transcripts = int(raw_value)
                translated_transcripts_count += 1
                print(f"  -> 转换成功: {translated_transcripts}")
            except ValueError as e:
                print(f"  -> 转换失败: {e}")
                translated_transcripts = None
        else:
            print(f"  -> 跳过: 值为空或NA")

        sample_data = (
            row['SRA Accession'] if row['SRA Accession'] != 'NA' else None,
            row['Dataset ID'] if row['Dataset ID'] != 'NA' else None,
            row['GEO_Accession'] if row['GEO_Accession'] != 'NA' else None,
            row['BioProject ID'] if row['BioProject ID'] != 'NA' else None,
            row['BioSample ID'] if row['BioSample ID'] != 'NA' else None,
            row['Tissue/Cell Type'] if row['Tissue/Cell Type'] != 'NA' else None,
            row['Cell line'] if row['Cell line'] != 'NA' else None,
            row['Condition'] if row['Condition'] != 'NA' else None,
            row['Disease Category'] if row['Disease Category'] != 'NA' else None,
            row['Data Type'] if row['Data Type'] != 'NA' else None,
            row['Platform'] if row['Platform'] != 'NA' else None,
            row['Instrument'] if row['Instrument'] != 'NA' else None,
            row['LibraryLayout'] if row['LibraryLayout'] != 'NA' else None,
            row['Detail'] if row['Detail'] != 'NA' else None,
            translated_transcripts
        )
        
        if row_count <= 5:
            print(f"  最终数据元组的最后一个元素 (translatedTranscriptsNumber): {sample_data[-1]}")
        
        samples.append(sample_data)
        valid_count += 1
        
        # 只处理前10行用于测试
        if row_count >= 10:
            break

print(f"\n总结:")
print(f"CSV文件共读取 {row_count} 行，有效数据 {valid_count} 行")
print(f"成功转换的 translatedTranscriptsNumber: {translated_transcripts_count}")

# 显示将要插入的数据示例
print(f"\n前3条数据示例:")
for i, sample in enumerate(samples[:3]):
    print(f"第{i+1}条: SRA={sample[0]}, translatedTranscriptsNumber={sample[-1]}")