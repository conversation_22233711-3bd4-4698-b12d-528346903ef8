import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
from tqdm import tqdm

def fetch_gene_info(gene_id):
    """
    Fetch gene information from NCBI Gene database
    
    Args:
        gene_id: The gene ID or transcript ID (e.g., ENST00000673477)
        
    Returns:
        Dictionary containing the gene information
    """
    url = f"https://www.ncbi.nlm.nih.gov/gene/?term={gene_id}"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Initialize the results dictionary
            gene_info = {}
            
            # Extract information from the summary section
            summary_div = soup.find('div', class_='rprt-section gene-summary')
            if summary_div:
                dl = summary_div.find('dl', id='summaryDl')
                if dl:
                    # Process each dt/dd pair
                    dt_elements = dl.find_all('dt')
                    dd_elements = dl.find_all('dd')
                    
                    for i in range(len(dt_elements)):
                        if i < len(dd_elements):
                            key = dt_elements[i].text.strip()
                            # Extract just the text, not the child elements
                            value = dd_elements[i].get_text(strip=True)
                            # Stop at "Expression" entry to match requirements
                            if key == "Summary":
                                gene_info[key] = value
                                break
                            gene_info[key] = value
            
            # Extract chromosome location
            chr_info = soup.find('dl', class_='dl-chr-info')
            if chr_info:
                dt_elements = chr_info.find_all('dt')
                dd_elements = chr_info.find_all('dd')
                
                for i in range(len(dt_elements)):
                    if i < len(dd_elements):
                        key = dt_elements[i].text.strip()
                        value = dd_elements[i].get_text(strip=True)
                        gene_info[key] = value
            
            return gene_info
        else:
            print(f"Error fetching gene information: Status code {response.status_code}")
            return None
    except Exception as e:
        print(f"Exception when fetching gene information: {str(e)}")
        return None

def process_gene_list(gene_ids, output_file):
    """
    Process a list of gene IDs and save the results to a CSV file
    
    Args:
        gene_ids: List of gene IDs to process
        output_file: Path to the output CSV file
    """
    results = []
    
    for gene_id in tqdm(gene_ids, desc="Fetching gene information"):
        gene_info = fetch_gene_info(gene_id)
        if gene_info:
            gene_info['gene_id'] = gene_id
            results.append(gene_info)
        
        # Add a small delay to avoid hitting rate limits
        time.sleep(1)
    
    # Convert to DataFrame and save to CSV
    df = pd.DataFrame(results)
    df.to_csv(output_file, index=False)
    print(f"Data saved to {output_file}")

def process_gene_file(input_file, output_file, id_column):
    """
    Process a CSV file containing gene IDs
    
    Args:
        input_file: Path to the input CSV file
        output_file: Path to the output CSV file
        id_column: Name of the column containing gene IDs
    """
    df = pd.read_csv(input_file)
    gene_ids = df[id_column].tolist()
    
    # Remove any NaN values
    gene_ids = [str(gene_id) for gene_id in gene_ids if str(gene_id) != 'nan']
    
    process_gene_list(gene_ids, output_file)

if __name__ == "__main__":
    # Example usage
    # 1. Process a single gene ID
    gene_info = fetch_gene_info("ENST00000673477")
    print(gene_info)
    
    # 2. Process a list of gene IDs
    # gene_ids = ["ENST00000673477", "ENSG00000160072"]
    # process_gene_list(gene_ids, "gene_info.csv")
    
    # 3. Process a CSV file
    # process_gene_file("input.csv", "gene_info.csv", "gene_id_column") 