import pandas as pd
import requests
import time
import xml.etree.ElementTree as ET
from tqdm import tqdm

def fetch_pubmed_data(pmid):
    """
    Fetch detailed information from PubMed for a given PMID and extract key fields
    """
    base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi"
    
    # Parameters for the API request
    params = {
        "db": "pubmed",
        "id": pmid,
        "retmode": "xml",
        "rettype": "abstract"
    }
    
    try:
        # Make the request with a small delay to avoid hitting rate limits
        response = requests.get(base_url, params=params)
        
        if response.status_code == 200:
            # Parse the XML response
            root = ET.fromstring(response.text)
            
            # Extract key information
            article_data = {
                "pmid": pmid,
                "title": "",
                "journal": "",
                "pub_date": "",
                "abstract": "",
                "authors": [],
                "keywords": []
            }
            
            # Extract title
            title_element = root.find(".//ArticleTitle")
            if title_element is not None:
                article_data["title"] = title_element.text
            
            # Extract journal info
            journal_element = root.find(".//Journal/Title")
            if journal_element is not None:
                article_data["journal"] = journal_element.text
            
            # Extract publication date
            pub_date_elements = root.findall(".//PubDate/*")
            if pub_date_elements:
                article_data["pub_date"] = " ".join([e.text for e in pub_date_elements if e.text])
            
            # Extract abstract
            abstract_elements = root.findall(".//AbstractText")
            if abstract_elements:
                article_data["abstract"] = " ".join([e.text for e in abstract_elements if e.text])
            
            # Extract authors
            author_elements = root.findall(".//Author")
            for author in author_elements:
                last_name = author.find("LastName")
                fore_name = author.find("ForeName")
                if last_name is not None and fore_name is not None:
                    article_data["authors"].append(f"{fore_name.text} {last_name.text}")
            
            # Extract keywords
            keyword_elements = root.findall(".//Keyword")
            article_data["keywords"] = [k.text for k in keyword_elements if k.text]
            
            return article_data
        else:
            print(f"Error fetching PMID {pmid}: Status code {response.status_code}")
            return None
    except Exception as e:
        print(f"Exception when fetching PMID {pmid}: {str(e)}")
        return None

def process_csv_file(input_file, output_file):
    """
    Process the CSV file, fetch PubMed data for each PMID, and save to a new file
    """
    # Read the CSV file
    df = pd.read_csv(input_file)
    
    # Create columns for the extracted PubMed data
    for col in ["pubmed_title", "pubmed_journal", "pubmed_date", "pubmed_abstract", 
                "pubmed_authors", "pubmed_keywords"]:
        df[col] = None
    
    # Process each row
    for index, row in tqdm(df.iterrows(), total=len(df), desc="Fetching PubMed data"):
        # Extract PMID from the first column if it's a valid number
        try:
            pmid = str(row.iloc[0]).strip()
            if pmid.isdigit():
                # Fetch data from PubMed
                article_data = fetch_pubmed_data(pmid)
                
                # Store the extracted data
                if article_data:
                    df.at[index, 'pubmed_title'] = article_data["title"]
                    df.at[index, 'pubmed_journal'] = article_data["journal"]
                    df.at[index, 'pubmed_date'] = article_data["pub_date"]
                    df.at[index, 'pubmed_abstract'] = article_data["abstract"]
                    df.at[index, 'pubmed_authors'] = ", ".join(article_data["authors"])
                    df.at[index, 'pubmed_keywords'] = ", ".join(article_data["keywords"])
                
                # Add a small delay to avoid hitting API rate limits
                time.sleep(0.5)
        except Exception as e:
            print(f"Error processing row {index}: {str(e)}")
    
    # Save the updated dataframe to a new CSV file
    df.to_csv(output_file, index=False)
    print(f"Data saved to {output_file}")

if __name__ == "__main__":
    input_file = "Browse-Publication/paper_info.csv"
    output_file = "Browse-Publication/paper_info_with_pubmed.csv"
    
    process_csv_file(input_file, output_file) 