#!/bin/bash
# 修复numpy环境的脚本

echo "=== 修复numpy环境 ==="

# 1. 备份当前环境
echo "1. 备份当前环境..."
conda list > conda_packages_backup.txt

# 2. 重新安装numpy和pandas
echo "2. 重新安装numpy和pandas..."
conda uninstall numpy pandas scipy -y
conda install numpy pandas scipy -y

# 3. 或者使用pip重新安装
echo "3. 如果conda安装失败，尝试pip..."
pip uninstall numpy pandas scipy -y
pip install numpy pandas scipy

# 4. 测试安装
echo "4. 测试numpy安装..."
python3 -c "import numpy as np; print('Numpy version:', np.__version__); print('Test array:', np.array([1,2,3]))"

echo "=== 修复完成 ==="
