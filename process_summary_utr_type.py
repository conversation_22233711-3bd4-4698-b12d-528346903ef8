#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理 transcriptinfo/processed_output_results_1_summary.json 文件
根据 Region 列的值修改 UTR_Type 列的值：
- 如果 Region 为 "3' UTR"，则 UTR_Type 设为 "3UTR"
- 如果 Region 为 "5' UTR"，则 UTR_Type 设为 "5UTR"
"""

import json
import logging
from collections import Counter

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('process_summary_utr_type.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

INPUT_FILE = 'transcriptinfo/processed_output_results_1_summary.json'
OUTPUT_FILE = 'transcriptinfo/processed_output_results_1_summary_updated.json'

def process_utr_type():
    """处理UTR_Type列的值"""
    try:
        logging.info(f'开始读取文件: {INPUT_FILE}')
        
        # 读取JSON文件
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logging.info(f'文件读取完成，共 {len(data)} 条记录')
        
        # 统计原始数据
        original_utr_types = Counter()
        original_regions = Counter()
        
        for record in data:
            if 'UTR_Type' in record:
                original_utr_types[record['UTR_Type']] += 1
            if 'Region' in record:
                original_regions[record['Region']] += 1
        
        logging.info('原始数据统计:')
        logging.info(f'UTR_Type 分布: {dict(original_utr_types)}')
        logging.info(f'Region 分布: {dict(original_regions)}')
        
        # 记录修改情况
        changes = 0
        unchanged = 0
        
        # 处理每条记录
        for record in data:
            if 'Region' in record and 'UTR_Type' in record:
                region = record['Region']
                original_utr_type = record['UTR_Type']
                
                if region == "3' UTR":
                    new_utr_type = "3UTR"
                elif region == "5' UTR":
                    new_utr_type = "5UTR"
                else:
                    # 如果Region值不是预期的，保持原值
                    logging.warning(f'发现未预期的Region值: {region}')
                    new_utr_type = original_utr_type
                
                if original_utr_type != new_utr_type:
                    record['UTR_Type'] = new_utr_type
                    changes += 1
                else:
                    unchanged += 1
        
        logging.info(f'处理完成: 修改了 {changes} 条记录，{unchanged} 条记录保持不变')
        
        # 统计修改后的数据
        updated_utr_types = Counter()
        updated_regions = Counter()
        
        for record in data:
            if 'UTR_Type' in record:
                updated_utr_types[record['UTR_Type']] += 1
            if 'Region' in record:
                updated_regions[record['Region']] += 1
        
        logging.info('修改后数据统计:')
        logging.info(f'UTR_Type 分布: {dict(updated_utr_types)}')
        logging.info(f'Region 分布: {dict(updated_regions)}')
        
        # 验证修改结果
        logging.info('验证修改结果:')
        three_utr_check = set()
        five_utr_check = set()
        
        for record in data:
            if record.get('Region') == "3' UTR":
                three_utr_check.add(record.get('UTR_Type'))
            elif record.get('Region') == "5' UTR":
                five_utr_check.add(record.get('UTR_Type'))
        
        logging.info(f'Region为"3\' UTR"的记录，UTR_Type值: {three_utr_check}')
        logging.info(f'Region为"5\' UTR"的记录，UTR_Type值: {five_utr_check}')
        
        # 保存修改后的文件
        logging.info(f'保存修改后的文件到: {OUTPUT_FILE}')
        with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logging.info('处理完成！')
        
        # 显示前几条示例
        logging.info('修改后的前5条记录示例:')
        for i in range(min(5, len(data))):
            record = data[i]
            transcript_id = record.get('TranscriptID', 'N/A')
            region = record.get('Region', 'N/A')
            utr_type = record.get('UTR_Type', 'N/A')
            logging.info(f'  TranscriptID: {transcript_id}, Region: {region}, UTR_Type: {utr_type}')
        
        return True
        
    except Exception as e:
        logging.error(f'处理过程中发生错误: {str(e)}')
        return False

def main():
    """主函数"""
    logging.info('开始处理JSON文件中的UTR_Type列...')
    success = process_utr_type()
    
    if success:
        logging.info('UTR_Type列处理任务完成！')
    else:
        logging.error('UTR_Type列处理任务失败！')

if __name__ == '__main__':
    main()
