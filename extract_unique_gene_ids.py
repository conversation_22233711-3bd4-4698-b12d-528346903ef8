#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import logging
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extract_gene_ids.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def extract_unique_gene_ids(input_file, output_file):
    """提取GENE ID列的唯一值并保存到CSV文件"""
    try:
        logging.info(f"开始读取文件: {input_file}")
        
        # 由于文件很大，使用分块读取以节省内存
        chunk_size = 10000
        unique_gene_ids = set()
        total_rows = 0
        
        # 分块读取文件
        for chunk in pd.read_csv(input_file, chunksize=chunk_size):
            # 提取GENE ID列的值并添加到集合中
            gene_ids_in_chunk = chunk['GENE ID'].dropna().astype(str)
            unique_gene_ids.update(gene_ids_in_chunk)
            total_rows += len(chunk)
            
            logging.info(f"已处理 {total_rows} 行，当前唯一基因ID数量: {len(unique_gene_ids)}")
        
        logging.info(f"文件读取完成！总行数: {total_rows}")
        logging.info(f"唯一基因ID总数: {len(unique_gene_ids)}")
        
        # 将唯一值转换为DataFrame并排序
        unique_df = pd.DataFrame({
            'ensembl_gene_id': sorted(list(unique_gene_ids))
        })
        
        # 保存到CSV文件
        unique_df.to_csv(output_file, index=False)
        logging.info(f"唯一基因ID已保存到: {output_file}")
        
        # 显示统计信息
        logging.info("统计信息:")
        logging.info(f"  原始数据行数: {total_rows}")
        logging.info(f"  唯一基因ID数量: {len(unique_gene_ids)}")
        logging.info(f"  重复率: {((total_rows - len(unique_gene_ids)) / total_rows * 100):.2f}%")
        
        # 显示前10个基因ID作为示例
        logging.info("前10个基因ID示例:")
        for i, gene_id in enumerate(unique_df['ensembl_gene_id'].head(10)):
            logging.info(f"  {i+1}. {gene_id}")
        
        return True
        
    except Exception as e:
        logging.error(f"处理文件时出错: {e}")
        return False

def main():
    """主函数"""
    input_file = "translation_indices_results_grouped_filtered_processed.csv"
    output_file = "unique_ensembl_gene_ids.csv"
    
    # 检查输入文件是否存在
    import os
    if not os.path.exists(input_file):
        logging.error(f"输入文件不存在: {input_file}")
        return
    
    # 提取唯一基因ID
    success = extract_unique_gene_ids(input_file, output_file)
    
    if success:
        logging.info("基因ID提取任务完成！")
    else:
        logging.error("基因ID提取任务失败！")

if __name__ == "__main__":
    main() 