#!/usr/bin/env python3
import pandas as pd

print("正在比较两个文件的基因对应关系...")

# 读取两个文件
print("读取 gene_count_by_project_results_with_chromosome.csv...")
gene_count_df = pd.read_csv('gene/gene_count_by_project_results_with_chromosome.csv')
print(f"gene_count文件有 {len(gene_count_df)} 行")

print("读取 unique_genes.csv...")
unique_genes_df = pd.read_csv('gene/unique_genes.csv')
print(f"unique_genes文件有 {len(unique_genes_df)} 行")

print("\n文件列名:")
print(f"gene_count列名: {list(gene_count_df.columns)}")
print(f"unique_genes列名: {list(unique_genes_df.columns)}")

# 从gene_count文件中提取唯一的geneId和geneSymbol对应关系
print("\n提取gene_count文件中的唯一基因映射...")
gene_count_mapping = gene_count_df[['geneId', 'geneSymbol']].drop_duplicates()
print(f"gene_count文件中有 {len(gene_count_mapping)} 个唯一的基因映射")

# 从unique_genes文件中提取映射关系
print("unique_genes文件中的基因映射...")
unique_genes_mapping = unique_genes_df[['ensembl_gene_id', 'external_gene_name']].copy()
print(f"unique_genes文件中有 {len(unique_genes_mapping)} 个基因映射")

# 重命名列以便比较
gene_count_mapping = gene_count_mapping.rename(columns={'geneId': 'gene_id', 'geneSymbol': 'gene_symbol'})
unique_genes_mapping = unique_genes_mapping.rename(columns={'ensembl_gene_id': 'gene_id', 'external_gene_name': 'gene_symbol'})

print("\n比较两个映射关系...")

# 检查gene_count中的基因ID是否都在unique_genes中
gene_count_ids = set(gene_count_mapping['gene_id'])
unique_genes_ids = set(unique_genes_mapping['gene_id'])

print(f"gene_count中的基因ID数量: {len(gene_count_ids)}")
print(f"unique_genes中的基因ID数量: {len(unique_genes_ids)}")

# 找出差异
only_in_gene_count = gene_count_ids - unique_genes_ids
only_in_unique_genes = unique_genes_ids - gene_count_ids
common_ids = gene_count_ids & unique_genes_ids

print(f"\n只在gene_count中的基因ID: {len(only_in_gene_count)}")
print(f"只在unique_genes中的基因ID: {len(only_in_unique_genes)}")
print(f"两个文件共同的基因ID: {len(common_ids)}")

if only_in_gene_count:
    print(f"\n只在gene_count中的前10个基因ID: {list(only_in_gene_count)[:10]}")

if only_in_unique_genes:
    print(f"\n只在unique_genes中的前10个基因ID: {list(only_in_unique_genes)[:10]}")

# 对于共同的基因ID，检查基因符号是否一致
print("\n检查共同基因ID的符号是否一致...")
conflicts = []

for gene_id in list(common_ids)[:100]:  # 检查前100个避免输出过多
    gene_count_symbol = gene_count_mapping[gene_count_mapping['gene_id'] == gene_id]['gene_symbol'].iloc[0]
    unique_genes_symbol = unique_genes_mapping[unique_genes_mapping['gene_id'] == gene_id]['gene_symbol'].iloc[0]
    
    if gene_count_symbol != unique_genes_symbol:
        conflicts.append({
            'gene_id': gene_id,
            'gene_count_symbol': gene_count_symbol,
            'unique_genes_symbol': unique_genes_symbol
        })

print(f"发现 {len(conflicts)} 个基因符号不一致的情况")

if conflicts:
    print("\n前10个冲突示例:")
    for i, conflict in enumerate(conflicts[:10]):
        print(f"{i+1}. {conflict['gene_id']}: gene_count='{conflict['gene_count_symbol']}' vs unique_genes='{conflict['unique_genes_symbol']}'")

# 显示一些统计信息
print(f"\n总结:")
print(f"- gene_count文件中的基因映射: {len(gene_count_mapping)}")
print(f"- unique_genes文件中的基因映射: {len(unique_genes_mapping)}")
print(f"- 覆盖率: {len(common_ids)}/{len(gene_count_ids)} = {len(common_ids)/len(gene_count_ids)*100:.2f}%")
print(f"- 基因符号冲突: {len(conflicts)}")