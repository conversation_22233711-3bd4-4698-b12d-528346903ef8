import pandas as pd
import requests
import time
from bs4 import BeautifulSoup
from tqdm import tqdm

def fetch_pubmed_citation(pmid):
    """
    Fetch citation information from PubMed web page for a given PMID
    """
    url = f"https://pubmed.ncbi.nlm.nih.gov/{pmid}/"
    
    try:
        # Make the request with a small delay to avoid hitting rate limits
        response = requests.get(url)
        
        if response.status_code == 200:
            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find the article citation div
            citation_div = soup.find('div', class_='article-citation')
            
            if citation_div:
                # Extract both HTML and text content
                citation_html = str(citation_div)
                citation_text = citation_div.get_text(strip=True, separator=' ')
                
                return {
                    'html': citation_html,
                    'text': citation_text
                }
            else:
                print(f"Citation div not found for PMID {pmid}")
                return None
        else:
            print(f"Error fetching PMID {pmid}: Status code {response.status_code}")
            return None
    except Exception as e:
        print(f"Exception when fetching PMID {pmid}: {str(e)}")
        return None

def process_csv_file(input_file, output_file):
    """
    Process the CSV file, fetch PubMed citation for each PMID, and save to a new file
    """
    # Read the CSV file
    df = pd.read_csv(input_file)
    
    # Create new columns for PubMed citation
    df['pubmed_citation_text'] = None
    df['pubmed_citation_html'] = None
    
    # Process each row
    for index, row in tqdm(df.iterrows(), total=len(df), desc="Fetching PubMed citations"):
        # Extract PMID from the first column if it's a valid number
        try:
            pmid = str(row.iloc[0]).strip()
            if pmid.isdigit():
                # Fetch citation from PubMed
                citation_data = fetch_pubmed_citation(pmid)
                
                # Store the result
                if citation_data:
                    df.at[index, 'pubmed_citation_text'] = citation_data['text']
                    df.at[index, 'pubmed_citation_html'] = citation_data['html']
                
                # Add a small delay to avoid hitting rate limits
                time.sleep(1)  # Increased delay for web scraping
        except Exception as e:
            print(f"Error processing row {index}: {str(e)}")
    
    # Save the updated dataframe to a new CSV file
    df.to_csv(output_file, index=False)
    print(f"Data saved to {output_file}")

if __name__ == "__main__":
    input_file = "Browse-Publication/paper_info.csv"
    output_file = "Browse-Publication/paper_info_with_citation.csv"
    
    process_csv_file(input_file, output_file) 