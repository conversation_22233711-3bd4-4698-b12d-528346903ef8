#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理 ensembl_gene_id_with_full_info_split_2.csv 文件：
1. 根据 unique_gene_id_symbol_mapping.csv 文件过滤不存在的基因ID
2. 检查并更新不匹配的基因符号
"""

import pandas as pd
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('process_ensembl_gene_mapping.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

ENSEMBL_FILE = 'ensembl_gene_id_with_full_info_split_2.csv'
MAPPING_FILE = 'unique_gene_id_symbol_mapping copy.csv'
OUTPUT_FILE = 'ensembl_gene_id_with_full_info_split_2_processed.csv'

def process_ensembl_gene_mapping():
    """处理Ensembl基因映射文件"""
    try:
        # 读取映射文件
        logging.info(f'读取映射文件: {MAPPING_FILE}')
        mapping_df = pd.read_csv(MAPPING_FILE)
        logging.info(f'映射文件共 {len(mapping_df)} 行')
        
        # 创建基因ID到基因符号的映射字典
        gene_id_to_symbol = {}
        for _, row in mapping_df.iterrows():
            gene_id = str(row['GENE ID']).strip()
            gene_symbol = str(row['GENE symbol']).strip()
            if gene_id and gene_symbol and gene_symbol != 'nan':
                gene_id_to_symbol[gene_id] = gene_symbol
        
        logging.info(f'创建了 {len(gene_id_to_symbol)} 个基因ID到符号的映射')
        
        # 读取Ensembl文件
        logging.info(f'读取Ensembl文件: {ENSEMBL_FILE}')
        ensembl_df = pd.read_csv(ENSEMBL_FILE)
        logging.info(f'Ensembl文件共 {len(ensembl_df)} 行')
        
        # 统计变量
        original_count = len(ensembl_df)
        deleted_count = 0
        symbol_mismatch_count = 0
        symbol_updated_count = 0
        
        # 1. 过滤不存在于映射文件中的基因ID
        logging.info('开始过滤不存在的基因ID...')
        
        # 检查哪些基因ID存在于映射文件中
        ensembl_df['exists_in_mapping'] = ensembl_df['ensembl_gene_id'].astype(str).isin(gene_id_to_symbol.keys())
        
        # 统计要删除的行数
        deleted_count = (~ensembl_df['exists_in_mapping']).sum()
        logging.info(f'将删除 {deleted_count} 行（不存在于映射文件中的基因ID）')
        
        # 显示一些要删除的基因ID样例
        if deleted_count > 0:
            to_delete = ensembl_df[~ensembl_df['exists_in_mapping']]['ensembl_gene_id'].head(10)
            logging.info('要删除的基因ID样例（前10个）：')
            for gene_id in to_delete:
                logging.info(f'  {gene_id}')
        
        # 过滤掉不存在的基因ID
        filtered_df = ensembl_df[ensembl_df['exists_in_mapping']].copy()
        filtered_df = filtered_df.drop('exists_in_mapping', axis=1)
        
        logging.info(f'过滤后剩余 {len(filtered_df)} 行')
        
        # 2. 检查并更新基因符号
        logging.info('开始检查基因符号匹配情况...')
        
        mismatch_examples = []
        
        for index, row in filtered_df.iterrows():
            ensembl_gene_id = str(row['ensembl_gene_id']).strip()
            current_symbol = str(row['GENE symbol']).strip()
            
            if ensembl_gene_id in gene_id_to_symbol:
                mapping_symbol = gene_id_to_symbol[ensembl_gene_id]
                
                # 检查符号是否匹配
                if current_symbol != mapping_symbol:
                    symbol_mismatch_count += 1
                    
                    # 记录前10个不匹配的样例
                    if len(mismatch_examples) < 10:
                        mismatch_examples.append({
                            'gene_id': ensembl_gene_id,
                            'original_symbol': current_symbol,
                            'new_symbol': mapping_symbol
                        })
                    
                    # 更新基因符号
                    filtered_df.at[index, 'GENE symbol'] = mapping_symbol
                    symbol_updated_count += 1
        
        # 输出统计信息
        logging.info('=' * 60)
        logging.info('处理结果统计：')
        logging.info(f'原始记录数: {original_count:,}')
        logging.info(f'删除的记录数: {deleted_count:,}')
        logging.info(f'保留的记录数: {len(filtered_df):,}')
        logging.info(f'基因符号不匹配的数量: {symbol_mismatch_count:,}')
        logging.info(f'已更新的基因符号数量: {symbol_updated_count:,}')
        logging.info(f'删除比例: {(deleted_count / original_count) * 100:.2f}%')
        logging.info(f'符号不匹配比例: {(symbol_mismatch_count / len(filtered_df)) * 100:.2f}%')
        
        # 显示不匹配的样例
        if mismatch_examples:
            logging.info('\n基因符号不匹配样例：')
            for example in mismatch_examples:
                logging.info(f'  {example["gene_id"]}: "{example["original_symbol"]}" -> "{example["new_symbol"]}"')
            
            if symbol_mismatch_count > 10:
                logging.info(f'  ... 还有 {symbol_mismatch_count - 10} 个不匹配的记录')
        
        # 保存处理后的文件
        logging.info(f'保存处理后的文件到 {OUTPUT_FILE}...')
        filtered_df.to_csv(OUTPUT_FILE, index=False)
        
        # 验证保存的文件
        logging.info('验证保存的文件...')
        verify_df = pd.read_csv(OUTPUT_FILE)
        logging.info(f'验证：保存的文件有 {len(verify_df)} 行')
        
        logging.info('=' * 60)
        logging.info('处理完成！')
        logging.info(f'输出文件: {OUTPUT_FILE}')
        logging.info('=' * 60)
        
        return True
        
    except Exception as e:
        logging.error(f'处理过程中发生错误: {str(e)}')
        return False

if __name__ == '__main__':
    print('开始处理Ensembl基因映射文件...')
    success = process_ensembl_gene_mapping()
    if success:
        print('处理完成！')
    else:
        print('处理失败，请查看日志文件。') 