import pandas as pd
import json
from collections import OrderedDict

def clean_value(value):
    """清理值，将 NaN、'NA'、'nan' 统一为 null"""
    if pd.isna(value) or str(value).strip().upper() in {'NA', 'NAN', ''}:
        return None
    return str(value).strip()

def generate_mappings():
    """处理 projectInfo.csv 文件，生成三个映射字典"""
    
    # 读取CSV文件
    df = pd.read_csv('go_kegg/projectInfo.csv', encoding='utf-8')
    print(f"读取到 {len(df)} 条记录")
    
    # 初始化三个映射字典（使用 OrderedDict 保持顺序）
    tissue_mappings = OrderedDict()
    cell_line_mappings = OrderedDict()
    disease_mappings = OrderedDict()
    
    # 遍历数据生成映射关系
    for index, row in df.iterrows():
        # 处理 tissueOrCellType 映射
        original_tissue = clean_value(row.get('tissueOrCellType'))
        updated_tissue = clean_value(row.get('tissueOrCellType_update'))
        
        # 只有当原始值和更新值不同时才记录映射关系
        if original_tissue != updated_tissue:
            # 使用字符串形式存储，便于JSON序列化
            key = str(original_tissue) if original_tissue is not None else "null"
            value = str(updated_tissue) if updated_tissue is not None else "null"
            tissue_mappings[key] = value
        
        # 处理 cellLine 映射
        original_cell = clean_value(row.get('cellLine'))
        updated_cell = clean_value(row.get('cellLine_update'))
        
        if original_cell != updated_cell:
            key = str(original_cell) if original_cell is not None else "null"
            value = str(updated_cell) if updated_cell is not None else "null"
            cell_line_mappings[key] = value
        
        # 处理 disease 映射
        original_disease = clean_value(row.get('disease'))
        updated_disease = clean_value(row.get('disease_update'))
        
        if original_disease != updated_disease:
            key = str(original_disease) if original_disease is not None else "null"
            value = str(updated_disease) if updated_disease is not None else "null"
            disease_mappings[key] = value
    
    # 生成最终的JSON结构
    result = {
        "tissueOrCellType_mappings": dict(tissue_mappings),
        "cellLine_mappings": dict(cell_line_mappings),
        "disease_mappings": dict(disease_mappings)
    }
    
    # 统计信息
    print(f"tissueOrCellType 映射关系: {len(tissue_mappings)} 个")
    print(f"cellLine 映射关系: {len(cell_line_mappings)} 个")
    print(f"disease 映射关系: {len(disease_mappings)} 个")
    
    return result

def main():
    """主函数"""
    try:
        # 生成映射关系
        mappings = generate_mappings()
        
        # 保存为JSON文件
        output_file = 'project_field_mappings.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(mappings, f, ensure_ascii=False, indent=2)
        
        print(f"\n映射关系已保存到 {output_file}")
        
        # 显示每个部分的内容预览
        for section_name, section_data in mappings.items():
            print(f"\n{section_name}:")
            if section_data:
                # 显示前5个映射关系作为预览
                for i, (key, value) in enumerate(section_data.items()):
                    if i < 5:
                        print(f"  '{key}' -> '{value}'")
                    else:
                        print(f"  ... 还有 {len(section_data) - 5} 个映射关系")
                        break
            else:
                print("  无映射关系（所有值都相同）")
        
    except Exception as e:
        print(f"处理过程中发生错误: {e}")

if __name__ == '__main__':
    main() 