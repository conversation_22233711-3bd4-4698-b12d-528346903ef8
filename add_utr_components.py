#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向 translation_indices_results_grouped_filtered_processed_updated.csv 添加
"3'UTR component characteristics" 和 "5'UTR component characteristics" 两列。
数据来源: Transcript_id_with_UTR_info_and_genes.csv 中匹配 Transcript == transcript_id。
"""

import pandas as pd
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('add_utr_components.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

TRANSLATION_FILE = 'translation_indices_results_grouped_filtered_processed_updated.csv'
UTR_FILE = 'Transcript_id_with_UTR_info_and_genes_cleaned.csv'
OUTPUT_FILE = 'translation_indices_results_grouped_filtered_processed_final.csv'


def add_utr_components():
    try:
        logging.info('读取 UTR 信息文件...')
        utr_df = pd.read_csv(UTR_FILE)
        logging.info(f'UTR 文件读取完成，共 {len(utr_df)} 行')

        # 选择需要的列，并去除重复 transcript
        utr_subset = utr_df[['Transcript', "3'UTR component characteristics", "5'UTR component characteristics"]].copy()
        utr_subset = utr_subset.drop_duplicates(subset=['Transcript'])

        # 将列名重命名，方便 merge
        utr_subset = utr_subset.rename(columns={
            'Transcript': 'transcript_id',
            "3'UTR component characteristics": "3'UTR component characteristics",
            "5'UTR component characteristics": "5'UTR component characteristics"
        })

        logging.info('读取翻译指数文件...')
        translation_df = pd.read_csv(TRANSLATION_FILE)
        logging.info(f'翻译指数文件读取完成，共 {len(translation_df)} 行')

        # 合并
        logging.info('开始合并数据...')
        merged_df = translation_df.merge(
            utr_subset,
            on='transcript_id',
            how='left'
        )

        # 填充缺失值为 NA
        merged_df["3'UTR component characteristics"] = merged_df["3'UTR component characteristics"].fillna('NA')
        merged_df["5'UTR component characteristics"] = merged_df["5'UTR component characteristics"].fillna('NA')

        logging.info('保存结果...')
        merged_df.to_csv(OUTPUT_FILE, index=False)
        logging.info(f'已保存到 {OUTPUT_FILE}')
        return True

    except Exception as e:
        logging.error(f'处理过程中出现错误: {str(e)}')
        return False


if __name__ == '__main__':
    print('开始添加 UTR 组件信息...')
    success = add_utr_components()
    if success:
        print('添加完成！')
    else:
        print('添加失败，请查看日志文件。') 