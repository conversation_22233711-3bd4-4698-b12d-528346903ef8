2025-06-23 17:13:55,122 - INFO - === tbkb 数据库备份导入工具 ===
2025-06-23 17:13:55,125 - ERROR - 未找到 MySQL 客户端，请先安装 MySQL
2025-06-23 17:14:35,194 - INFO - === tbkb 数据库备份导入工具 ===
2025-06-23 17:14:35,196 - ERROR - 未找到 MySQL 客户端，请先安装 MySQL
2025-06-23 17:17:10,737 - INFO - === tbkb 远程数据库备份导入工具 ===
2025-06-23 17:17:10,737 - INFO - 测试数据库连接...
2025-06-23 17:17:10,831 - INFO - ✅ 数据库连接测试成功!
2025-06-23 17:17:10,832 - INFO - 连接到远程数据库...
2025-06-23 17:17:10,832 - INFO - 目标数据库: 192.168.129.38:3306/tbkb
2025-06-23 17:17:10,884 - INFO - ✅ 数据库连接成功!
2025-06-23 17:17:10,885 - INFO - 读取 SQL 备份文件...
2025-06-23 17:17:10,914 - ERROR - ❌ 导入过程中发生错误: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte
2025-06-23 17:17:10,940 - INFO - 数据库连接已关闭
2025-06-23 17:17:10,942 - ERROR - 💥 数据库导入失败!
2025-06-23 17:18:11,559 - INFO - === tbkb 远程数据库备份导入工具 ===
2025-06-23 17:18:11,559 - INFO - 测试数据库连接...
2025-06-23 17:18:13,389 - INFO - ✅ 数据库连接测试成功!
2025-06-23 17:18:13,395 - INFO - 连接到远程数据库...
2025-06-23 17:18:13,395 - INFO - 目标数据库: 192.168.129.38:3306/tbkb
2025-06-23 17:18:15,027 - INFO - ✅ 数据库连接成功!
2025-06-23 17:18:15,027 - INFO - 读取 SQL 备份文件...
2025-06-23 17:18:15,082 - INFO - ✅ 使用 utf-16 编码成功读取文件
2025-06-23 17:18:15,082 - INFO - 解析 SQL 语句...
2025-06-23 17:18:16,766 - INFO - 共找到 4284 条 SQL 语句
2025-06-23 17:18:16,766 - INFO - 开始执行 SQL 语句...
2025-06-23 17:18:18,087 - WARNING - 语句 6 执行失败: 1824 (HY000): Failed to open the referenced table 'auth_permission'
2025-06-23 17:18:18,087 - WARNING - 失败的语句: CREATE TABLE `auth_group_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_id` int NOT ...
2025-06-23 17:18:18,224 - WARNING - 语句 7 执行失败: 1146 (42S02): Table 'tbkb.auth_group_permissions' doesn't exist
2025-06-23 17:18:18,224 - WARNING - 失败的语句: LOCK TABLES `auth_group_permissions` WRITE...
2025-06-23 17:18:18,698 - WARNING - 语句 10 执行失败: 1824 (HY000): Failed to open the referenced table 'django_content_type'
2025-06-23 17:18:18,698 - WARNING - 失败的语句: CREATE TABLE `auth_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,...
2025-06-23 17:18:18,834 - WARNING - 语句 11 执行失败: 1146 (42S02): Table 'tbkb.auth_permission' doesn't exist
2025-06-23 17:18:18,834 - WARNING - 失败的语句: LOCK TABLES `auth_permission` WRITE...
2025-06-23 17:18:18,974 - WARNING - 语句 12 执行失败: 1146 (42S02): Table 'tbkb.auth_permission' doesn't exist
2025-06-23 17:18:18,974 - WARNING - 失败的语句: INSERT INTO `auth_permission` VALUES (1,'Can add log entry',1,'add_logentry'),(2,'Can change log ent...
2025-06-23 17:18:20,966 - WARNING - 语句 24 执行失败: 1824 (HY000): Failed to open the referenced table 'auth_permission'
2025-06-23 17:18:20,966 - WARNING - 失败的语句: CREATE TABLE `auth_user_user_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` int N...
2025-06-23 17:18:21,113 - WARNING - 语句 25 执行失败: 1146 (42S02): Table 'tbkb.auth_user_user_permissions' doesn't exist
2025-06-23 17:18:21,113 - WARNING - 失败的语句: LOCK TABLES `auth_user_user_permissions` WRITE...
2025-06-23 17:18:21,603 - WARNING - 语句 28 执行失败: 1824 (HY000): Failed to open the referenced table 'django_content_type'
2025-06-23 17:18:21,603 - WARNING - 失败的语句: CREATE TABLE `django_admin_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `action_time` datetime(6) NO...
2025-06-23 17:18:21,996 - WARNING - 语句 29 执行失败: 1146 (42S02): Table 'tbkb.django_admin_log' doesn't exist
2025-06-23 17:18:21,997 - WARNING - 失败的语句: LOCK TABLES `django_admin_log` WRITE...
2025-06-23 17:18:23,061 - WARNING - 语句 30 执行失败: 1146 (42S02): Table 'tbkb.django_admin_log' doesn't exist
2025-06-23 17:18:23,061 - WARNING - 失败的语句: INSERT INTO `django_admin_log` VALUES (1,'2025-04-04 08:24:08.737607','8','Rifampin',1,'new through ...
2025-06-23 17:40:32,996 - INFO - === tbkb 远程数据库备份导入工具 ===
2025-06-23 17:40:32,996 - INFO - 测试数据库连接...
2025-06-23 17:40:33,147 - INFO - ✅ 数据库连接测试成功!
2025-06-23 17:40:33,147 - INFO - 连接到远程数据库...
2025-06-23 17:40:33,147 - INFO - 目标数据库: 192.168.129.38:3306/tbkb
2025-06-23 17:40:33,209 - INFO - ✅ 数据库连接成功!
2025-06-23 17:40:33,210 - INFO - 读取 SQL 备份文件...
2025-06-23 17:40:33,297 - INFO - ✅ 使用 utf-16 编码成功读取文件
2025-06-23 17:40:33,297 - INFO - 解析 SQL 语句...
2025-06-23 17:40:36,009 - INFO - 共找到 4284 条 SQL 语句
2025-06-23 17:40:36,009 - INFO - 开始执行 SQL 语句...
2025-06-23 17:40:36,018 - WARNING - 语句 1 执行失败: 3730 (HY000): Cannot drop table 'auth_group' referenced by a foreign key constraint 'auth_group_permissions_group_id_b120cbf9_fk_auth_group_id' on table 'auth_group_permissions'.
2025-06-23 17:40:36,018 - WARNING - 失败的语句: DROP TABLE IF EXISTS `auth_group`...
2025-06-23 17:40:36,029 - WARNING - 语句 2 执行失败: 1050 (42S01): Table 'auth_group' already exists
2025-06-23 17:40:36,030 - WARNING - 失败的语句: CREATE TABLE `auth_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(150) NOT NULL,
  PR...
2025-06-23 17:40:36,114 - WARNING - 语句 9 执行失败: 3730 (HY000): Cannot drop table 'auth_permission' referenced by a foreign key constraint 'auth_user_user_permi_permission_id_1fbb5f2c_fk_auth_perm' on table 'auth_user_user_permissions'.
2025-06-23 17:40:36,114 - WARNING - 失败的语句: DROP TABLE IF EXISTS `auth_permission`...
2025-06-23 17:40:36,127 - WARNING - 语句 10 执行失败: 1050 (42S01): Table 'auth_permission' already exists
2025-06-23 17:40:36,127 - WARNING - 失败的语句: CREATE TABLE `auth_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,...
2025-06-23 17:40:36,150 - WARNING - 语句 12 执行失败: 1062 (23000): Duplicate entry '1' for key 'auth_permission.PRIMARY'
2025-06-23 17:40:36,150 - WARNING - 失败的语句: INSERT INTO `auth_permission` VALUES (1,'Can add log entry',1,'add_logentry'),(2,'Can change log ent...
2025-06-23 17:40:36,163 - WARNING - 语句 14 执行失败: 3730 (HY000): Cannot drop table 'auth_user' referenced by a foreign key constraint 'auth_user_groups_user_id_6a12ed8b_fk_auth_user_id' on table 'auth_user_groups'.
2025-06-23 17:40:36,164 - WARNING - 失败的语句: DROP TABLE IF EXISTS `auth_user`...
2025-06-23 17:40:36,174 - WARNING - 语句 15 执行失败: 1050 (42S01): Table 'auth_user' already exists
2025-06-23 17:40:36,174 - WARNING - 失败的语句: CREATE TABLE `auth_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `password` varchar(128) NOT NULL,
 ...
2025-06-23 17:40:36,832 - WARNING - 语句 32 执行失败: 3730 (HY000): Cannot drop table 'django_content_type' referenced by a foreign key constraint 'auth_permission_content_type_id_2f476e4b_fk_django_co' on table 'auth_permission'.
2025-06-23 17:40:36,832 - WARNING - 失败的语句: DROP TABLE IF EXISTS `django_content_type`...
2025-06-23 17:40:36,838 - WARNING - 语句 33 执行失败: 1050 (42S01): Table 'django_content_type' already exists
2025-06-23 17:40:36,839 - WARNING - 失败的语句: CREATE TABLE `django_content_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `app_label` varchar(100) ...
2025-06-23 17:40:36,858 - WARNING - 语句 35 执行失败: 1062 (23000): Duplicate entry '1' for key 'django_content_type.PRIMARY'
2025-06-23 17:40:36,859 - WARNING - 失败的语句: INSERT INTO `django_content_type` VALUES (1,'admin','logentry'),(3,'auth','group'),(2,'auth','permis...
2025-06-23 17:41:15,190 - INFO - ✅ SQL 导入完成!
2025-06-23 17:41:15,192 - INFO - 总语句数: 4284
2025-06-23 17:41:15,192 - INFO - 成功执行: 81
2025-06-23 17:41:15,192 - INFO - 失败数量: 4203
2025-06-23 17:41:15,192 - WARNING - ⚠️ 有 4203 条语句执行失败，请检查日志
2025-06-23 17:41:15,206 - INFO - 数据库连接已关闭
2025-06-23 17:41:15,215 - INFO - 🎉 数据库导入完成!
