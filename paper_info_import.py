import pandas as pd
import mysql.connector
import json

# 数据库配置信息
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306  # 端口号设置为整型
}

def main():
    # 1. 读取 CSV 文件，使用更严格的参数
    # 使用quoting参数确保引号内的内容被正确解析，skipfooter跳过文件末尾可能的空行
    df = pd.read_csv('Browse-Publication/paper_info_with_citation.csv', 
                     quoting=1,  # QUOTE_ALL模式
                     skipinitialspace=True,
                     skip_blank_lines=True)
    
    # 检查并删除任何空行或PMID为NaN的行
    df = df.dropna(subset=['PMID'])
    
    print(f"读取到 {len(df)} 行有效数据")
    
    # 2. 连接 MySQL 数据库
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()

    # 3. 检查表是否存在，如果存在则删除
    cursor.execute("SHOW TABLES LIKE 'paperInfo'")
    if cursor.fetchone():
        print("删除已存在的表: paperInfo")
        cursor.execute("DROP TABLE paperInfo")
        conn.commit()

    # 4. 创建数据表（使用驼峰命名法），添加pubmedCitation列
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS paperInfo (
        pmid VARCHAR(50) PRIMARY KEY,
        title VARCHAR(1024),
        doi VARCHAR(255),
        correspondingAuthorInformation TEXT,
        author TEXT,
        abstract TEXT,
        pubmedCitation TEXT,
        INDEX idx_title (title(255)),  # 限制索引长度为255
        INDEX idx_author (author(255)),
        INDEX idx_corresponding (correspondingAuthorInformation(255)),
        INDEX idx_abstract (abstract(255))
    )
    """
    cursor.execute(create_table_sql)

    # 5. 准备插入数据的 SQL 语句，包含pubmedCitation
    insert_sql = """
    INSERT INTO paperInfo (pmid, title, doi, correspondingAuthorInformation, author, abstract, pubmedCitation)
    VALUES (%s, %s, %s, %s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE
        title = VALUES(title),
        doi = VALUES(doi),
        correspondingAuthorInformation = VALUES(correspondingAuthorInformation),
        author = VALUES(author),
        abstract = VALUES(abstract),
        pubmedCitation = VALUES(pubmedCitation)
    """

    # 6. 遍历 DataFrame 并组织数据元组列表
    data_to_insert = []
    for index, row in df.iterrows():
        try:
            # 将PMID转为字符串
            pmid = str(row['PMID'])
            
            # 获取citation信息（使用pubmed_citation列名或最后一列）
            citation = None
            if 'pubmed_citation' in row and not pd.isna(row['pubmed_citation']):
                citation = row['pubmed_citation']
            # 如果没有明确的列名，尝试使用最后一列
            elif len(row) > 6:  # 检查是否有额外列
                citation = row.iloc[-1] if not pd.isna(row.iloc[-1]) else None
            
            # 构建数据元组
            data_tuple = (
                pmid,
                row['Title'] if not pd.isna(row['Title']) else None,
                row['Doi'] if not pd.isna(row['Doi']) else None,
                row['Corresponding Author Information'] if not pd.isna(row['Corresponding Author Information']) else None,
                row['Author'] if not pd.isna(row['Author']) else None,
                row['Abstract'] if not pd.isna(row['Abstract']) else None,
                citation
            )
            data_to_insert.append(data_tuple)
        except (ValueError, TypeError) as e:
            print(f"跳过行 {index}, 原因: {e}")
            continue
        except IndexError as e:
            print(f"跳过行 {index}, 索引错误: {e}")
            print(f"行数据: {row}")
            continue

    print(f"准备插入 {len(data_to_insert)} 条记录到数据库")
    
    # 7. 批量插入数据
    cursor.executemany(insert_sql, data_to_insert)
    conn.commit()

    # 8. 关闭连接
    cursor.close()
    conn.close()
    print("数据插入成功！")

if __name__ == '__main__':
    main()