#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为基因完整性分析结果添加基因符号信息脚本
从 unique_gene_id_symbol_mapping_with_details.csv 文件中查找基因符号并添加到合并的完整性分析结果中
"""

import pandas as pd
import sys
from datetime import datetime

def add_gene_symbols_to_completeness_analysis():
    """
    为基因完整性分析结果添加基因符号信息
    """
    print("=" * 80)
    print("为基因完整性分析结果添加基因符号信息")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 定义输入文件路径
    completeness_file = 'merged_gene_completeness_analysis_20250807_184659.csv'
    gene_info_file = 'gene_info/unique_gene_id_symbol_mapping_with_details.csv'
    
    print(f"\n输入文件:")
    print(f"1. 基因完整性分析文件: {completeness_file}")
    print(f"2. 基因信息文件: {gene_info_file}")
    
    # 读取基因完整性分析文件
    print(f"\n正在读取基因完整性分析文件...")
    try:
        df_completeness = pd.read_csv(completeness_file)
        print(f"成功读取完整性分析文件，共 {len(df_completeness):,} 行")
        print(f"列名: {df_completeness.columns.tolist()}")
        
        # 检查必要的列
        if 'geneId' not in df_completeness.columns:
            print("错误: 完整性分析文件中缺少 'geneId' 列")
            return
            
    except Exception as e:
        print(f"读取完整性分析文件失败: {e}")
        return
    
    # 读取基因信息文件
    print(f"\n正在读取基因信息文件...")
    try:
        df_gene_info = pd.read_csv(gene_info_file)
        print(f"成功读取基因信息文件，共 {len(df_gene_info):,} 行")
        print(f"列名: {df_gene_info.columns.tolist()}")
        
        # 检查必要的列
        if 'GENE_ID' not in df_gene_info.columns:
            print("错误: 基因信息文件中缺少 'GENE_ID' 列")
            return
        if 'GENE_symbol' not in df_gene_info.columns:
            print("错误: 基因信息文件中缺少 'GENE_symbol' 列")
            return
            
    except Exception as e:
        print(f"读取基因信息文件失败: {e}")
        return
    
    # 显示基本统计信息
    print(f"\n" + "=" * 60)
    print("文件统计信息")
    print("=" * 60)
    print(f"完整性分析文件包含的基因数: {len(df_completeness):,}")
    print(f"基因信息文件包含的基因数: {len(df_gene_info):,}")
    
    # 检查重复的基因ID
    completeness_duplicates = df_completeness['geneId'].duplicated().sum()
    gene_info_duplicates = df_gene_info['GENE_ID'].duplicated().sum()
    
    if completeness_duplicates > 0:
        print(f"警告: 完整性分析文件中发现 {completeness_duplicates} 个重复的geneId")
    if gene_info_duplicates > 0:
        print(f"警告: 基因信息文件中发现 {gene_info_duplicates} 个重复的GENE_ID")
        # 如果有重复，保留第一个
        df_gene_info = df_gene_info.drop_duplicates(subset=['GENE_ID'], keep='first')
        print(f"已去除重复，基因信息文件现在包含 {len(df_gene_info):,} 个唯一基因")
    
    # 分析两个数据集的交集
    gene_ids_completeness = set(df_completeness['geneId'])
    gene_ids_info = set(df_gene_info['GENE_ID'])
    
    common_genes = gene_ids_completeness & gene_ids_info
    only_in_completeness = gene_ids_completeness - gene_ids_info
    only_in_info = gene_ids_info - gene_ids_completeness
    
    print(f"\n基因ID交集分析:")
    print(f"两个文件共同的基因数: {len(common_genes):,}")
    print(f"仅在完整性分析文件中的基因数: {len(only_in_completeness):,}")
    print(f"仅在基因信息文件中的基因数: {len(only_in_info):,}")
    print(f"预期匹配成功率: {len(common_genes)/len(gene_ids_completeness)*100:.2f}%")
    
    # 显示一些未匹配的基因ID示例
    if only_in_completeness:
        print(f"\n仅在完整性分析文件中的基因ID示例 (前10个):")
        print(list(only_in_completeness)[:10])
    
    # 执行左连接合并
    print(f"\n正在执行左连接合并...")
    print("使用 geneId 与 GENE_ID 进行匹配...")
    
    # 只选择需要的列进行合并
    gene_info_subset = df_gene_info[['GENE_ID', 'GENE_symbol']].copy()
    
    # 执行左连接
    merged_df = pd.merge(
        df_completeness, 
        gene_info_subset, 
        left_on='geneId', 
        right_on='GENE_ID', 
        how='left'
    )
    
    # 删除多余的GENE_ID列
    if 'GENE_ID' in merged_df.columns:
        merged_df = merged_df.drop('GENE_ID', axis=1)
    
    print(f"合并完成！")
    print(f"合并后的数据包含 {len(merged_df):,} 行")
    print(f"合并后的列: {merged_df.columns.tolist()}")
    
    # 处理未匹配的基因符号
    missing_symbols = merged_df['GENE_symbol'].isna().sum()
    matched_symbols = len(merged_df) - missing_symbols
    
    print(f"\n" + "=" * 60)
    print("匹配结果统计")
    print("=" * 60)
    print(f"成功匹配的基因数: {matched_symbols:,}")
    print(f"未匹配的基因数: {missing_symbols:,}")
    print(f"匹配成功率: {matched_symbols/len(merged_df)*100:.2f}%")
    
    # 对于未匹配的基因，标记为 "Unknown"
    merged_df['GENE_symbol'] = merged_df['GENE_symbol'].fillna('Unknown')
    
    # 统计Unknown的数量
    unknown_count = (merged_df['GENE_symbol'] == 'Unknown').sum()
    print(f"标记为 'Unknown' 的基因数: {unknown_count:,}")
    
    # 重新排列列的顺序，将GENE_symbol放在geneId之后
    columns_order = ['geneId', 'GENE_symbol'] + [col for col in merged_df.columns if col not in ['geneId', 'GENE_symbol']]
    merged_df = merged_df[columns_order]
    
    # 确保按geneId字母顺序排序（保持原始排序）
    merged_df = merged_df.sort_values('geneId').reset_index(drop=True)
    
    # 显示前10行作为预览
    print(f"\n前10行合并结果预览:")
    print("-" * 120)
    print(merged_df.head(10).to_string(index=False))
    
    # 显示一些统计信息
    print(f"\n" + "=" * 60)
    print("基因符号统计")
    print("=" * 60)
    
    # 统计基因符号的分布
    symbol_counts = merged_df['GENE_symbol'].value_counts()
    print(f"唯一基因符号数: {len(symbol_counts):,}")
    print(f"最常见的基因符号 (前10个):")
    print(symbol_counts.head(10))
    
    if 'Unknown' in symbol_counts:
        print(f"\n'Unknown' 基因符号数量: {symbol_counts['Unknown']:,}")
    
    # 显示一些成功匹配的示例
    matched_examples = merged_df[merged_df['GENE_symbol'] != 'Unknown'].head(5)
    if len(matched_examples) > 0:
        print(f"\n成功匹配的基因示例:")
        print(matched_examples[['geneId', 'GENE_symbol']].to_string(index=False))
    
    # 显示一些未匹配的示例
    unmatched_examples = merged_df[merged_df['GENE_symbol'] == 'Unknown'].head(5)
    if len(unmatched_examples) > 0:
        print(f"\n未匹配的基因示例:")
        print(unmatched_examples[['geneId', 'GENE_symbol']].to_string(index=False))
    
    # 保存结果
    output_file = f"merged_gene_completeness_with_symbols_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    print(f"\n正在保存结果到: {output_file}")
    
    try:
        merged_df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"结果已成功保存到: {output_file}")
        print(f"输出文件包含 {len(merged_df):,} 行数据，{len(merged_df.columns)} 列")
        
        # 验证保存的文件
        print(f"\n验证保存的文件...")
        verification_df = pd.read_csv(output_file, nrows=3)
        print(f"验证成功！文件前3行:")
        print(verification_df.to_string(index=False))
        
        # 最终统计摘要
        print(f"\n" + "=" * 60)
        print("最终统计摘要")
        print("=" * 60)
        print(f"输入基因数: {len(df_completeness):,}")
        print(f"输出基因数: {len(merged_df):,}")
        print(f"成功匹配基因符号: {matched_symbols:,} ({matched_symbols/len(merged_df)*100:.2f}%)")
        print(f"未匹配基因符号: {missing_symbols:,} ({missing_symbols/len(merged_df)*100:.2f}%)")
        print(f"输出文件列数: {len(merged_df.columns)}")
        
    except Exception as e:
        print(f"保存文件失败: {e}")
        return
    
    print(f"\n处理完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

if __name__ == "__main__":
    add_gene_symbols_to_completeness_analysis()
