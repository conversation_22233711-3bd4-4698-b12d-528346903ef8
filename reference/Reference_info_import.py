import pandas as pd
import mysql.connector

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': '3306'
}

def create_reference_table(cursor):
    """创建参考文献信息表（使用驼峰命名法）"""
    # 检查表是否存在
    cursor.execute("SHOW TABLES LIKE 'referenceInfo'")
    table_exists = cursor.fetchone() is not None

    if table_exists:
        print("表 referenceInfo 已存在，正在删除...")
        cursor.execute("DROP TABLE referenceInfo")
        print("表 referenceInfo 删除成功！")

    # 创建新表
    create_table_sql = """
    CREATE TABLE referenceInfo (
        bioProjectId VARCHAR(100),
        geoAccession VARCHAR(100),
        reference TEXT,
        pubmedId VARCHAR(50),
        doi VARCHAR(255)
    )
    """
    cursor.execute(create_table_sql)
    print("参考文献信息表创建成功！")

def create_indexes(cursor):
    """为每一列创建普通索引"""
    indexes = [
        ('idx_bioProjectId', 'bioProjectId'),
        ('idx_geoAccession', 'geoAccession'),
        ('idx_reference', 'reference(255)'),  # TEXT字段索引需指定长度
        ('idx_pubmedId', 'pubmedId'),
        ('idx_doi', 'doi')
    ]
    for index_name, column in indexes:
        try:
            create_index_sql = f"CREATE INDEX {index_name} ON referenceInfo ({column})"
            cursor.execute(create_index_sql)
            print(f"创建索引 {index_name} 成功")
        except mysql.connector.Error as err:
            print(f"创建索引 {index_name} 失败: {err}")
    print("所有索引创建完成")

def import_reference_data(cursor, conn):
    """导入参考文献数据"""
    csv_path = 'Reference.csv'
    df = pd.read_csv(csv_path, dtype=str).fillna('')

    insert_sql = """
    INSERT INTO referenceInfo (bioProjectId, geoAccession, reference, pubmedId, doi)
    VALUES (%s, %s, %s, %s, %s)
    """

    # 清空所有现有的查询结果和状态
    try:
        cursor.fetchall()
    except:
        pass

    # 检查表的现有记录数
    cursor.execute("SELECT COUNT(*) FROM referenceInfo")
    initial_count = cursor.fetchone()[0]
    print(f"导入前表中有 {initial_count} 条记录")

    # 逐行插入数据
    successful = 0
    for i, row in df.iterrows():
        try:
            bio_project_id = row['BioProject ID'] if row['BioProject ID'] != 'NA' else None
            geo_accession = row['GEO_Accession'] if row['GEO_Accession'] != 'NA' else None
            reference = row['Reference'] if row['Reference'] != 'NA' else None
            pubmed_id = row['PubMed ID'] if row['PubMed ID'] != 'NA' else None
            doi = row['DOI'] if row['DOI'] != 'NA' else None

            cursor.execute(insert_sql, (bio_project_id, geo_accession, reference, pubmed_id, doi))
            conn.commit()
            successful += 1
        except mysql.connector.Error as err:
            print(f"无法导入第 {i+1} 行数据 (BioProject ID: {row['BioProject ID']}): {err}")
            conn.rollback()

    # 检查最终表中的记录数
    cursor.execute("SELECT COUNT(*) FROM referenceInfo")
    final_count = cursor.fetchone()[0]
    print(f"成功导入 {successful} 条参考文献数据")
    print(f"导入前表中有 {initial_count} 条记录，导入后有 {final_count} 条记录，净增加 {final_count - initial_count} 条")

def main():
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    create_reference_table(cursor)
    import_reference_data(cursor, conn)
    create_indexes(cursor)
    conn.commit()
    cursor.close()
    conn.close()
    print("参考文献数据导入和索引创建完成！")

if __name__ == '__main__':
    main() 