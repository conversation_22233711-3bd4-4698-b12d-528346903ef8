import pandas as pd
import mysql.connector
import csv
from datetime import datetime
import os

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306,
}

def main():
    # 连接 MySQL 数据库
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    # 处理 Browse_Sample.csv 文件
    create_sample_table(cursor)
    import_sample_data(cursor)
    create_indexes(cursor)
    
    # 提交更改并关闭连接
    conn.commit()
    cursor.close()
    conn.close()
    print("完成样本数据导入和索引创建！")

def create_sample_table(cursor):
    """创建样本信息表"""
    # 先删除旧表（如果存在）
    try:
        cursor.execute("DROP TABLE IF EXISTS sampleInfo")
        print("已删除旧的sampleInfo表")
    except mysql.connector.Error as err:
        print(f"删除旧表时出错: {err}")

    # 创建新表
    create_table_sql = """
    CREATE TABLE sampleInfo (
        sraAccession VARCHAR(50) PRIMARY KEY,
        datasetId VARCHAR(50),
        geoAccession VARCHAR(50),
        bioProjectId VARCHAR(50),
        bioSampleId VARCHAR(50),
        tissueCellType VARCHAR(255),
        cellLine VARCHAR(255),
        condition_ VARCHAR(255),
        diseaseCategory VARCHAR(255),
        dataType VARCHAR(255),
        platform VARCHAR(255),
        instrument VARCHAR(255),
        libraryLayout VARCHAR(50),
        detail VARCHAR(255),
        translatedTranscriptsNumber INT,
        translatedGenesNumber INT
    )
    """
    cursor.execute(create_table_sql)

    print("样本信息表创建成功！")

def import_sample_data(cursor):
    """导入样本数据"""
    # CSV文件路径
    csv_path = 'Sample.csv'

    # 准备SQL语句 - 使用ON DUPLICATE KEY UPDATE防止重复主键问题
    insert_sql = """
    INSERT INTO sampleInfo
    (sraAccession, datasetId, geoAccession, bioProjectId, bioSampleId,
     tissueCellType, cellLine, condition_, diseaseCategory,
     dataType, platform, instrument, libraryLayout, detail, translatedTranscriptsNumber, translatedGenesNumber)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE
        datasetId = VALUES(datasetId),
        geoAccession = VALUES(geoAccession),
        bioProjectId = VALUES(bioProjectId),
        bioSampleId = VALUES(bioSampleId),
        tissueCellType = VALUES(tissueCellType),
        cellLine = VALUES(cellLine),
        condition_ = VALUES(condition_),
        diseaseCategory = VALUES(diseaseCategory),
        dataType = VALUES(dataType),
        platform = VALUES(platform),
        instrument = VALUES(instrument),
        libraryLayout = VALUES(libraryLayout),
        detail = VALUES(detail),
        translatedTranscriptsNumber = VALUES(translatedTranscriptsNumber),
        translatedGenesNumber = VALUES(translatedGenesNumber)
    """
    
    # 读取CSV并处理数据
    samples = []
    row_count = 0
    valid_count = 0
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            row_count += 1
            
            # 跳过空行或SRA Accession为空的行
            if not row or not row.get('SRA Accession') or row.get('SRA Accession') == 'NA':
                print(f"跳过第 {row_count} 行: SRA Accession为空或NA")
                continue
                
            # 处理空值和特殊值
            # 处理TRANSLATED TRANSCRIPTS NUMBER为数值类型
            translated_transcripts = None
            if row.get('TRANSLATED TRANSCRIPTS NUMBER') and row['TRANSLATED TRANSCRIPTS NUMBER'] != 'NA':
                try:
                    translated_transcripts = int(row['TRANSLATED TRANSCRIPTS NUMBER'])
                except ValueError:
                    translated_transcripts = None
            
            # 处理TRANSLATED GENES NUMBER为数值类型
            translated_genes = None
            if row.get('TRANSLATED GENES NUMBER') and row['TRANSLATED GENES NUMBER'] != 'NA':
                try:
                    translated_genes = int(row['TRANSLATED GENES NUMBER'])
                except ValueError:
                    translated_genes = None

            sample_data = (
                row['SRA Accession'] if row['SRA Accession'] != 'NA' else None,
                row['Dataset ID'] if row['Dataset ID'] != 'NA' else None,
                row['GEO_Accession'] if row['GEO_Accession'] != 'NA' else None,
                row['BioProject ID'] if row['BioProject ID'] != 'NA' else None,
                row['BioSample ID'] if row['BioSample ID'] != 'NA' else None,
                row['Tissue/Cell Type'] if row['Tissue/Cell Type'] != 'NA' else None,
                row['Cell line'] if row['Cell line'] != 'NA' else None,
                row['Condition'] if row['Condition'] != 'NA' else None,
                row['Disease Category'] if row['Disease Category'] != 'NA' else None,
                row['Data Type'] if row['Data Type'] != 'NA' else None,
                row['Platform'] if row['Platform'] != 'NA' else None,
                row['Instrument'] if row['Instrument'] != 'NA' else None,
                row['LibraryLayout'] if row['LibraryLayout'] != 'NA' else None,
                row['Detail'] if row['Detail'] != 'NA' else None,
                translated_transcripts,
                translated_genes
            )
            samples.append(sample_data)
            valid_count += 1
    
    print(f"CSV文件共读取 {row_count} 行，有效数据 {valid_count} 行")
    
    # 批量插入数据
    try:
        cursor.executemany(insert_sql, samples)
        print(f"成功导入 {len(samples)} 条样本数据")
    except mysql.connector.Error as err:
        print(f"导入数据时出错: {err}")
        # 单条插入以确定哪些记录有问题
        successful = 0
        for i, sample_data in enumerate(samples):
            try:
                cursor.execute(insert_sql, sample_data)
                successful += 1
            except mysql.connector.Error as err:
                print(f"无法导入第 {i+1} 行数据: {err}")
        print(f"成功导入 {successful} / {len(samples)} 条记录")

def create_indexes(cursor):
    """为指定列创建索引，优化模糊匹配搜索"""

    # 为文本列创建FULLTEXT索引，支持模糊匹配（数值列不包含在FULLTEXT索引中）
    search_columns = [
        'sraAccession', 'datasetId', 'geoAccession', 'bioProjectId', 'bioSampleId',
        'tissueCellType', 'cellLine', 'condition_', 'diseaseCategory',
        'dataType', 'platform', 'instrument', 'libraryLayout', 'detail'
    ]

    # 创建联合FULLTEXT索引来支持在任何一列找到匹配项
    fulltext_index_sql = f"""
    CREATE FULLTEXT INDEX ft_sample_search
    ON sampleInfo ({', '.join(search_columns)})
    """
    try:
        cursor.execute(fulltext_index_sql)
        print("创建全文搜索索引成功，支持模糊匹配以下列：")
        for col in search_columns:
            print(f"- {col}")
    except mysql.connector.Error as err:
        if err.errno == 1061:  # Duplicate key name
            print("全文搜索索引已存在，跳过创建")
        else:
            print(f"创建全文搜索索引失败: {err}")

    # 为数值列创建普通索引
    try:
        cursor.execute("CREATE INDEX idx_translated_transcripts ON sampleInfo (translatedTranscriptsNumber)")
        print("为translatedTranscriptsNumber创建索引成功")
    except mysql.connector.Error as err:
        if err.errno == 1061:  # Duplicate key name
            print("translatedTranscriptsNumber索引已存在，跳过创建")
        else:
            print(f"创建translatedTranscriptsNumber索引失败: {err}")

    try:
        cursor.execute("CREATE INDEX idx_translated_genes ON sampleInfo (translatedGenesNumber)")
        print("为translatedGenesNumber创建索引成功")
    except mysql.connector.Error as err:
        if err.errno == 1061:  # Duplicate key name
            print("translatedGenesNumber索引已存在，跳过创建")
        else:
            print(f"创建translatedGenesNumber索引失败: {err}")

    # 主键索引已在表创建时添加，不需要再尝试添加
    print("sraAccession已设置为主键")
    
if __name__ == '__main__':
    main() 