import pandas as pd
import os

def process_na_values(input_file, output_file=None):
    """
    处理CSV文件中的NA值，将其替换为空值
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径，如果为None则覆盖原文件
    """
    try:
        # 读取CSV文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_csv(input_file, encoding='utf-8')
        
        print(f"原始文件包含 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
        
        # 统计NA值的数量
        na_count_before = (df == 'NA').sum().sum()
        print(f"处理前NA值总数: {na_count_before}")
        
        # 将所有"NA"字符串替换为空值（NaN）
        df_processed = df.replace('NA', pd.NA)
        
        # 统计处理后的空值数量
        na_count_after = df_processed.isna().sum().sum()
        print(f"处理后空值总数: {na_count_after}")
        
        # 确定输出文件路径
        if output_file is None:
            output_file = input_file
        
        # 保存处理后的文件
        # 使用na_rep=''参数确保空值在CSV中显示为空而不是NaN
        df_processed.to_csv(output_file, index=False, encoding='utf-8', na_rep='')
        
        print(f"文件已成功保存到: {output_file}")
        print(f"成功将 {na_count_before} 个NA值替换为空值")
        
        # 显示前几行数据作为预览
        print("\n处理后的前5行数据预览:")
        print(df_processed.head().to_string())
        
        return True
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return False
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return False

def main():
    """主函数"""
    input_file = 'Sample_processed.csv'
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return
    
    # 询问用户是否要创建备份
    create_backup = input("是否创建原文件备份？(y/n): ").lower().strip()
    
    if create_backup == 'y':
        backup_file = input_file.replace('.csv', '_backup.csv')
        try:
            df_backup = pd.read_csv(input_file, encoding='utf-8')
            df_backup.to_csv(backup_file, index=False, encoding='utf-8')
            print(f"备份文件已创建: {backup_file}")
        except Exception as e:
            print(f"创建备份时出错: {e}")
            return
    
    # 处理文件
    success = process_na_values(input_file)
    
    if success:
        print("\n✅ 文件处理完成！")
    else:
        print("\n❌ 文件处理失败！")

if __name__ == "__main__":
    main() 