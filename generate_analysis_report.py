#!/usr/bin/env python3
"""
生成基因翻译效率统计分析报告
"""

import csv
from collections import defaultdict, Counter

def read_results(filename):
    """读取分析结果"""
    results = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # 转换数值字段
                for field in ['p_t', 'fdr_t', 'p_wilcox', 'fdr_wilcox', 'p_ks', 'fdr_ks']:
                    try:
                        if row[field] and row[field] != '':
                            row[field] = float(row[field])
                        else:
                            row[field] = None
                    except:
                        row[field] = None
                
                results.append(row)
        
        print(f"成功读取 {len(results)} 个分析结果")
        return results
    
    except Exception as e:
        print(f"读取结果文件失败: {e}")
        return None

def generate_summary_report(results):
    """生成摘要报告"""
    print("\n=== 基因翻译效率统计分析报告 ===")
    
    # 基本统计
    total_tests = len(results)
    unique_genes = len(set(r['geneSymbol'] for r in results))
    unique_diseases = len(set(r['disease_category'] for r in results))
    unique_variables = len(set(r['variable'] for r in results))
    
    print(f"\n1. 基本统计信息:")
    print(f"   - 总测试数量: {total_tests:,}")
    print(f"   - 分析基因数: {unique_genes:,}")
    print(f"   - 疾病类型数: {unique_diseases}")
    print(f"   - 变量类型数: {unique_variables}")
    
    # 疾病分布
    disease_counts = Counter(r['disease_category'] for r in results)
    print(f"\n2. 疾病类型分布:")
    for disease, count in disease_counts.most_common():
        print(f"   - {disease}: {count:,} 个测试")
    
    # 变量分布
    variable_counts = Counter(r['variable'] for r in results)
    print(f"\n3. 变量类型分布:")
    for variable, count in variable_counts.items():
        print(f"   - {variable}: {count:,} 个测试")
    
    # 显著性结果统计
    print(f"\n4. 显著性结果统计 (p < 0.05):")
    
    for test_type in ['fdr_t', 'fdr_wilcox', 'fdr_ks']:
        test_name = {
            'fdr_t': 'T检验 (FDR校正)',
            'fdr_wilcox': 'Wilcoxon检验 (FDR校正)',
            'fdr_ks': 'KS检验 (FDR校正)'
        }[test_type]
        
        valid_tests = [r for r in results if r[test_type] is not None]
        significant = [r for r in valid_tests if r[test_type] < 0.05]
        
        if valid_tests:
            percentage = len(significant) / len(valid_tests) * 100
            print(f"   - {test_name}: {len(significant):,}/{len(valid_tests):,} ({percentage:.2f}%)")
        else:
            print(f"   - {test_name}: 无有效结果")
    
    # 表达变化方向统计
    direction_counts = Counter(r['direction'] for r in results)
    print(f"\n5. 表达变化方向:")
    for direction, count in direction_counts.items():
        percentage = count / total_tests * 100
        print(f"   - {direction}: {count:,} ({percentage:.2f}%)")

def find_top_significant_results(results, test_type='fdr_t', top_n=20):
    """找到最显著的结果"""
    print(f"\n6. 最显著的结果 (按{test_type}排序, 前{top_n}个):")
    
    # 过滤有效结果并排序
    valid_results = [r for r in results if r[test_type] is not None and r[test_type] < 0.05]
    valid_results.sort(key=lambda x: x[test_type])
    
    if not valid_results:
        print("   没有显著结果")
        return
    
    print(f"   {'基因符号':<15} {'疾病':<30} {'变量':<5} {test_type:<10} {'方向':<8}")
    print("   " + "-" * 80)
    
    for i, result in enumerate(valid_results[:top_n]):
        gene = result['geneSymbol'][:14]
        disease = result['disease_category'][:29]
        variable = result['variable']
        p_value = result[test_type]
        direction = result['direction']
        
        print(f"   {gene:<15} {disease:<30} {variable:<5} {p_value:<10.2e} {direction:<8}")

def analyze_by_disease(results):
    """按疾病分析"""
    print(f"\n7. 按疾病分析显著结果:")
    
    disease_stats = defaultdict(lambda: {'total': 0, 'significant_t': 0, 'significant_wilcox': 0, 'significant_ks': 0})
    
    for result in results:
        disease = result['disease_category']
        disease_stats[disease]['total'] += 1
        
        if result['fdr_t'] is not None and result['fdr_t'] < 0.05:
            disease_stats[disease]['significant_t'] += 1
        if result['fdr_wilcox'] is not None and result['fdr_wilcox'] < 0.05:
            disease_stats[disease]['significant_wilcox'] += 1
        if result['fdr_ks'] is not None and result['fdr_ks'] < 0.05:
            disease_stats[disease]['significant_ks'] += 1
    
    print(f"   {'疾病':<35} {'总测试':<8} {'T检验显著':<10} {'Wilcoxon显著':<12} {'KS显著':<8}")
    print("   " + "-" * 85)
    
    for disease, stats in sorted(disease_stats.items()):
        total = stats['total']
        sig_t = stats['significant_t']
        sig_w = stats['significant_wilcox']
        sig_k = stats['significant_ks']
        
        disease_short = disease[:34]
        print(f"   {disease_short:<35} {total:<8} {sig_t:<10} {sig_w:<12} {sig_k:<8}")

def analyze_by_gene(results, top_n=15):
    """按基因分析"""
    print(f"\n8. 显著结果最多的基因 (前{top_n}个):")
    
    gene_stats = defaultdict(lambda: {'total': 0, 'significant': 0})
    
    for result in results:
        gene = result['geneSymbol']
        gene_stats[gene]['total'] += 1
        
        if result['fdr_t'] is not None and result['fdr_t'] < 0.05:
            gene_stats[gene]['significant'] += 1
    
    # 按显著结果数量排序
    sorted_genes = sorted(gene_stats.items(), key=lambda x: x[1]['significant'], reverse=True)
    
    print(f"   {'基因符号':<15} {'显著结果':<8} {'总测试':<8} {'显著率':<8}")
    print("   " + "-" * 45)
    
    for gene, stats in sorted_genes[:top_n]:
        if stats['significant'] > 0:
            percentage = stats['significant'] / stats['total'] * 100
            print(f"   {gene:<15} {stats['significant']:<8} {stats['total']:<8} {percentage:<7.1f}%")

def save_summary_report(results, output_file):
    """保存摘要报告到文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            # 重定向print输出到文件
            import sys
            original_stdout = sys.stdout
            sys.stdout = f
            
            generate_summary_report(results)
            find_top_significant_results(results)
            analyze_by_disease(results)
            analyze_by_gene(results)
            
            # 恢复stdout
            sys.stdout = original_stdout
        
        print(f"\n摘要报告已保存到: {output_file}")
    
    except Exception as e:
        print(f"保存报告失败: {e}")

def main():
    """主函数"""
    results_file = "gene_statistical_analysis_results.csv"
    report_file = "gene_analysis_summary_report.txt"
    
    # 读取结果
    results = read_results(results_file)
    if results is None:
        return
    
    # 生成并显示报告
    generate_summary_report(results)
    find_top_significant_results(results)
    analyze_by_disease(results)
    analyze_by_gene(results)
    
    # 保存报告
    save_summary_report(results, report_file)

if __name__ == "__main__":
    main()
