#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析第二个文件多出的基因-项目组合的详细信息
"""

import pandas as pd

def analyze_extra_combinations():
    """
    分析多出的组合的详细信息
    """
    print("读取多出的组合数据...")
    
    try:
        df = pd.read_csv('gene_combinations_extra_in_second_file.csv')
        print(f"成功读取数据，共 {len(df)} 行")
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    print("\n=== 基本统计信息 ===")
    print(f"总共多出的组合数: {len(df)}")
    print(f"涉及的唯一基因数: {df['geneId'].nunique()}")
    print(f"涉及的唯一项目数: {df['projectId'].nunique()}")
    
    print("\n=== 涉及的基因列表 ===")
    gene_counts = df.groupby(['geneId', 'geneSymbol']).size().reset_index(name='count')
    gene_counts = gene_counts.sort_values('count', ascending=False)
    print(gene_counts.to_string(index=False))
    
    print("\n=== 每个基因涉及的项目数量 ===")
    gene_project_counts = df.groupby(['geneId', 'geneSymbol'])['projectId'].nunique().reset_index(name='project_count')
    gene_project_counts = gene_project_counts.sort_values('project_count', ascending=False)
    print(gene_project_counts.to_string(index=False))
    
    print("\n=== 项目分布统计 ===")
    project_counts = df['projectId'].value_counts().head(20)
    print("前20个项目的组合数量:")
    print(project_counts.to_string())
    
    # 保存详细的统计报告
    with open('extra_combinations_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write("第二个文件多出的基因-项目组合分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"总共多出的组合数: {len(df)}\n")
        f.write(f"涉及的唯一基因数: {df['geneId'].nunique()}\n")
        f.write(f"涉及的唯一项目数: {df['projectId'].nunique()}\n\n")
        
        f.write("涉及的基因列表:\n")
        f.write(gene_counts.to_string(index=False))
        f.write("\n\n")
        
        f.write("每个基因涉及的项目数量:\n")
        f.write(gene_project_counts.to_string(index=False))
        f.write("\n\n")
        
        f.write("项目分布统计 (前20个):\n")
        f.write(project_counts.to_string())
        f.write("\n")
    
    print("\n详细分析报告已保存到: extra_combinations_analysis_report.txt")

if __name__ == "__main__":
    analyze_extra_combinations()
