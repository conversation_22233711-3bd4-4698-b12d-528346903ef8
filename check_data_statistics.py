#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计 translation_indices_results_grouped_filtered_processed_final.csv 文件中
transcript_id、GENE ID、GENE symbol 三列的唯一值数量，并检查 GENE ID 列的空值情况。
"""

import pandas as pd
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('check_data_statistics.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

INPUT_FILE = 'translation_indices_results_grouped_filtered_processed_final.csv'

def check_statistics():
    """检查数据统计信息"""
    try:
        logging.info('读取文件...')
        df = pd.read_csv(INPUT_FILE)
        logging.info(f'文件总行数: {len(df)}')
        
        # 统计三列的唯一值数量
        logging.info('=' * 60)
        logging.info('唯一值统计：')
        
        transcript_unique = df['transcript_id'].nunique()
        gene_id_unique = df['GENE ID'].nunique()
        gene_symbol_unique = df['GENE symbol'].nunique()
        
        logging.info(f'transcript_id 唯一值数量: {transcript_unique:,}')
        logging.info(f'GENE ID 唯一值数量: {gene_id_unique:,}')
        logging.info(f'GENE symbol 唯一值数量: {gene_symbol_unique:,}')
        
        # 检查 GENE ID 列的空值情况
        logging.info('=' * 60)
        logging.info('GENE ID 列空值检查：')
        
        # 检查各种可能的空值情况
        gene_id_null = df['GENE ID'].isnull().sum()
        gene_id_empty_str = (df['GENE ID'] == '').sum()
        gene_id_na_str = (df['GENE ID'] == 'NA').sum()
        gene_id_nan_str = (df['GENE ID'] == 'nan').sum()
        
        logging.info(f'NULL/NaN 值数量: {gene_id_null:,}')
        logging.info(f'空字符串 ("") 数量: {gene_id_empty_str:,}')
        logging.info(f'"NA" 字符串数量: {gene_id_na_str:,}')
        logging.info(f'"nan" 字符串数量: {gene_id_nan_str:,}')
        
        total_empty = gene_id_null + gene_id_empty_str + gene_id_na_str + gene_id_nan_str
        logging.info(f'总空值数量: {total_empty:,}')
        logging.info(f'空值占比: {(total_empty / len(df)) * 100:.2f}%')
        
        # 如果有空值，显示一些样例
        if total_empty > 0:
            logging.info('\n空值样例（前10个）：')
            empty_mask = (df['GENE ID'].isnull()) | (df['GENE ID'] == '') | (df['GENE ID'] == 'NA') | (df['GENE ID'] == 'nan')
            empty_samples = df[empty_mask][['transcript_id', 'GENE ID', 'GENE symbol']].head(10)
            for idx, row in empty_samples.iterrows():
                logging.info(f'  {row["transcript_id"]} -> GENE ID: "{row["GENE ID"]}", GENE symbol: "{row["GENE symbol"]}"')
        
        # 检查其他两列的空值情况
        logging.info('=' * 60)
        logging.info('其他列空值检查：')
        
        transcript_null = df['transcript_id'].isnull().sum()
        gene_symbol_null = df['GENE symbol'].isnull().sum()
        gene_symbol_na = (df['GENE symbol'] == 'NA').sum()
        
        logging.info(f'transcript_id NULL 值数量: {transcript_null:,}')
        logging.info(f'GENE symbol NULL 值数量: {gene_symbol_null:,}')
        logging.info(f'GENE symbol "NA" 值数量: {gene_symbol_na:,}')
        
        # 重复值检查
        logging.info('=' * 60)
        logging.info('重复值检查：')
        
        transcript_duplicates = len(df) - transcript_unique
        logging.info(f'transcript_id 重复记录数: {transcript_duplicates:,}')
        
        # 检查一个转录本对应多个基因的情况
        transcript_gene_mapping = df.groupby('transcript_id')['GENE ID'].nunique()
        multi_gene_transcripts = transcript_gene_mapping[transcript_gene_mapping > 1]
        
        if len(multi_gene_transcripts) > 0:
            logging.info(f'一个转录本对应多个基因ID的数量: {len(multi_gene_transcripts):,}')
            logging.info('前5个样例：')
            for transcript_id in multi_gene_transcripts.head().index:
                genes = df[df['transcript_id'] == transcript_id]['GENE ID'].unique()
                logging.info(f'  {transcript_id}: {list(genes)}')
        else:
            logging.info('所有转录本都只对应一个基因ID')
        
        logging.info('=' * 60)
        logging.info('统计检查完成！')
        
        return True
        
    except Exception as e:
        logging.error(f'处理过程中发生错误: {str(e)}')
        return False

if __name__ == '__main__':
    print('开始检查数据统计信息...')
    success = check_statistics()
    if success:
        print('检查完成！')
    else:
        print('检查失败，请查看日志文件。') 