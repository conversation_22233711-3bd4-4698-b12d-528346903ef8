#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从 translation_indices_results_grouped_filtered_processed_updated.csv 提取唯一的
transcript_id，并生成包含 transcript_id、GENE ID、GENE symbol 三列的新文件。
"""

import pandas as pd
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extract_unique_transcripts.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

INPUT_FILE = 'translation_indices_results_grouped_filtered_processed_final.csv'
OUTPUT_FILE = 'unique_transcript_gene_mapping.csv'

def extract_unique_transcripts():
    """提取唯一的转录本ID及其对应的基因信息"""
    try:
        logging.info('读取输入文件...')
        df = pd.read_csv(INPUT_FILE)
        logging.info(f'原始文件共 {len(df)} 行')
        
        # 提取需要的三列
        subset_df = df[['transcript_id', 'GENE ID', 'GENE symbol']].copy()
        
        # 去除完全重复的行
        subset_df = subset_df.drop_duplicates()
        logging.info(f'去除重复行后剩余 {len(subset_df)} 行')
        
        # 按 transcript_id 去重，保留第一个遇到的记录
        unique_df = subset_df.drop_duplicates(subset=['transcript_id'], keep='first')
        logging.info(f'按 transcript_id 去重后剩余 {len(unique_df)} 行')
        
        # 检查是否有 transcript_id 对应多个不同的基因ID或符号
        duplicated_transcripts = subset_df[subset_df.duplicated(subset=['transcript_id'], keep=False)]
        if not duplicated_transcripts.empty:
            # 按 transcript_id 分组，检查基因信息是否一致
            inconsistent_count = 0
            for transcript_id, group in duplicated_transcripts.groupby('transcript_id'):
                unique_gene_ids = group['GENE ID'].nunique()
                unique_gene_symbols = group['GENE symbol'].nunique()
                
                if unique_gene_ids > 1 or unique_gene_symbols > 1:
                    inconsistent_count += 1
                    if inconsistent_count <= 5:  # 只记录前5个不一致的案例
                        logging.warning(f'转录本 {transcript_id} 对应多个不同的基因信息:')
                        for _, row in group.iterrows():
                            logging.warning(f'  GENE ID: {row["GENE ID"]}, GENE symbol: {row["GENE symbol"]}')
            
            if inconsistent_count > 0:
                logging.warning(f'发现 {inconsistent_count} 个转录本对应多个不同的基因信息，已保留第一个遇到的记录')
        
        # 按 transcript_id 排序
        unique_df = unique_df.sort_values('transcript_id').reset_index(drop=True)
        
        # 保存结果
        logging.info(f'保存结果到 {OUTPUT_FILE}...')
        unique_df.to_csv(OUTPUT_FILE, index=False)
        
        # 输出统计信息
        logging.info('=' * 60)
        logging.info('提取完成！统计信息：')
        logging.info(f'原始记录数: {len(df)}')
        logging.info(f'唯一转录本数: {len(unique_df)}')
        logging.info(f'唯一基因ID数: {unique_df["GENE ID"].nunique()}')
        logging.info(f'唯一基因符号数: {unique_df["GENE symbol"].nunique()}')
        logging.info(f'输出文件: {OUTPUT_FILE}')
        logging.info('=' * 60)
        
        return True
        
    except Exception as e:
        logging.error(f'处理过程中发生错误: {str(e)}')
        return False

if __name__ == '__main__':
    print('开始提取唯一转录本信息...')
    success = extract_unique_transcripts()
    if success:
        print('提取完成！')
    else:
        print('提取失败，请查看日志文件。') 