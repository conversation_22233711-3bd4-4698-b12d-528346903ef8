#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理 unique_transcript_gene_mapping_with_utr_updated.csv 文件，
通过查询本地 MySQL 数据库的 transcript 表，添加 transcript_location 列。
"""

import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
import time

# 数据库配置信息
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306,
    'autocommit': False,
    'connection_timeout': 600,
    'use_unicode': True,
    'charset': 'utf8mb4'
}

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('add_transcript_location.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

INPUT_FILE = 'unique_transcript_gene_mapping_with_utr_updated.csv'
OUTPUT_FILE = 'unique_transcript_gene_mapping_with_location.csv'

def create_connection():
    """创建数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        logging.info("成功连接到MySQL数据库")
        return connection
    except Error as e:
        logging.error(f"连接数据库时出错: {e}")
        return None

def check_connection(connection):
    """检查并重新连接数据库"""
    try:
        if not connection.is_connected():
            logging.warning("数据库连接已断开，尝试重新连接...")
            connection.reconnect(attempts=3, delay=2)
            logging.info("数据库重新连接成功")
        return True
    except Error as e:
        logging.error(f"重新连接数据库失败: {e}")
        return False

def get_transcript_locations(connection, transcript_ids):
    """批量查询转录本位置信息"""
    if not check_connection(connection):
        logging.error("数据库连接检查失败")
        return {}
    
    cursor = connection.cursor()
    location_mapping = {}
    
    try:
        # 分批查询，避免一次性查询过多数据
        batch_size = 1000
        total_batches = (len(transcript_ids) + batch_size - 1) // batch_size
        
        for i in range(0, len(transcript_ids), batch_size):
            batch_ids = transcript_ids[i:i + batch_size]
            current_batch = (i // batch_size) + 1
            
            logging.info(f"查询第 {current_batch}/{total_batches} 批转录本位置信息...")
            
            # 构建查询语句
            placeholders = ','.join(['%s'] * len(batch_ids))
            query = f"""
            SELECT transcriptId, location 
            FROM transcriptSequence 
            WHERE transcriptId IN ({placeholders})
            """
            
            cursor.execute(query, batch_ids)
            results = cursor.fetchall()
            
            # 将结果添加到映射字典
            for transcript_id, location in results:
                location_mapping[transcript_id] = location
            
            logging.info(f"第 {current_batch} 批查询完成，找到 {len(results)} 个匹配记录")
            
            # 短暂休息，避免数据库压力过大
            if current_batch < total_batches:
                time.sleep(0.1)
        
        logging.info(f"所有批次查询完成，总共找到 {len(location_mapping)} 个转录本位置信息")
        
    except Error as e:
        logging.error(f"查询转录本位置信息时出错: {e}")
    finally:
        cursor.close()
    
    return location_mapping

def add_transcript_location():
    """添加转录本位置信息到CSV文件"""
    try:
        # 读取输入文件
        logging.info(f'读取输入文件: {INPUT_FILE}')
        df = pd.read_csv(INPUT_FILE)
        logging.info(f'输入文件共 {len(df)} 行')
        
        # 获取唯一的转录本ID列表
        unique_transcript_ids = df['transcript_id'].unique().tolist()
        logging.info(f'唯一转录本ID数量: {len(unique_transcript_ids)}')
        
        # 连接数据库
        connection = create_connection()
        if connection is None:
            logging.error("无法连接到数据库，程序退出")
            return False
        
        try:
            # 查询转录本位置信息
            location_mapping = get_transcript_locations(connection, unique_transcript_ids)
            
            # 添加位置信息列
            logging.info('添加 transcript_location 列...')
            df['transcript_location'] = df['transcript_id'].map(location_mapping)
            
            # 统计匹配情况
            matched_count = df['transcript_location'].notna().sum()
            unmatched_count = df['transcript_location'].isna().sum()
            
            logging.info('=' * 60)
            logging.info('匹配结果统计：')
            logging.info(f'总记录数: {len(df):,}')
            logging.info(f'成功匹配位置信息的记录数: {matched_count:,}')
            logging.info(f'未匹配到位置信息的记录数: {unmatched_count:,}')
            logging.info(f'匹配率: {(matched_count / len(df)) * 100:.2f}%')
            
            # 显示一些未匹配的转录本ID样例
            if unmatched_count > 0:
                unmatched_transcripts = df[df['transcript_location'].isna()]['transcript_id'].unique()
                sample_count = min(10, len(unmatched_transcripts))
                logging.info(f'\n未匹配的转录本ID样例（前{sample_count}个）：')
                for transcript_id in unmatched_transcripts[:sample_count]:
                    logging.info(f'  {transcript_id}')
                
                if len(unmatched_transcripts) > 10:
                    logging.info(f'  ... 还有 {len(unmatched_transcripts) - 10} 个未匹配的转录本')
                
                # 用 'NA' 填充未匹配的位置
                df['transcript_location'] = df['transcript_location'].fillna('NA')
            
            # 保存结果
            logging.info(f'保存结果到 {OUTPUT_FILE}...')
            df.to_csv(OUTPUT_FILE, index=False)
            
            logging.info('=' * 60)
            logging.info('处理完成！')
            logging.info(f'输出文件: {OUTPUT_FILE}')
            logging.info('=' * 60)
            
            return True
            
        finally:
            if connection and connection.is_connected():
                connection.close()
                logging.info("数据库连接已关闭")
        
    except Exception as e:
        logging.error(f'处理过程中发生错误: {str(e)}')
        return False

if __name__ == '__main__':
    print('开始添加转录本位置信息...')
    success = add_transcript_location()
    if success:
        print('处理完成！')
    else:
        print('处理失败，请查看日志文件。') 