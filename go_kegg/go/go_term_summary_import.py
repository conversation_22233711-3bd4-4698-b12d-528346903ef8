import pandas as pd
import mysql.connector
import os
import time

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306,
    'autocommit': False,
    'connect_timeout': 60,
    'sql_mode': 'TRADITIONAL'
}

def main():
    # 1. 读取 CSV 文件
    csv_path = 'GO_Term_with_merged_project_ids_replaced.csv'
    
    # 检查文件是否存在
    if not os.path.exists(csv_path):
        print(f"错误: 文件 '{csv_path}' 不存在")
        return
    
    # 使用更严格的参数读取CSV
    df = pd.read_csv(csv_path, 
                     skipinitialspace=True,
                     skip_blank_lines=True)
    
    # 检查并删除任何空行或GO_Term为NaN的行
    df = df.dropna(subset=['GO_Term'])
    
    print(f"读取到 {len(df)} 行有效数据")
    print(f"列名: {list(df.columns)}")
    
    # 2. 连接 MySQL 数据库
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("成功连接到数据库")
        
        # 3. 创建表（如果不存在）
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS goTermSummaryLast (
            goTerm VARCHAR(500) PRIMARY KEY,
            translationProjectIds TEXT,
            FULLTEXT INDEX ft_go_term_search (goTerm, translationProjectIds)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """

        cursor.execute(create_table_sql)
        print("表 goTermSummaryLast 创建成功或已存在")

        # 4. 清空表数据（如果需要）
        cursor.execute("TRUNCATE TABLE goTermSummaryLast")
        print("清空表数据")
        
        # 5. 准备插入数据的SQL语句
        insert_sql = """
        INSERT INTO goTermSummaryLast (goTerm, translationProjectIds)
        VALUES (%s, %s)
        ON DUPLICATE KEY UPDATE
            translationProjectIds = VALUES(translationProjectIds)
        """
        
        # 6. 分批插入数据
        batch_size = 1000
        total_inserted = 0
        
        for i in range(0, len(df), batch_size):
            batch = df.iloc[i:i + batch_size]
            batch_data = []
            
            for _, row in batch.iterrows():
                batch_data.append((
                    str(row['GO_Term']).strip(),
                    str(row['translation_project_ids']).strip()
                ))
            
            try:
                cursor.executemany(insert_sql, batch_data)
                connection.commit()
                total_inserted += len(batch_data)
                print(f"批次 {i//batch_size + 1}: 成功插入 {len(batch_data)} 条记录 (总计: {total_inserted}/{len(df)})")
                
                # 短暂休息，避免数据库过载
                if i + batch_size < len(df):
                    time.sleep(0.1)
                    
            except mysql.connector.Error as err:
                print(f"批量插入失败: {err}")
                connection.rollback()
                
                # 尝试单条插入
                for data in batch_data:
                    try:
                        cursor.execute(insert_sql, data)
                        connection.commit()
                        total_inserted += 1
                    except mysql.connector.Error as single_err:
                        print(f"单条插入失败: {data[0][:50]}... - {single_err}")
                        connection.rollback()
        
        print(f"\n数据导入完成！")
        print(f"成功导入 {total_inserted} 条GO项汇总记录")
        
        # 7. 验证导入结果
        cursor.execute("SELECT COUNT(*) FROM goTermSummaryLast")
        count = cursor.fetchone()[0]
        print(f"数据库中GO项汇总记录总数: {count}")

        # 显示一些示例数据
        cursor.execute("SELECT goTerm, translationProjectIds FROM goTermSummaryLast LIMIT 3")
        results = cursor.fetchall()
        print("\n前3条示例数据:")
        for i, (go_term, project_ids) in enumerate(results, 1):
            print(f"{i}. GO项: {go_term}")
            print(f"   项目ID: {project_ids[:100]}{'...' if len(project_ids) > 100 else ''}")
            print()
    
    except mysql.connector.Error as err:
        print(f"数据库错误: {err}")
        if 'connection' in locals():
            connection.rollback()
    
    finally:
        # 关闭连接
        if 'connection' in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("数据库连接已关闭")

if __name__ == '__main__':
    main() 