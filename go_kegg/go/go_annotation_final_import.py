import pandas as pd
import mysql.connector
import os
import time

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306,
    'autocommit': False,
    'connect_timeout': 60,
    'sql_mode': 'TRADITIONAL'
}

def main():
    # 1. 读取 CSV 文件
    csv_path = 'GO_annotation_final_processed_with_project_ids_replaced.csv'
    
    # 检查文件是否存在
    if not os.path.exists(csv_path):
        print(f"错误: 文件 '{csv_path}' 不存在")
        return
    
    # 使用更严格的参数读取CSV
    df = pd.read_csv(csv_path, 
                     skipinitialspace=True,
                     skip_blank_lines=True)
    
    # 检查并删除任何空行或Gene_symbol为NaN的行
    df = df.dropna(subset=['Gene_symbol'])
    
    print(f"读取到 {len(df)} 行有效数据")
    print(f"列名: {list(df.columns)}")
    
    # 2. 连接 MySQL 数据库
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()

    # 3. 检查表是否存在，如果存在则删除
    cursor.execute("SHOW TABLES LIKE 'goAnnotationLast'")
    if cursor.fetchone():
        print("删除已存在的表: goAnnotationLast")
        cursor.execute("DROP TABLE goAnnotationLast")
        conn.commit()

    # 4. 创建数据表，包含CSV文件的所有列
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS goAnnotationLast (
        id INT AUTO_INCREMENT PRIMARY KEY,
        geneSymbol VARCHAR(100),
        geneId VARCHAR(100),
        goTerm VARCHAR(500),
        goDomain VARCHAR(10),
        goId VARCHAR(20),
        translationProjectIds TEXT,
        INDEX idx_gene_symbol (geneSymbol),
        INDEX idx_gene_id (geneId),
        INDEX idx_go_term (goTerm(255)),
        INDEX idx_go_domain (goDomain),
        INDEX idx_go_id (goId),
        INDEX idx_project_ids (translationProjectIds(255)),
        FULLTEXT INDEX ft_go_search (geneSymbol, geneId, goTerm, goId, translationProjectIds)
    )
    """
    cursor.execute(create_table_sql)
    print("GO注释表创建成功！")

    # 5. 准备插入数据的 SQL 语句
    insert_sql = """
    INSERT INTO goAnnotationLast (geneSymbol, geneId, goTerm, goDomain, goId, translationProjectIds)
    VALUES (%s, %s, %s, %s, %s, %s)
    """

    # 6. 分批处理数据以避免连接超时
    batch_size = 1000
    total_rows = len(df)
    successful_imports = 0
    
    print(f"开始分批导入数据，批次大小: {batch_size}")
    
    for i in range(0, total_rows, batch_size):
        batch_end = min(i + batch_size, total_rows)
        batch_df = df.iloc[i:batch_end]
        
        print(f"处理批次 {i//batch_size + 1}: 行 {i+1} 到 {batch_end}")
        
        # 准备当前批次的数据
        data_to_insert = []
        for index, row in batch_df.iterrows():
            try:
                # 构建数据元组，处理NaN值
                data_tuple = (
                    row['Gene_symbol'] if not pd.isna(row['Gene_symbol']) else None,
                    row['Gene_ID'] if not pd.isna(row['Gene_ID']) else None,
                    row['GO_Term'] if not pd.isna(row['GO_Term']) else None,
                    row['GO_Domain'] if not pd.isna(row['GO_Domain']) else None,
                    row['GO_ID'] if not pd.isna(row['GO_ID']) else None,
                    row['translation_project_ids'] if not pd.isna(row['translation_project_ids']) else None
                )
                
                # 只添加有效的记录（至少有gene_symbol）
                if data_tuple[0] is not None:
                    data_to_insert.append(data_tuple)
                    
            except Exception as e:
                print(f"跳过行 {index}, 错误: {e}")
                continue
        
        # 批量插入当前批次
        if data_to_insert:
            try:
                # 重新连接数据库以避免连接超时
                if not conn.is_connected():
                    conn = mysql.connector.connect(**DB_CONFIG)
                    cursor = conn.cursor()
                
                cursor.executemany(insert_sql, data_to_insert)
                conn.commit()
                successful_imports += len(data_to_insert)
                print(f"批次 {i//batch_size + 1} 成功导入 {len(data_to_insert)} 条记录")
                
            except mysql.connector.Error as err:
                print(f"批次 {i//batch_size + 1} 导入失败: {err}")
                # 尝试单条插入
                single_success = 0
                for data_tuple in data_to_insert:
                    try:
                        cursor.execute(insert_sql, data_tuple)
                        conn.commit()
                        single_success += 1
                    except mysql.connector.Error as single_err:
                        print(f"单条记录导入失败: {single_err}")
                successful_imports += single_success
                print(f"批次 {i//batch_size + 1} 单条导入成功 {single_success} 条记录")
        
        # 短暂休息以避免数据库过载
        time.sleep(0.1)

    print(f"\n总共成功导入 {successful_imports} / {total_rows} 条记录")

    # 7. 查看导入后的数据统计
    try:
        cursor.execute("SELECT COUNT(*) FROM goAnnotationLast")
        total_count = cursor.fetchone()[0]
        print(f"数据库中总共有 {total_count} 条GO注释记录")
        
        # 查看每个GO域的记录数
        cursor.execute("""
            SELECT goDomain, COUNT(*) as count 
            FROM goAnnotationLast 
            GROUP BY goDomain 
            ORDER BY count DESC
        """)
        domain_stats = cursor.fetchall()
        print("\n各GO域统计:")
        for domain, count in domain_stats:
            print(f"  {domain}: {count} 条记录")
            
    except mysql.connector.Error as err:
        print(f"查询统计信息时出错: {err}")

    # 8. 关闭连接
    cursor.close()
    conn.close()
    print("\nGO注释数据导入完成！")

if __name__ == '__main__':
    main() 