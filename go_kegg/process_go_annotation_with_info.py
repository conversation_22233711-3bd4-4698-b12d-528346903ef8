import pandas as pd
import numpy as np
from collections import defaultdict
import ast

def main():
    print("开始处理GO数据...")
    
    # 1. 读取GO注释文件
    print("读取GO_annotation_final.csv...")
    go_df = pd.read_csv('GO_annotation_final.csv')
    print(f"GO注释数据: {len(go_df)} 行")
    
    # 2. 读取基因计数文件
    print("读取gene_count_by_project_results_with_info.csv...")
    gene_count_df = pd.read_csv('gene_count_by_project_results_with_info.csv')
    print(f"基因计数数据: {len(gene_count_df)} 行")
    
    # 3. 构建基因ID到信息的映射字典
    print("构建基因信息映射...")
    gene_info_map = defaultdict(lambda: {'tissues': set(), 'cell_lines': set(), 'diseases': set()})
    
    for _, row in gene_count_df.iterrows():
        gene_id = row['GENE ID']
        
        # 处理Tissue/Cell Type
        tissue = row['Tissue/Cell Type']
        if pd.notna(tissue) and tissue.strip():
            gene_info_map[gene_id]['tissues'].add(tissue.strip())
        
        # 处理Cell line
        cell_line = row['Cell line']
        if pd.notna(cell_line) and cell_line.strip():
            gene_info_map[gene_id]['cell_lines'].add(cell_line.strip())
        
        # 处理Disease
        disease = row['Disease']
        if pd.notna(disease) and disease.strip():
            gene_info_map[gene_id]['diseases'].add(disease.strip())
    
    print(f"处理了 {len(gene_info_map)} 个基因的信息")
    
    # 4. 为GO注释数据添加新列
    print("为GO注释数据添加新列...")
    
    def format_set_as_string(s):
        """将集合格式化为字符串形式 {1,2,3,...}"""
        if not s:
            return "{}"
        # 将集合转换为排序后的列表，然后格式化
        sorted_items = sorted(list(s))
        return "{" + ",".join(sorted_items) + "}"
    
    # 添加新列
    tissues_list = []
    cell_lines_list = []
    diseases_list = []
    
    for _, row in go_df.iterrows():
        gene_id = row['Gene_ID']
        
        if gene_id in gene_info_map:
            tissues_list.append(format_set_as_string(gene_info_map[gene_id]['tissues']))
            cell_lines_list.append(format_set_as_string(gene_info_map[gene_id]['cell_lines']))
            diseases_list.append(format_set_as_string(gene_info_map[gene_id]['diseases']))
        else:
            tissues_list.append("{}")
            cell_lines_list.append("{}")
            diseases_list.append("{}")
    
    # 添加新列到DataFrame
    go_df['Tissue/Cell Type'] = tissues_list
    go_df['Cell line'] = cell_lines_list
    go_df['Disease'] = diseases_list
    
    # 5. 保存处理后的文件
    output_file = 'GO_annotation_final_with_info.csv'
    print(f"保存处理后的文件到: {output_file}")
    
    # 保存所有列
    go_df.to_csv(output_file, index=False)
    
    # 6. 显示统计信息
    print("\n处理完成！统计信息:")
    print(f"总GO注释记录数: {len(go_df)}")
    
    # 统计有信息的基因数量
    non_empty_tissues = sum(1 for x in tissues_list if x != "{}")
    non_empty_cell_lines = sum(1 for x in cell_lines_list if x != "{}")
    non_empty_diseases = sum(1 for x in diseases_list if x != "{}")
    
    print(f"有组织/细胞类型信息的记录: {non_empty_tissues}")
    print(f"有细胞系信息的记录: {non_empty_cell_lines}")
    print(f"有疾病信息的记录: {non_empty_diseases}")
    
    # 显示一些示例
    print("\n前5行示例:")
    print(go_df[['GO_Term', 'Tissue/Cell Type', 'Cell line', 'Disease']].head())
    
    # 显示一些有信息的例子
    print("\n有完整信息的示例:")
    mask = (go_df['Tissue/Cell Type'] != "{}") | (go_df['Cell line'] != "{}") | (go_df['Disease'] != "{}")
    if mask.any():
        examples = go_df[mask][['GO_Term', 'Tissue/Cell Type', 'Cell line', 'Disease']].head(3)
        print(examples)
    
    print(f"\n处理完成的文件已保存为: {output_file}")

if __name__ == '__main__':
    main() 