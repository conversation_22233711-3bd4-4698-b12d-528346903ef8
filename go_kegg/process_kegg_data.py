#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def process_kegg_data(input_file, output_file):
    """
    处理KEGG数据文件，标准化列名和数据格式
    """
    print(f"读取文件: {input_file}")
    
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    print(f"原始数据形状: {df.shape}")
    print(f"原始列名: {list(df.columns)}")
    
    # 标准化列名 - 将空格和特殊字符替换为下划线
    column_mapping = {}
    for col in df.columns:
        # 将 "Tissue/Cell Type" 转换为 "Tissue_Cell_Type"
        new_col = col.replace('/', '_').replace(' ', '_')
        column_mapping[col] = new_col
    
    # 重命名列
    df = df.rename(columns=column_mapping)
    
    print(f"标准化后的列名: {list(df.columns)}")
    
    # 检查需要处理的列
    target_columns = ['Tissue_Cell_Type', 'Cell_line', 'Disease']
    
    for col in target_columns:
        if col in df.columns:
            print(f"处理列: {col}")
            
            # 检查数据格式
            sample_values = df[col].dropna().head(5)
            print(f"  样本数据: {list(sample_values)}")
            
            # 处理数据格式
            df[col] = df[col].apply(process_column_value)
            
            # 检查处理后的数据
            processed_sample = df[col].dropna().head(5)
            print(f"  处理后样本: {list(processed_sample)}")
        else:
            print(f"警告: 列 '{col}' 不存在于数据中")
    
    # 保存处理后的数据
    df.to_csv(output_file, index=False)
    print(f"处理完成，结果保存到: {output_file}")
    
    return df

def process_column_value(value):
    """
    处理单个列值，确保格式为 {item1,item2,item3}
    """
    if pd.isna(value) or value == '':
        return '{}'
    
    # 转换为字符串
    value_str = str(value).strip()
    
    # 如果已经是正确格式，直接返回
    if value_str.startswith('{') and value_str.endswith('}'):
        return value_str
    
    # 如果是其他格式，尝试转换
    if ',' in value_str:
        # 分割并清理每个项目
        items = [item.strip() for item in value_str.split(',')]
        # 过滤空项目
        items = [item for item in items if item]
        # 格式化为 {item1,item2,item3}
        return '{' + ','.join(items) + '}'
    else:
        # 单个值
        if value_str:
            return '{' + value_str + '}'
        else:
            return '{}'

def analyze_data_format(file_path):
    """
    分析数据格式
    """
    print(f"分析文件: {file_path}")
    
    df = pd.read_csv(file_path)
    
    # 检查目标列的数据格式
    target_columns = ['Tissue/Cell Type', 'Cell line', 'Disease']
    
    for col in target_columns:
        if col in df.columns:
            print(f"\n分析列: {col}")
            
            # 统计不同格式的数据
            non_null_values = df[col].dropna()
            
            # 统计以 { 开头的数据
            bracket_format = non_null_values[non_null_values.str.startswith('{', na=False)]
            print(f"  以{{开头的数据: {len(bracket_format)} / {len(non_null_values)}")
            
            # 统计空值
            null_count = df[col].isna().sum()
            print(f"  空值数量: {null_count}")
            
            # 显示几个样本
            print(f"  样本数据:")
            for i, sample in enumerate(non_null_values.head(3)):
                print(f"    {i+1}: {sample}")
        else:
            print(f"列 '{col}' 不存在")

def main():
    """
    主函数
    """
    input_file = "go_kegg/KEGG_final_with_info.csv"
    output_file = "go_kegg/KEGG_final_with_info_processed.csv"
    
    print("=== KEGG数据处理脚本 ===\n")
    
    # 首先分析数据格式
    analyze_data_format(input_file)
    
    print("\n" + "="*50 + "\n")
    
    # 处理数据
    processed_df = process_kegg_data(input_file, output_file)
    
    print(f"\n处理完成！")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"处理的记录数: {len(processed_df)}")

if __name__ == "__main__":
    main()
