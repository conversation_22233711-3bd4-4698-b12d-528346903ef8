import pandas as pd
import numpy as np
from collections import defaultdict
import ast

def parse_set_string(set_str):
    """解析集合字符串 {a,b,c} 为Python集合"""
    if pd.isna(set_str) or set_str == '{}' or set_str.strip() == '':
        return set()
    
    # 移除大括号并分割
    content = set_str.strip('{}')
    if not content:
        return set()
    
    # 分割并清理每个元素，处理可能包含逗号的引用字符串
    items = []
    current_item = ""
    in_quotes = False
    
    for char in content:
        if char == '"' and not in_quotes:
            in_quotes = True
        elif char == '"' and in_quotes:
            in_quotes = False
        elif char == ',' and not in_quotes:
            if current_item.strip():
                items.append(current_item.strip().strip('"'))
            current_item = ""
            continue
        
        current_item += char
    
    # 添加最后一个项目
    if current_item.strip():
        items.append(current_item.strip().strip('"'))
    
    return set(item for item in items if item)

def format_set_as_string(s):
    """将集合格式化为字符串形式 {1,2,3,...}"""
    if not s:
        return "{}"
    # 将集合转换为排序后的列表，然后格式化
    sorted_items = sorted(list(s))
    return "{" + ",".join(sorted_items) + "}"

def main():
    print("开始处理GO项汇总数据...")
    
    # 1. 读取GO文件
    print("读取GO_annotation_final_with_info_lastest.csv...")
    go_df = pd.read_csv('GO_annotation_final_with_info_lastest.csv')
    print(f"GO数据: {len(go_df)} 行")
    
    # 2. 获取所有唯一的GO_Term
    unique_go_terms = go_df['GO_Term'].unique()
    print(f"发现 {len(unique_go_terms)} 个唯一的GO项")
    
    # 3. 按GO_Term分组并合并信息
    print("按GO项分组并合并信息...")
    
    go_term_summary = []
    
    # 测试解析功能
    test_row = go_df.iloc[0]
    print(f"\n测试数据解析:")
    print(f"原始Tissue/Cell Type: {test_row['Tissue/Cell Type']}")
    test_tissues = parse_set_string(test_row['Tissue/Cell Type'])
    print(f"解析后的组织类型数量: {len(test_tissues)}")
    print(f"前5个组织类型: {list(test_tissues)[:5] if test_tissues else '无'}")
    
    for i, go_term in enumerate(unique_go_terms):
        if pd.isna(go_term):
            continue
        
        # 显示进度
        if i % 1000 == 0:
            print(f"处理进度: {i+1}/{len(unique_go_terms)} ({(i+1)/len(unique_go_terms)*100:.1f}%)")
            
        # 获取该GO项的所有行
        go_term_rows = go_df[go_df['GO_Term'] == go_term]
        
        # 合并三列的所有唯一值
        all_tissues = set()
        all_cell_lines = set()
        all_diseases = set()
        
        # 获取该GO项的所有基因信息（用于统计）
        genes_in_go_term = set()
        go_domains = set()
        go_ids = set()
        
        for _, row in go_term_rows.iterrows():
            # 收集基因和GO信息
            if not pd.isna(row['Gene_symbol']):
                genes_in_go_term.add(row['Gene_symbol'])
            if not pd.isna(row['GO_Domain']):
                go_domains.add(row['GO_Domain'])
            if not pd.isna(row['GO_ID']):
                go_ids.add(row['GO_ID'])
            
            # 解析并合并Tissue/Cell Type
            tissues = parse_set_string(row['Tissue/Cell Type'])
            all_tissues.update(tissues)
            
            # 解析并合并Cell line
            cell_lines = parse_set_string(row['Cell line'])
            all_cell_lines.update(cell_lines)
            
            # 解析并合并Disease
            diseases = parse_set_string(row['Disease'])
            all_diseases.update(diseases)
        
        # 创建汇总行，只保存四列
        summary_row = {
            'GO_Term': go_term,
            'Tissue/Cell Type': format_set_as_string(all_tissues),
            'Cell line': format_set_as_string(all_cell_lines),
            'Disease': format_set_as_string(all_diseases)
        }
        
        go_term_summary.append(summary_row)
    
    # 4. 创建汇总DataFrame
    summary_df = pd.DataFrame(go_term_summary)
    
    # 按GO项排序
    summary_df = summary_df.sort_values('GO_Term').reset_index(drop=True)
    
    # 5. 保存结果
    output_file = 'GO_term_summary_lastest.csv'
    print(f"保存汇总结果到: {output_file}")
    summary_df.to_csv(output_file, index=False)
    
    # 6. 显示统计信息
    print("\n处理完成！统计信息:")
    print(f"总GO项数: {len(summary_df)}")
    
    # 显示信息覆盖统计
    go_terms_with_tissues = len(summary_df[summary_df['Tissue/Cell Type'] != '{}'])
    go_terms_with_cell_lines = len(summary_df[summary_df['Cell line'] != '{}'])
    go_terms_with_diseases = len(summary_df[summary_df['Disease'] != '{}'])
    
    print(f"\n信息覆盖统计:")
    print(f"有组织/细胞类型信息的GO项: {go_terms_with_tissues}/{len(summary_df)} ({go_terms_with_tissues/len(summary_df)*100:.1f}%)")
    print(f"有细胞系信息的GO项: {go_terms_with_cell_lines}/{len(summary_df)} ({go_terms_with_cell_lines/len(summary_df)*100:.1f}%)")
    print(f"有疾病信息的GO项: {go_terms_with_diseases}/{len(summary_df)} ({go_terms_with_diseases/len(summary_df)*100:.1f}%)")
    
    # 显示前10个GO项示例
    print("\n前10个GO项示例:")
    for i, row in summary_df.head(10).iterrows():
        print(f"\n{i+1}. GO项: {row['GO_Term']}")
        print(f"   组织类型数量: {len(parse_set_string(row['Tissue/Cell Type']))}")
        print(f"   细胞系数量: {len(parse_set_string(row['Cell line']))}")
        print(f"   疾病数量: {len(parse_set_string(row['Disease']))}")
        
        # 显示部分具体内容
        tissues = parse_set_string(row['Tissue/Cell Type'])
        if tissues:
            print(f"   前5个组织: {list(tissues)[:5]}")
        
        cell_lines = parse_set_string(row['Cell line'])
        if cell_lines:
            print(f"   前5个细胞系: {list(cell_lines)[:5]}")
        
        diseases = parse_set_string(row['Disease'])
        if diseases:
            print(f"   前5个疾病: {list(diseases)[:5]}")
    
    print(f"\n汇总完成的文件已保存为: {output_file}")

if __name__ == '__main__':
    main() 