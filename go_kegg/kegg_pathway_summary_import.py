import pandas as pd
import mysql.connector
import os
import time

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306,
    'autocommit': False,
    'connect_timeout': 60,
    'sql_mode': 'TRADITIONAL'
}

def main():
    # 1. 读取 CSV 文件
    csv_path = 'KEGG_pathway_summary_last.csv'
    
    # 检查文件是否存在
    if not os.path.exists(csv_path):
        print(f"错误: 文件 '{csv_path}' 不存在")
        return
    
    # 使用更严格的参数读取CSV
    df = pd.read_csv(csv_path, 
                     skipinitialspace=True,
                     skip_blank_lines=True)
    
    # 检查并删除任何空行或Pathway_Description为NaN的行
    df = df.dropna(subset=['Pathway_Description'])
    
    print(f"读取到 {len(df)} 行有效数据")
    print(f"列名: {list(df.columns)}")
    
    # 2. 连接 MySQL 数据库
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()

    # 3. 检查表是否存在，如果存在则删除
    cursor.execute("SHOW TABLES LIKE 'keggPathwaySummary'")
    if cursor.fetchone():
        print("删除已存在的表: keggPathwaySummary")
        cursor.execute("DROP TABLE keggPathwaySummary")
        conn.commit()

    # 4. 创建数据表，使用Pathway_Description作为主键
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS keggPathwaySummary (
        pathwayDescription VARCHAR(500) PRIMARY KEY,
        tissueCellType TEXT,
        cellLine TEXT,
        disease TEXT,
        FULLTEXT INDEX ft_pathway_search (pathwayDescription, tissueCellType, cellLine, disease)
    )
    """
    cursor.execute(create_table_sql)
    print("KEGG通路汇总表创建成功！")

    # 5. 准备插入数据的 SQL 语句
    insert_sql = """
    INSERT INTO keggPathwaySummary (pathwayDescription, tissueCellType, cellLine, disease)
    VALUES (%s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE
        tissueCellType = VALUES(tissueCellType),
        cellLine = VALUES(cellLine),
        disease = VALUES(disease)
    """

    # 6. 分批处理数据以避免连接超时
    batch_size = 100  # 由于数据量较小(367行)，使用较小的批次
    total_rows = len(df)
    successful_imports = 0
    
    print(f"开始分批导入数据，批次大小: {batch_size}")
    
    for i in range(0, total_rows, batch_size):
        batch_end = min(i + batch_size, total_rows)
        batch_df = df.iloc[i:batch_end]
        
        print(f"处理批次 {i//batch_size + 1}: 行 {i+1} 到 {batch_end}")
        
        # 准备当前批次的数据
        data_to_insert = []
        for index, row in batch_df.iterrows():
            try:
                # 构建数据元组，处理NaN值
                data_tuple = (
                    row['Pathway_Description'] if not pd.isna(row['Pathway_Description']) else None,
                    row['Tissue/Cell Type'] if not pd.isna(row['Tissue/Cell Type']) else None,
                    row['Cell line'] if not pd.isna(row['Cell line']) else None,
                    row['Disease'] if not pd.isna(row['Disease']) else None
                )
                
                # 只添加有效的记录（至少有pathway_description）
                if data_tuple[0] is not None:
                    data_to_insert.append(data_tuple)
                    
            except Exception as e:
                print(f"跳过行 {index}, 错误: {e}")
                continue
        
        # 批量插入当前批次
        if data_to_insert:
            try:
                # 重新连接数据库以避免连接超时
                if not conn.is_connected():
                    conn = mysql.connector.connect(**DB_CONFIG)
                    cursor = conn.cursor()
                
                cursor.executemany(insert_sql, data_to_insert)
                conn.commit()
                successful_imports += len(data_to_insert)
                print(f"批次 {i//batch_size + 1} 成功导入 {len(data_to_insert)} 条记录")
                
            except mysql.connector.Error as err:
                print(f"批次 {i//batch_size + 1} 导入失败: {err}")
                # 尝试单条插入
                single_success = 0
                for data_tuple in data_to_insert:
                    try:
                        cursor.execute(insert_sql, data_tuple)
                        conn.commit()
                        single_success += 1
                    except mysql.connector.Error as single_err:
                        print(f"单条记录导入失败: {single_err}")
                        print(f"失败的记录: {data_tuple[0]}")
                successful_imports += single_success
                print(f"批次 {i//batch_size + 1} 单条导入成功 {single_success} 条记录")
        
        # 短暂休息以避免数据库过载
        time.sleep(0.05)

    print(f"\n总共成功导入 {successful_imports} / {total_rows} 条记录")

    # 7. 查看导入后的数据统计
    try:
        cursor.execute("SELECT COUNT(*) FROM keggPathwaySummary")
        total_count = cursor.fetchone()[0]
        print(f"数据库中总共有 {total_count} 条KEGG通路汇总记录")
        
        # 查看一些示例记录
        cursor.execute("""
            SELECT pathwayDescription, 
                   SUBSTRING(tissueCellType, 1, 50) as tissue_preview,
                   SUBSTRING(cellLine, 1, 50) as cell_preview,
                   SUBSTRING(disease, 1, 50) as disease_preview
            FROM keggPathwaySummary 
            LIMIT 5
        """)
        sample_records = cursor.fetchall()
        print("\n前5条记录预览:")
        for record in sample_records:
            print(f"  通路: {record[0]}")
            print(f"  组织: {record[1]}...")
            print(f"  细胞系: {record[2]}...")
            print(f"  疾病: {record[3]}...")
            print("  ---")
            
    except mysql.connector.Error as err:
        print(f"查询统计信息时出错: {err}")

    # 8. 关闭连接
    cursor.close()
    conn.close()
    print("\nKEGG通路汇总数据导入完成！")

if __name__ == '__main__':
    main() 