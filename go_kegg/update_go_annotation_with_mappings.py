import pandas as pd
import json
import ast
import re

def load_mappings(json_file='project_field_mappings.json'):
    """加载映射关系JSON文件"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            mappings = json.load(f)
        print(f"成功加载映射文件: {json_file}")
        return mappings
    except Exception as e:
        print(f"加载映射文件时出错: {e}")
        return None

def parse_list_string(value_str):
    """解析类似 "{value1,value2,value3}" 格式的字符串为列表"""
    if pd.isna(value_str) or not value_str:
        return []
    
    # 移除外层的大括号
    if value_str.startswith('{') and value_str.endswith('}'):
        value_str = value_str[1:-1]
    
    # 按逗号分割并清理每个值
    values = [v.strip() for v in value_str.split(',') if v.strip()]
    return values

def list_to_string(value_list):
    """将列表转换回 "{value1,value2,value3}" 格式"""
    if not value_list:
        return ""
    return "{" + ",".join(value_list) + "}"

def update_values_with_mappings(values_list, mappings_dict):
    """根据映射字典更新值列表"""
    updated_values = []
    changes_count = 0
    
    for value in values_list:
        if value in mappings_dict:
            updated_values.append(mappings_dict[value])
            changes_count += 1
        else:
            updated_values.append(value)
    
    return updated_values, changes_count

def process_go_annotation_file(input_file='GO_term_summary.csv', 
                              output_file='GO_term_summary_updated.csv'):
    """处理GO注释文件，更新三列的值"""
    
    # 加载映射关系
    mappings = load_mappings()
    if not mappings:
        return
    
    tissue_mappings = mappings.get('tissueOrCellType_mappings', {})
    cell_line_mappings = mappings.get('cellLine_mappings', {})
    disease_mappings = mappings.get('disease_mappings', {})
    
    print(f"Tissue/Cell Type 映射: {len(tissue_mappings)} 个")
    print(f"Cell Line 映射: {len(cell_line_mappings)} 个")
    print(f"Disease 映射: {len(disease_mappings)} 个")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(input_file, encoding='utf-8')
        print(f"\n读取CSV文件成功，共 {len(df)} 行记录")
        print(f"列名: {list(df.columns)}")
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return
    
    # 统计更新数量
    total_tissue_changes = 0
    total_cell_line_changes = 0
    total_disease_changes = 0
    
    # 处理每一行
    for index, row in df.iterrows():
        # 处理 Tissue/Cell Type 列
        if 'Tissue/Cell Type' in df.columns:
            tissue_values = parse_list_string(row['Tissue/Cell Type'])
            updated_tissue, tissue_changes = update_values_with_mappings(tissue_values, tissue_mappings)
            df.at[index, 'Tissue/Cell Type'] = list_to_string(updated_tissue)
            total_tissue_changes += tissue_changes
        
        # 处理 Cell line 列
        if 'Cell line' in df.columns:
            cell_line_values = parse_list_string(row['Cell line'])
            updated_cell_line, cell_line_changes = update_values_with_mappings(cell_line_values, cell_line_mappings)
            df.at[index, 'Cell line'] = list_to_string(updated_cell_line)
            total_cell_line_changes += cell_line_changes
        
        # 处理 Disease 列
        if 'Disease' in df.columns:
            disease_values = parse_list_string(row['Disease'])
            updated_disease, disease_changes = update_values_with_mappings(disease_values, disease_mappings)
            df.at[index, 'Disease'] = list_to_string(updated_disease)
            total_disease_changes += disease_changes
    
    # 保存更新后的文件
    try:
        df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"\n更新完成！文件已保存为: {output_file}")
        print(f"Tissue/Cell Type 更新了 {total_tissue_changes} 个值")
        print(f"Cell Line 更新了 {total_cell_line_changes} 个值")
        print(f"Disease 更新了 {total_disease_changes} 个值")
        print(f"总共更新了 {total_tissue_changes + total_cell_line_changes + total_disease_changes} 个值")
    except Exception as e:
        print(f"保存文件时出错: {e}")

def show_sample_changes():
    """显示一些更新示例"""
    mappings = load_mappings()
    if not mappings:
        return
    
    print("\n映射关系示例:")
    
    # 显示组织/细胞类型映射示例
    tissue_mappings = mappings.get('tissueOrCellType_mappings', {})
    if tissue_mappings:
        print("\nTissue/Cell Type 映射示例:")
        for i, (old, new) in enumerate(tissue_mappings.items()):
            if i < 5:  # 只显示前5个
                print(f"  '{old}' -> '{new}'")
            else:
                print(f"  ... 还有 {len(tissue_mappings) - 5} 个映射")
                break
    
    # 显示疾病映射示例
    disease_mappings = mappings.get('disease_mappings', {})
    if disease_mappings:
        print("\nDisease 映射示例:")
        for i, (old, new) in enumerate(disease_mappings.items()):
            if i < 5:  # 只显示前5个
                print(f"  '{old}' -> '{new}'")
            else:
                print(f"  ... 还有 {len(disease_mappings) - 5} 个映射")
                break

def main():
    """主函数"""
    print("开始处理 GO 注释文件...")
    
    # 显示映射关系示例
    show_sample_changes()
    
    # 处理文件
    process_go_annotation_file()
    
    print("\n处理完成！")

if __name__ == '__main__':
    main() 