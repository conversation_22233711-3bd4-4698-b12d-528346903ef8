import pandas as pd
import numpy as np
from collections import defaultdict
import ast

def parse_set_string(set_str):
    """解析集合字符串 {a,b,c} 为Python集合"""
    if pd.isna(set_str) or set_str == '{}' or set_str.strip() == '':
        return set()
    
    # 移除大括号并分割
    content = set_str.strip('{}')
    if not content:
        return set()
    
    # 分割并清理每个元素，处理可能包含逗号的引用字符串
    items = []
    current_item = ""
    in_quotes = False
    
    for char in content:
        if char == '"' and not in_quotes:
            in_quotes = True
        elif char == '"' and in_quotes:
            in_quotes = False
        elif char == ',' and not in_quotes:
            if current_item.strip():
                items.append(current_item.strip().strip('"'))
            current_item = ""
            continue
        
        current_item += char
    
    # 添加最后一个项目
    if current_item.strip():
        items.append(current_item.strip().strip('"'))
    
    return set(item for item in items if item)

def format_set_as_string(s):
    """将集合格式化为字符串形式 {1,2,3,...}"""
    if not s:
        return "{}"
    # 将集合转换为排序后的列表，然后格式化
    sorted_items = sorted(list(s))
    return "{" + ",".join(sorted_items) + "}"

def main():
    print("开始处理KEGG通路汇总数据...")
    
    # 1. 读取KEGG文件
    print("读取KEGG_final_with_info.csv...")
    kegg_df = pd.read_csv('KEGG_final_with_info_last.csv')
    print(f"KEGG数据: {len(kegg_df)} 行")
    
    # 2. 获取所有唯一的Pathway_Description
    unique_pathways = kegg_df['Pathway_Description'].unique()
    print(f"发现 {len(unique_pathways)} 个唯一的通路描述")
    
    # 3. 按Pathway_Description分组并合并信息
    print("按通路描述分组并合并信息...")
    
    pathway_summary = []
    
    # 测试解析功能
    test_row = kegg_df.iloc[0]
    print(f"\n测试数据解析:")
    print(f"原始Tissue/Cell Type: {test_row['Tissue/Cell Type']}")
    test_tissues = parse_set_string(test_row['Tissue/Cell Type'])
    print(f"解析后的组织类型数量: {len(test_tissues)}")
    print(f"前5个组织类型: {list(test_tissues)[:5] if test_tissues else '无'}")
    
    for i, pathway_desc in enumerate(unique_pathways):
        if pd.isna(pathway_desc):
            continue
        
        # 显示进度
        if i % 10 == 0:
            print(f"处理进度: {i+1}/{len(unique_pathways)} ({(i+1)/len(unique_pathways)*100:.1f}%)")
            
        # 获取该通路的所有行
        pathway_rows = kegg_df[kegg_df['Pathway_Description'] == pathway_desc]
        
        # 合并三列的所有唯一值
        all_tissues = set()
        all_cell_lines = set()
        all_diseases = set()
        
        # 获取该通路的所有基因和通路ID（用于统计）
        genes_in_pathway = set()
        pathway_ids = set()
        
        for _, row in pathway_rows.iterrows():
            # 收集基因和通路ID信息
            if not pd.isna(row['Gene symbol']):
                genes_in_pathway.add(row['Gene symbol'])
            if not pd.isna(row['Pathway_ID']):
                pathway_ids.add(row['Pathway_ID'])
            
            # 解析并合并Tissue/Cell Type
            tissues = parse_set_string(row['Tissue/Cell Type'])
            all_tissues.update(tissues)
            
            # 解析并合并Cell line
            cell_lines = parse_set_string(row['Cell line'])
            all_cell_lines.update(cell_lines)
            
            # 解析并合并Disease
            diseases = parse_set_string(row['Disease'])
            all_diseases.update(diseases)
        
        # 创建汇总行，只保存四列
        summary_row = {
            'Pathway_Description': pathway_desc,
            'Tissue/Cell Type': format_set_as_string(all_tissues),
            'Cell line': format_set_as_string(all_cell_lines),
            'Disease': format_set_as_string(all_diseases)
        }
        
        pathway_summary.append(summary_row)
    
    # 4. 创建汇总DataFrame
    summary_df = pd.DataFrame(pathway_summary)
    
    # 按通路描述排序
    summary_df = summary_df.sort_values('Pathway_Description').reset_index(drop=True)
    
    # 5. 保存结果
    output_file = 'KEGG_pathway_summary_last.csv'
    print(f"保存汇总结果到: {output_file}")
    summary_df.to_csv(output_file, index=False)
    
    # 6. 显示统计信息
    print("\n处理完成！统计信息:")
    print(f"总通路数: {len(summary_df)}")
    
    # 显示信息覆盖统计
    pathways_with_tissues = len(summary_df[summary_df['Tissue/Cell Type'] != '{}'])
    pathways_with_cell_lines = len(summary_df[summary_df['Cell line'] != '{}'])
    pathways_with_diseases = len(summary_df[summary_df['Disease'] != '{}'])
    
    print(f"\n信息覆盖统计:")
    print(f"有组织/细胞类型信息的通路: {pathways_with_tissues}/{len(summary_df)} ({pathways_with_tissues/len(summary_df)*100:.1f}%)")
    print(f"有细胞系信息的通路: {pathways_with_cell_lines}/{len(summary_df)} ({pathways_with_cell_lines/len(summary_df)*100:.1f}%)")
    print(f"有疾病信息的通路: {pathways_with_diseases}/{len(summary_df)} ({pathways_with_diseases/len(summary_df)*100:.1f}%)")
    
    # 显示前5个通路示例
    print("\n前5个通路示例:")
    print(summary_df[['Pathway_Description', 'Tissue/Cell Type', 'Cell line', 'Disease']].head(5).to_string(index=False))
    
    print(f"\n汇总完成的文件已保存为: {output_file}")

if __name__ == '__main__':
    main() 