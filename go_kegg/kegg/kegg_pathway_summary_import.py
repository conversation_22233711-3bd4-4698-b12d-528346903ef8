import pandas as pd
import mysql.connector
import os
import time

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306,
    'autocommit': False,
    'connect_timeout': 60,
    'sql_mode': 'TRADITIONAL'
}

def main():
    # 1. 读取 CSV 文件
    csv_path = 'KEGG_summary_final_replaced.csv'
    
    # 检查文件是否存在
    if not os.path.exists(csv_path):
        print(f"错误: 文件 '{csv_path}' 不存在")
        return
    
    # 使用更严格的参数读取CSV
    df = pd.read_csv(csv_path,
                     skipinitialspace=True,
                     skip_blank_lines=True)

    # 检查并删除任何空行或Pathway_Description为NaN的行
    df = df.dropna(subset=['Pathway_Description'])

    print(f"读取到 {len(df)} 行有效数据")
    print(f"列名: {list(df.columns)}")

    # 2. 连接 MySQL 数据库
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()

    # 3. 检查表是否存在，如果存在则删除
    cursor.execute("SHOW TABLES LIKE 'keggPathwaySummaryLast'")
    if cursor.fetchone():
        print("删除已存在的表: keggPathwaySummaryLast")
        cursor.execute("DROP TABLE keggPathwaySummaryLast")
        conn.commit()

    # 4. 创建数据表，使用Pathway_Description作为主键
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS keggPathwaySummaryLast (
        pathwayDescription VARCHAR(500) PRIMARY KEY,
        translationProjectIds TEXT,
        FULLTEXT INDEX ft_pathway_search (pathwayDescription, translationProjectIds)
    )
    """
    cursor.execute(create_table_sql)
    print("通路联合结果表创建成功！")

    # 5. 准备插入数据的 SQL 语句
    insert_sql = """
    INSERT INTO keggPathwaySummaryLast (pathwayDescription, translationProjectIds)
    VALUES (%s, %s)
    ON DUPLICATE KEY UPDATE
        translationProjectIds = VALUES(translationProjectIds)
    """

    # 6. 分批处理数据以避免连接超时
    batch_size = 100  # 由于数据量较小(367行)，使用较小的批次
    total_rows = len(df)
    successful_imports = 0
    
    print(f"开始分批导入数据，批次大小: {batch_size}")
    
    for i in range(0, total_rows, batch_size):
        batch_end = min(i + batch_size, total_rows)
        batch_df = df.iloc[i:batch_end]
        
        print(f"处理批次 {i//batch_size + 1}: 行 {i+1} 到 {batch_end}")
        
        # 准备当前批次的数据
        data_to_insert = []
        for index, row in batch_df.iterrows():
            try:
                # 构建数据元组，处理NaN值
                data_tuple = (
                    row['Pathway_Description'] if not pd.isna(row['Pathway_Description']) else None,
                    row['translation_project_ids'] if not pd.isna(row['translation_project_ids']) else None
                )

                # 只添加有效的记录（至少有pathway_description）
                if data_tuple[0] is not None:
                    data_to_insert.append(data_tuple)

            except Exception as e:
                print(f"跳过行 {index}, 错误: {e}")
                continue
        
        # 批量插入当前批次
        if data_to_insert:
            try:
                # 重新连接数据库以避免连接超时
                if not conn.is_connected():
                    conn = mysql.connector.connect(**DB_CONFIG)
                    cursor = conn.cursor()
                
                cursor.executemany(insert_sql, data_to_insert)
                conn.commit()
                successful_imports += len(data_to_insert)
                print(f"批次 {i//batch_size + 1} 成功导入 {len(data_to_insert)} 条记录")
                
            except mysql.connector.Error as err:
                print(f"批次 {i//batch_size + 1} 导入失败: {err}")
                # 尝试单条插入
                single_success = 0
                for data_tuple in data_to_insert:
                    try:
                        cursor.execute(insert_sql, data_tuple)
                        conn.commit()
                        single_success += 1
                    except mysql.connector.Error as single_err:
                        print(f"单条记录导入失败: {single_err}")
                        print(f"失败的记录: {data_tuple[0]}")
                successful_imports += single_success
                print(f"批次 {i//batch_size + 1} 单条导入成功 {single_success} 条记录")
        
        # 短暂休息以避免数据库过载
        time.sleep(0.05)

    print(f"\n总共成功导入 {successful_imports} / {total_rows} 条记录")

    # 7. 查看导入后的数据统计
    try:
        cursor.execute("SELECT COUNT(*) FROM keggPathwaySummaryLast")
        total_count = cursor.fetchone()[0]
        print(f"数据库中总共有 {total_count} 条通路联合结果记录")

        # 查看一些示例记录
        cursor.execute("""
            SELECT pathwayDescription,
                   SUBSTRING(translationProjectIds, 1, 100) as project_ids_preview
            FROM keggPathwaySummaryLast
            LIMIT 5
        """)
        sample_records = cursor.fetchall()
        print("\n前5条记录预览:")
        for record in sample_records:
            print(f"  通路: {record[0]}")
            print(f"  项目ID: {record[1]}...")
            print("  ---")

    except mysql.connector.Error as err:
        print(f"查询统计信息时出错: {err}")

    # 8. 关闭连接
    cursor.close()
    conn.close()
    print("\n通路联合结果数据导入完成！")

if __name__ == '__main__':
    main() 