import pandas as pd
import mysql.connector
import os

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306,
}

def main():
    # 1. 读取 CSV 文件
    csv_path = 'KEGG_final_with_info_last.csv'
    
    # 检查文件是否存在
    if not os.path.exists(csv_path):
        print(f"错误: 文件 '{csv_path}' 不存在")
        return
    
    # 使用更严格的参数读取CSV
    df = pd.read_csv(csv_path, 
                     skipinitialspace=True,
                     skip_blank_lines=True,
                     quoting=1)  # QUOTE_ALL模式，处理引号
    
    # 检查并删除任何空行或Gene symbol为NaN的行
    df = df.dropna(subset=['Gene symbol'])
    
    print(f"读取到 {len(df)} 行有效数据")
    
    # 2. 连接 MySQL 数据库
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()

    # 3. 检查表是否存在，如果存在则删除
    cursor.execute("SHOW TABLES LIKE 'keggAnnotation'")
    if cursor.fetchone():
        print("删除已存在的表: keggAnnotation")
        cursor.execute("DROP TABLE keggAnnotation")
        conn.commit()

    # 4. 创建数据表（使用驼峰命名法），包含新增的三列
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS keggAnnotation (
        id INT AUTO_INCREMENT PRIMARY KEY,
        geneSymbol VARCHAR(100),
        geneId VARCHAR(100),
        pathwayDescription VARCHAR(500),
        pathwayId VARCHAR(20),
        tissueCellType TEXT,
        cellLine TEXT,
        disease TEXT,
        INDEX idx_gene_symbol (geneSymbol),
        INDEX idx_gene_id (geneId),
        INDEX idx_pathway_desc (pathwayDescription(255)),
        INDEX idx_pathway_id (pathwayId),
        FULLTEXT INDEX ft_kegg_search (geneSymbol, geneId, pathwayDescription, pathwayId, tissueCellType, cellLine, disease)
    )
    """
    cursor.execute(create_table_sql)
    print("KEGG注释表创建成功！")

    # 5. 准备插入数据的 SQL 语句，包含新增的三列
    insert_sql = """
    INSERT INTO keggAnnotation (geneSymbol, geneId, pathwayDescription, pathwayId, tissueCellType, cellLine, disease)
    VALUES (%s, %s, %s, %s, %s, %s, %s)
    """

    # 6. 遍历 DataFrame 并组织数据元组列表
    data_to_insert = []
    for index, row in df.iterrows():
        try:
            # 构建数据元组，处理NaN值，包含新增的三列
            data_tuple = (
                row['Gene symbol'] if not pd.isna(row['Gene symbol']) else None,
                row['Gene_ID'] if not pd.isna(row['Gene_ID']) else None,
                row['Pathway_Description'] if not pd.isna(row['Pathway_Description']) else None,
                row['Pathway_ID'] if not pd.isna(row['Pathway_ID']) else None,
                row['Tissue/Cell Type'] if not pd.isna(row['Tissue/Cell Type']) else None,
                row['Cell line'] if not pd.isna(row['Cell line']) else None,
                row['Disease'] if not pd.isna(row['Disease']) else None
            )
            
            # 只添加有效的记录（至少有gene_symbol）
            if data_tuple[0] is not None:
                data_to_insert.append(data_tuple)
            else:
                print(f"跳过行 {index}: Gene symbol为空")
                
        except (ValueError, TypeError) as e:
            print(f"跳过行 {index}, 原因: {e}")
            continue
        except Exception as e:
            print(f"跳过行 {index}, 未知错误: {e}")
            continue

    print(f"准备插入 {len(data_to_insert)} 条记录到数据库")
    
    # 7. 批量插入数据
    try:
        cursor.executemany(insert_sql, data_to_insert)
        conn.commit()
        print(f"成功导入 {len(data_to_insert)} 条KEGG注释数据")
    except mysql.connector.Error as err:
        print(f"导入数据时出错: {err}")
        # 单条插入以确定哪些记录有问题
        successful = 0
        for i, data_tuple in enumerate(data_to_insert):
            try:
                cursor.execute(insert_sql, data_tuple)
                conn.commit()
                successful += 1
            except mysql.connector.Error as err:
                print(f"无法导入第 {i+1} 行数据: {err}")
        print(f"成功导入 {successful} / {len(data_to_insert)} 条记录")

    # 8. 查看导入后的数据统计
    cursor.execute("SELECT COUNT(*) FROM keggAnnotation")
    total_count = cursor.fetchone()[0]
    print(f"数据库中总共有 {total_count} 条KEGG注释记录")
    
    # 查看前10个最常见的pathway
    cursor.execute("""
        SELECT pathwayDescription, pathwayId, COUNT(*) as gene_count 
        FROM keggAnnotation 
        GROUP BY pathwayDescription, pathwayId
        ORDER BY gene_count DESC
        LIMIT 10
    """)
    pathway_stats = cursor.fetchall()
    print("\n前10个最常见的KEGG通路:")
    for pathway_desc, pathway_id, count in pathway_stats:
        print(f"  {pathway_id}: {pathway_desc} ({count} 个基因)")
    
    # 查看基因总数
    cursor.execute("SELECT COUNT(DISTINCT geneSymbol) FROM keggAnnotation")
    unique_genes = cursor.fetchone()[0]
    print(f"\n涉及的基因总数: {unique_genes}")
    
    # 查看通路总数
    cursor.execute("SELECT COUNT(DISTINCT pathwayId) FROM keggAnnotation")
    unique_pathways = cursor.fetchone()[0]
    print(f"涉及的通路总数: {unique_pathways}")

    # 9. 关闭连接
    cursor.close()
    conn.close()
    print("\nKEGG注释数据导入完成！")

if __name__ == '__main__':
    main() 