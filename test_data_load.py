#!/usr/bin/env python3
"""
测试数据加载脚本，用于诊断numpy递归错误
"""

import sys
import os

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    try:
        import pandas as pd
        print("✓ pandas导入成功")
        
        import numpy as np
        print("✓ numpy导入成功")
        
        # 测试numpy基本功能
        arr = np.array([1, 2, 3])
        print(f"✓ numpy数组创建成功: {arr}")
        
        return True
    except Exception as e:
        print(f"✗ 基本导入失败: {e}")
        return False

def test_file_existence():
    """测试文件是否存在"""
    print("\n测试文件存在性...")
    file_path = "gene/gene_with_translation_indices.csv"
    
    if os.path.exists(file_path):
        print(f"✓ 文件存在: {file_path}")
        
        # 获取文件大小
        size = os.path.getsize(file_path)
        print(f"✓ 文件大小: {size} bytes")
        
        return True
    else:
        print(f"✗ 文件不存在: {file_path}")
        return False

def test_simple_csv_read():
    """测试简单CSV读取"""
    print("\n测试简单CSV读取...")
    try:
        import pandas as pd
        
        file_path = "gene/gene_with_translation_indices.csv"
        
        # 尝试只读取第一行（表头）
        print("读取表头...")
        df_header = pd.read_csv(file_path, nrows=0)
        print(f"✓ 表头读取成功，列名: {list(df_header.columns)}")
        
        # 尝试读取前几行，使用最安全的设置
        print("读取前3行数据...")
        df_sample = pd.read_csv(file_path, nrows=3, dtype=str, engine='python')
        print(f"✓ 样本数据读取成功，形状: {df_sample.shape}")
        print("样本数据:")
        print(df_sample)
        
        return True
    except Exception as e:
        print(f"✗ CSV读取失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment():
    """测试环境信息"""
    print("\n环境信息:")
    print(f"Python版本: {sys.version}")
    
    try:
        import pandas as pd
        print(f"Pandas版本: {pd.__version__}")
    except:
        print("无法获取Pandas版本")
    
    try:
        import numpy as np
        print(f"Numpy版本: {np.__version__}")
    except:
        print("无法获取Numpy版本")

def main():
    """主函数"""
    print("=== 数据加载诊断测试 ===")
    
    # 测试环境
    test_environment()
    
    # 测试基本导入
    if not test_basic_imports():
        return
    
    # 测试文件存在性
    if not test_file_existence():
        return
    
    # 测试CSV读取
    if not test_simple_csv_read():
        return
    
    print("\n=== 所有测试通过 ===")

if __name__ == "__main__":
    main()
