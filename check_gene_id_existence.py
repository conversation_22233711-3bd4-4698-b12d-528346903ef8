#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查 unique_gene_id_symbol_mapping.csv 中的 GENE ID 是否存在于 
ensembl_gene_id_unique.csv 中，将不存在的记录输出到新的CSV文件。
"""

import pandas as pd
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('check_gene_id_existence.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

FIRST_FILE = 'unique_gene_id_symbol_mapping.csv'
SECOND_FILE = 'ensembl_gene_id_unique.csv'
OUTPUT_FILE = 'gene_ids_not_in_ensembl.csv'

def check_gene_id_existence():
    """检查基因ID是否存在于参考文件中"""
    try:
        # 读取第一个文件（包含GENE ID和GENE symbol）
        logging.info(f'读取第一个文件: {FIRST_FILE}')
        first_df = pd.read_csv(FIRST_FILE)
        logging.info(f'第一个文件共 {len(first_df)} 行')
        
        # 读取第二个文件（包含ensembl_gene_id）
        logging.info(f'读取第二个文件: {SECOND_FILE}')
        second_df = pd.read_csv(SECOND_FILE)
        logging.info(f'第二个文件共 {len(second_df)} 行')
        
        # 创建第二个文件中ensembl_gene_id的集合（用于快速查找）
        ensembl_gene_ids = set(second_df['ensembl_gene_id'].astype(str))
        logging.info(f'第二个文件中唯一的ensembl_gene_id数量: {len(ensembl_gene_ids)}')
        
        # 检查第一个文件中的GENE ID是否存在于第二个文件中
        logging.info('开始检查GENE ID是否存在...')
        
        # 添加一个标记列来指示是否存在
        first_df['exists_in_ensembl'] = first_df['GENE ID'].astype(str).isin(ensembl_gene_ids)
        
        # 统计存在和不存在的数量
        exists_count = first_df['exists_in_ensembl'].sum()
        not_exists_count = len(first_df) - exists_count
        
        logging.info('=' * 60)
        logging.info('检查结果统计：')
        logging.info(f'总GENE ID数量: {len(first_df):,}')
        logging.info(f'存在于ensembl文件中的数量: {exists_count:,}')
        logging.info(f'不存在于ensembl文件中的数量: {not_exists_count:,}')
        logging.info(f'存在比例: {(exists_count / len(first_df)) * 100:.2f}%')
        logging.info(f'不存在比例: {(not_exists_count / len(first_df)) * 100:.2f}%')
        
        # 提取不存在的记录
        not_exists_df = first_df[~first_df['exists_in_ensembl']].copy()
        
        # 移除标记列，只保留原始的GENE ID和GENE symbol列
        not_exists_df = not_exists_df[['GENE ID', 'GENE symbol']]
        
        # 保存不存在的记录到新文件
        if not_exists_count > 0:
            logging.info(f'保存不存在的记录到 {OUTPUT_FILE}...')
            not_exists_df.to_csv(OUTPUT_FILE, index=False)
            
            # 显示一些不存在的样例
            logging.info('\n不存在的GENE ID样例（前10个）：')
            sample_count = min(10, len(not_exists_df))
            for idx, row in not_exists_df.head(sample_count).iterrows():
                logging.info(f'  {row["GENE ID"]} -> {row["GENE symbol"]}')
            
            if len(not_exists_df) > 10:
                logging.info(f'  ... 还有 {len(not_exists_df) - 10} 个不存在的记录')
        else:
            logging.info('所有GENE ID都存在于ensembl文件中，无需创建输出文件')
        
        # 检查是否有重复的GENE ID
        duplicated_gene_ids = first_df[first_df['GENE ID'].duplicated()]
        if len(duplicated_gene_ids) > 0:
            logging.warning(f'发现 {len(duplicated_gene_ids)} 个重复的GENE ID')
        
        logging.info('=' * 60)
        logging.info('检查完成！')
        if not_exists_count > 0:
            logging.info(f'不存在的记录已保存到: {OUTPUT_FILE}')
        
        return True
        
    except Exception as e:
        logging.error(f'处理过程中发生错误: {str(e)}')
        return False

if __name__ == '__main__':
    print('开始检查基因ID存在性...')
    success = check_gene_id_existence()
    if success:
        print('检查完成！')
    else:
        print('检查失败，请查看日志文件。') 