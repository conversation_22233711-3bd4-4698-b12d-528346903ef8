#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加Chromosome列到gene_count_by_project_results_with_info.csv文件
通过GENE ID匹配ensembl_gene_id_with_full_info_split_2_processed.csv文件获取Chromosome信息
"""

import pandas as pd
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('add_chromosome_column.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def main():
    try:
        # 文件路径
        main_file = 'gene_count_by_project_results_with_info.csv'
        reference_file = 'ensembl_gene_id_with_full_info_split_2_processed.csv'
        output_file = 'gene_count_by_project_results_with_info_with_chromosome.csv'
        
        logging.info("开始读取文件...")
        
        # 读取主要文件
        logging.info(f"正在读取主文件: {main_file}")
        main_df = pd.read_csv(main_file, dtype=str)
        logging.info(f"主文件读取完成，共有 {len(main_df)} 行数据")
        logging.info(f"主文件列名: {list(main_df.columns)}")
        
        # 读取参考文件，只需要GENE ID和Chromosome列
        logging.info(f"正在读取参考文件: {reference_file}")
        reference_df = pd.read_csv(reference_file, dtype=str, usecols=['GENE ID', 'Chromosome'])
        logging.info(f"参考文件读取完成，共有 {len(reference_df)} 行数据")
        
        # 去除重复的GENE ID，保留第一个
        reference_df = reference_df.drop_duplicates(subset='GENE ID', keep='first')
        logging.info(f"去除重复后，参考文件有 {len(reference_df)} 个唯一的GENE ID")
        
        # 检查主文件中的GENE ID列
        if 'GENE ID' not in main_df.columns:
            logging.error("主文件中没有找到'GENE ID'列")
            return
        
        # 统计匹配前的数据
        unique_gene_ids_main = main_df['GENE ID'].nunique()
        logging.info(f"主文件中有 {unique_gene_ids_main} 个唯一的GENE ID")
        
        # 执行左连接，通过GENE ID匹配
        logging.info("正在执行数据匹配...")
        merged_df = pd.merge(main_df, reference_df, on='GENE ID', how='left')
        
        # 统计匹配结果
        matched_count = merged_df['Chromosome'].notna().sum()
        total_count = len(merged_df)
        unmatched_count = total_count - matched_count
        
        logging.info(f"匹配完成:")
        logging.info(f"  总记录数: {total_count}")
        logging.info(f"  成功匹配到Chromosome的记录数: {matched_count}")
        logging.info(f"  未匹配到Chromosome的记录数: {unmatched_count}")
        logging.info(f"  匹配率: {(matched_count/total_count)*100:.2f}%")
        
        # 对于没有匹配到的记录，将Chromosome设为空字符串
        merged_df['Chromosome'] = merged_df['Chromosome'].fillna('')
        
        # 保存结果
        logging.info(f"正在保存结果到: {output_file}")
        merged_df.to_csv(output_file, index=False, encoding='utf-8')
        logging.info("文件保存完成")
        
        # 显示一些样本数据
        logging.info("样本数据预览:")
        sample_data = merged_df[['GENE ID', 'GENE symbol', 'Chromosome']].head(10)
        for idx, row in sample_data.iterrows():
            logging.info(f"  {row['GENE ID']} | {row['GENE symbol']} | {row['Chromosome']}")
        
        # 统计有多少个唯一的Chromosome
        unique_chromosomes = merged_df[merged_df['Chromosome'] != '']['Chromosome'].nunique()
        logging.info(f"匹配到的唯一Chromosome数量: {unique_chromosomes}")
        
        print(f"\n处理完成！")
        print(f"输出文件: {output_file}")
        print(f"总记录数: {total_count}")
        print(f"匹配成功: {matched_count} ({(matched_count/total_count)*100:.2f}%)")
        print(f"未匹配: {unmatched_count}")
        
    except Exception as e:
        logging.error(f"处理过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    start_time = datetime.now()
    logging.info("=" * 60)
    logging.info("开始添加Chromosome列处理")
    logging.info("=" * 60)
    
    main()
    
    end_time = datetime.now()
    duration = end_time - start_time
    logging.info("=" * 60)
    logging.info(f"处理完成，总耗时: {duration}")
    logging.info("=" * 60) 