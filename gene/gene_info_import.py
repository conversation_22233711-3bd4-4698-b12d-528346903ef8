#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
import sys
import time

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'autocommit': False,
    'connection_timeout': 600,  # 10分钟连接超时
    'use_unicode': True,
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gene_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def create_connection():
    """创建数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        logging.info("成功连接到MySQL数据库")
        return connection
    except Error as e:
        logging.error(f"连接数据库时出错: {e}")
        return None

def check_connection(connection):
    """检查并重新连接数据库"""
    try:
        if not connection.is_connected():
            logging.warning("数据库连接已断开，尝试重新连接...")
            connection.reconnect(attempts=3, delay=2)
            logging.info("数据库重新连接成功")
        return True
    except Error as e:
        logging.error(f"重新连接数据库失败: {e}")
        return False

def create_gene_table(connection):
    """创建gene表"""
    cursor = connection.cursor()

    try:
        # 删除已存在的表（如果存在）
        drop_table_query = "DROP TABLE IF EXISTS gene"
        cursor.execute(drop_table_query)
        logging.info("已删除现有的gene表（如果存在）")

        # 创建新表，添加TR、EVI、TE三列
        create_table_query = """
        CREATE TABLE gene (
            id INT AUTO_INCREMENT PRIMARY KEY,
            geneSymbol VARCHAR(255) NOT NULL,
            geneId VARCHAR(255) NOT NULL,
            projectId VARCHAR(255) NOT NULL,
            expressedTranscriptNumber INT,
            tissueCellType TEXT,
            cellLine VARCHAR(255),
            disease VARCHAR(255),
            chromosome TEXT,
            TR DECIMAL(10,6),
            EVI DECIMAL(10,6),
            TE DECIMAL(10,6)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        
        cursor.execute(create_table_query)
        logging.info("成功创建gene表")
        
        # 为主要列添加索引以提高检索速度
        indexes = [
            "CREATE INDEX idx_gene_symbol ON gene (geneSymbol)",
            "CREATE INDEX idx_gene_id ON gene (geneId)", 
            "CREATE INDEX idx_project_id ON gene (projectId)",
            "CREATE INDEX idx_chromosome ON gene (chromosome(50))",  # chromosome 索引，限制长度
            "CREATE INDEX idx_gene_project ON gene (geneSymbol, projectId)"  # 复合索引
        ]
        
        for index_query in indexes:
            cursor.execute(index_query)
            logging.info(f"成功创建索引: {index_query.split('INDEX ')[1].split(' ON')[0]}")
        
        # 提交事务
        connection.commit()
        
    except Error as e:
        logging.error(f"创建表时出错: {e}")
        connection.rollback()
        raise
    finally:
        cursor.close()

def import_gene_data(connection, csv_file_path):
    """导入基因数据到数据库"""
    try:
        # 读取CSV文件
        logging.info(f"开始读取CSV文件: {csv_file_path}")
        df = pd.read_csv(csv_file_path)
        
        # 显示数据基本信息
        logging.info(f"CSV文件包含 {len(df)} 行数据")
        logging.info(f"列名: {list(df.columns)}")
        
        # 清理数据 - 处理NaN值
        df = df.fillna('')  # 将NaN值替换为空字符串
        
        # 准备插入语句，添加TR、EVI、TE三列
        insert_query = """
        INSERT INTO gene (
            geneSymbol, geneId, projectId, expressedTranscriptNumber,
            tissueCellType, cellLine, disease, chromosome, TR, EVI, TE
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        # 批量插入数据
        batch_size = 5000  # 减小批次大小以避免超时
        total_rows = len(df)
        success_count = 0
        
        for i in range(0, total_rows, batch_size):
            # 检查连接状态
            if not check_connection(connection):
                logging.error("数据库连接检查失败，停止导入")
                break
                
            batch_df = df.iloc[i:i+batch_size]
            cursor = connection.cursor()
            
            try:
                # 准备批量数据
                batch_data = []
                for _, row in batch_df.iterrows():
                    # 处理 expressedTranscriptNumber，确保是整数
                    transcript_num = row['expressedTranscriptNumber']
                    if pd.isna(transcript_num) or transcript_num == '':
                        transcript_num = None
                    else:
                        try:
                            transcript_num = int(transcript_num)
                        except (ValueError, TypeError):
                            transcript_num = None

                    # 处理TR、EVI、TE值，确保是浮点数或None
                    def process_float_value(value):
                        if pd.isna(value) or value == '':
                            return None
                        try:
                            return float(value)
                        except (ValueError, TypeError):
                            return None

                    tr_value = process_float_value(row['TR'])
                    evi_value = process_float_value(row['EVI'])
                    te_value = process_float_value(row['TE'])

                    batch_data.append((
                        str(row['geneSymbol']),
                        str(row['geneId']),
                        str(row['projectId']),
                        transcript_num,
                        str(row['tissueCellType']),
                        str(row['cellLine']),
                        str(row['disease']),
                        str(row['chromosome']),
                        tr_value,
                        evi_value,
                        te_value
                    ))
                
                # 执行批量插入
                cursor.executemany(insert_query, batch_data)
                connection.commit()
                
                success_count += len(batch_data)
                logging.info(f"已导入 {min(i + batch_size, total_rows)}/{total_rows} 行数据 (成功: {success_count})")
                
                # 每10个批次休息一下，避免连接超时
                if (i // batch_size + 1) % 10 == 0:
                    time.sleep(0.1)
                
            except Error as e:
                logging.error(f"批次 {i//batch_size + 1} 导入失败: {e}")
                connection.rollback()
                # 继续处理下一批次
                continue
            finally:
                cursor.close()
        
        logging.info(f"数据导入完成！总共成功导入 {success_count} 行数据")
        
        # 显示统计信息
        show_statistics(connection)
        
    except Exception as e:
        logging.error(f"导入数据时出错: {e}")
        raise

def show_statistics(connection):
    """显示导入统计信息"""
    if not check_connection(connection):
        logging.error("无法连接数据库，跳过统计信息显示")
        return
        
    cursor = connection.cursor()
    
    try:
        # 总记录数
        cursor.execute("SELECT COUNT(*) FROM gene")
        total_count = cursor.fetchone()[0]
        logging.info(f"表中总记录数: {total_count}")
        
        # 唯一基因数
        cursor.execute("SELECT COUNT(DISTINCT geneSymbol) FROM gene")
        unique_genes = cursor.fetchone()[0]
        logging.info(f"唯一基因数: {unique_genes}")
        
        # 唯一项目数
        cursor.execute("SELECT COUNT(DISTINCT projectId) FROM gene")
        unique_projects = cursor.fetchone()[0]
        logging.info(f"唯一项目数: {unique_projects}")
        
        # 按疾病分组统计
        cursor.execute("""
            SELECT disease, COUNT(*) as count 
            FROM gene 
            WHERE disease != '' 
            GROUP BY disease 
            ORDER BY count DESC 
            LIMIT 10
        """)
        
        disease_stats = cursor.fetchall()
        logging.info("前10种疾病类型统计:")
        for disease, count in disease_stats:
            logging.info(f"  {disease}: {count}")
        
        # 按染色体分组统计
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN chromosome LIKE 'Chromosome %:%' THEN 
                        SUBSTRING_INDEX(SUBSTRING_INDEX(chromosome, ':', 1), ' ', -1)
                    ELSE 'Unknown'
                END as chr_name,
                COUNT(*) as count 
            FROM gene 
            WHERE chromosome != '' 
            GROUP BY chr_name 
            ORDER BY 
                CASE 
                    WHEN chr_name REGEXP '^[0-9]+$' THEN CAST(chr_name AS UNSIGNED)
                    WHEN chr_name = 'X' THEN 23
                    WHEN chr_name = 'Y' THEN 24
                    WHEN chr_name = 'MT' THEN 25
                    ELSE 26
                END
            LIMIT 25
        """)
        
        chromosome_stats = cursor.fetchall()
        logging.info("按染色体分组统计:")
        for chromosome, count in chromosome_stats:
            logging.info(f"  Chromosome {chromosome}: {count}")
        
        # 染色体数据完整性统计
        cursor.execute("SELECT COUNT(*) FROM gene WHERE chromosome != ''")
        chromosome_count = cursor.fetchone()[0]
        logging.info(f"有染色体信息的记录数: {chromosome_count} ({chromosome_count/total_count*100:.2f}%)")

        # 翻译指标数据完整性统计
        cursor.execute("SELECT COUNT(*) FROM gene WHERE TR IS NOT NULL")
        tr_count = cursor.fetchone()[0]
        logging.info(f"有TR值的记录数: {tr_count} ({tr_count/total_count*100:.2f}%)")

        cursor.execute("SELECT COUNT(*) FROM gene WHERE EVI IS NOT NULL")
        evi_count = cursor.fetchone()[0]
        logging.info(f"有EVI值的记录数: {evi_count} ({evi_count/total_count*100:.2f}%)")

        cursor.execute("SELECT COUNT(*) FROM gene WHERE TE IS NOT NULL")
        te_count = cursor.fetchone()[0]
        logging.info(f"有TE值的记录数: {te_count} ({te_count/total_count*100:.2f}%)")

        cursor.execute("SELECT COUNT(*) FROM gene WHERE TR IS NOT NULL OR EVI IS NOT NULL OR TE IS NOT NULL")
        any_translation_count = cursor.fetchone()[0]
        logging.info(f"至少有一个翻译指标的记录数: {any_translation_count} ({any_translation_count/total_count*100:.2f}%)")

    except Error as e:
        logging.error(f"显示统计信息时出错: {e}")
    finally:
        cursor.close()

def main():
    """主函数"""
    csv_file_path = "gene_count_by_project_results_with_chromosome.csv"
    
    # 检查文件是否存在
    import os
    if not os.path.exists(csv_file_path):
        logging.error(f"CSV文件不存在: {csv_file_path}")
        return
    
    # 创建数据库连接
    connection = create_connection()
    if connection is None:
        logging.error("无法连接到数据库，程序退出")
        return
    
    try:
        # 创建表
        create_gene_table(connection)
        
        # 导入数据
        import_gene_data(connection, csv_file_path)
        
        logging.info("基因数据导入任务完成！")
        
    except Exception as e:
        logging.error(f"程序执行出错: {e}")
    finally:
        if connection and connection.is_connected():
            connection.close()
            logging.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 