# gene_info_import.py 修改总结

## 修改目的
将数据源从 `gene_count_by_project_results_with_info_with_chromosome.csv` 换成 `Gene_TPM/gene_with_translation_indices.csv`，并添加 TR、EVI、TE 三列的导入功能。

## 主要修改内容

### 1. 数据库表结构修改
**位置**: `create_gene_table()` 函数中的 `create_table_query`

**修改前**:
```sql
CREATE TABLE gene (
    id INT AUTO_INCREMENT PRIMARY KEY,
    geneSymbol VARCHAR(255) NOT NULL,
    geneId VARCHAR(255) NOT NULL,
    projectId VARCHAR(255) NOT NULL,
    expressedTranscriptNumber INT,
    tissueCellType TEXT,
    cellLine VARCHAR(255),
    disease VARCHAR(255),
    chromosome TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
```

**修改后**:
```sql
CREATE TABLE gene (
    id INT AUTO_INCREMENT PRIMARY KEY,
    geneSymbol VARCHAR(255) NOT NULL,
    geneId VARCHAR(255) NOT NULL,
    projectId VARCHAR(255) NOT NULL,
    expressedTranscriptNumber INT,
    tissueCellType TEXT,
    cellLine VARCHAR(255),
    disease VARCHAR(255),
    chromosome TEXT,
    TR DECIMAL(10,6),
    EVI DECIMAL(10,6),
    TE DECIMAL(10,6)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
```

### 2. 插入语句修改
**位置**: `import_gene_data()` 函数中的 `insert_query`

**修改前**:
```sql
INSERT INTO gene (
    geneSymbol, geneId, projectId, expressedTranscriptNumber,
    tissueCellType, cellLine, disease, chromosome
) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
```

**修改后**:
```sql
INSERT INTO gene (
    geneSymbol, geneId, projectId, expressedTranscriptNumber,
    tissueCellType, cellLine, disease, chromosome, TR, EVI, TE
) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
```

### 3. 列名映射修改
**位置**: `import_gene_data()` 函数中的数据处理部分

**修改前** (旧CSV文件的列名):
- `'GENE symbol'` → `geneSymbol`
- `'GENE ID'` → `geneId`
- `'Project id'` → `projectId`
- `'Expressed Transcript Number'` → `expressedTranscriptNumber`
- `'Tissue/Cell Type'` → `tissueCellType`
- `'Cell line'` → `cellLine`
- `'Disease'` → `disease`
- `'Chromosome'` → `chromosome`

**修改后** (新CSV文件的列名):
- `'geneSymbol'` → `geneSymbol`
- `'geneId'` → `geneId`
- `'projectId'` → `projectId`
- `'expressedTranscriptNumber'` → `expressedTranscriptNumber`
- `'tissueCellType'` → `tissueCellType`
- `'cellLine'` → `cellLine`
- `'disease'` → `disease`
- `'chromosome'` → `chromosome`
- `'TR'` → `TR` (新增)
- `'EVI'` → `EVI` (新增)
- `'TE'` → `TE` (新增)

### 4. 数据处理逻辑增强
**位置**: `import_gene_data()` 函数中的批量数据处理

**新增功能**:
- 添加了翻译指标的数据类型转换函数 `process_float_value()`
- 对 TR、EVI、TE 三列进行浮点数转换和空值处理
- 在数据插入时包含这三个新字段

### 5. 文件路径修改
**位置**: `main()` 函数

**修改前**:
```python
csv_file_path = "gene_count_by_project_results_with_info_with_chromosome.csv"
```

**修改后**:
```python
csv_file_path = "Gene_TPM/gene_with_translation_indices.csv"
```

### 6. 统计信息增强
**位置**: `show_statistics()` 函数

**新增统计**:
- TR值的记录数和百分比
- EVI值的记录数和百分比
- TE值的记录数和百分比
- 至少有一个翻译指标的记录数和百分比

## 数据类型说明

### 翻译指标字段
- **TR** (Translation Rate): 翻译速率，DECIMAL(10,6) 类型
- **EVI** (Translation Efficiency Index): 翻译效率指数，DECIMAL(10,6) 类型
- **TE** (Translation Efficiency): 翻译效率，DECIMAL(10,6) 类型

### 数据处理
- 所有翻译指标字段允许 NULL 值
- 在导入时会自动处理空值和无效值
- 无效的数值会被转换为 NULL

## 预期数据统计
根据之前的处理结果，预期的数据分布：
- 总记录数: 1,349,245 行
- TR值: 约 173,736 行 (12.9%)
- EVI值: 约 71,817 行 (5.3%)
- TE值: 约 1,190,004 行 (88.2%)
- 至少一个指标: 约 1,291,923 行 (95.8%)

## 使用方法
运行修改后的脚本：
```bash
python3 gene/gene_info_import.py
```

确保 `Gene_TPM/gene_with_translation_indices.csv` 文件存在于正确的路径下。
