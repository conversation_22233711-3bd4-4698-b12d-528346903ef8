#!/usr/bin/env python3

import csv
from collections import defaultdict

def read_file1_mappings():
    """读取gene_count_by_project_results_with_chromosome.csv中的基因ID-符号映射"""
    mappings = {}
    try:
        with open('gene_count_by_project_results_with_chromosome.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            print(f"文件1列名: {reader.fieldnames}")
            for row in reader:
                gene_id = row['geneId']
                gene_symbol = row['geneSymbol']
                if gene_id not in mappings:
                    mappings[gene_id] = gene_symbol
                elif mappings[gene_id] != gene_symbol:
                    print(f"警告：文件1中基因ID {gene_id} 有不同的符号: {mappings[gene_id]} vs {gene_symbol}")
    except FileNotFoundError:
        print("错误：找不到文件 gene_count_by_project_results_with_chromosome.csv")
        return None
    except KeyError as e:
        print(f"错误：文件1中找不到列 {e}")
        return None
    except Exception as e:
        print(f"读取文件1时出错: {e}")
        return None
    return mappings

def read_file2_mappings():
    """读取unique_genes.csv中的基因ID-符号映射"""
    mappings = {}
    try:
        with open('unique_genes.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            print(f"文件2列名: {reader.fieldnames}")
            for row in reader:
                gene_id = row['ensembl_gene_id']
                gene_symbol = row['external_gene_name']
                mappings[gene_id] = gene_symbol
    except FileNotFoundError:
        print("错误：找不到文件 unique_genes.csv")
        return None
    except KeyError as e:
        print(f"错误：文件2中找不到列 {e}")
        return None
    except Exception as e:
        print(f"读取文件2时出错: {e}")
        return None
    return mappings

def main():
    print("=== 基因ID与基因符号对应关系比较 ===")
    
    print("读取文件1...")
    mappings1 = read_file1_mappings()
    if mappings1 is None:
        return
    print(f"文件1中的基因ID数量: {len(mappings1)}")

    print("读取文件2...")
    mappings2 = read_file2_mappings()
    if mappings2 is None:
        return
    print(f"文件2中的基因ID数量: {len(mappings2)}")
    
    # 找出共同的基因ID
    common_ids = set(mappings1.keys()) & set(mappings2.keys())
    print(f"共同的基因ID数量: {len(common_ids)}")
    
    # 只在某个文件中的基因ID
    only_in_1 = set(mappings1.keys()) - set(mappings2.keys())
    only_in_2 = set(mappings2.keys()) - set(mappings1.keys())
    
    print(f"只在文件1中的基因ID数量: {len(only_in_1)}")
    print(f"只在文件2中的基因ID数量: {len(only_in_2)}")
    
    # 检查符号一致性
    consistent = 0
    inconsistent = 0
    inconsistent_examples = []
    
    for gene_id in common_ids:
        symbol1 = mappings1[gene_id]
        symbol2 = mappings2[gene_id]
        
        if symbol1 == symbol2:
            consistent += 1
        else:
            inconsistent += 1
            if len(inconsistent_examples) < 10:
                inconsistent_examples.append((gene_id, symbol1, symbol2))
    
    print(f"\n符号一致的基因ID数量: {consistent}")
    print(f"符号不一致的基因ID数量: {inconsistent}")
    
    if inconsistent_examples:
        print("\n符号不一致的例子（前10个）:")
        for gene_id, sym1, sym2 in inconsistent_examples:
            print(f"  {gene_id}: '{sym1}' vs '{sym2}'")
    
    # 计算一致性百分比
    if len(common_ids) > 0:
        consistency_rate = (consistent / len(common_ids)) * 100
        print(f"\n共同基因ID的符号一致性: {consistency_rate:.2f}%")
    
    # 显示一些样本数据
    print(f"\n=== 样本数据 ===")
    print("文件1中的前5个映射:")
    for i, (gene_id, symbol) in enumerate(list(mappings1.items())[:5]):
        print(f"  {gene_id} -> {symbol}")
    
    print("文件2中的前5个映射:")
    for i, (gene_id, symbol) in enumerate(list(mappings2.items())[:5]):
        print(f"  {gene_id} -> {symbol}")
    
    print("\n比较完成！")

if __name__ == "__main__":
    main()
