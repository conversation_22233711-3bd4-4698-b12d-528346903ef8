#!/usr/bin/env python3
"""
比较和更新基因ID脚本

比较unique_gene_id_symbol_mapping.csv和ERR3367723_final_merged_tpm.txt文件中的基因ID，
统计不同的数量，并用第二个文件的值更新第一个文件。
"""

import pandas as pd
import logging
from datetime import datetime
import os

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('compare_and_update_gene_ids.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def load_files():
    """加载两个文件"""
    try:
        # 加载unique_gene_id_symbol_mapping.csv
        logging.info("正在加载unique_gene_id_symbol_mapping.csv...")
        mapping_df = pd.read_csv('unique_gene_id_symbol_mapping.csv')
        logging.info(f"mapping文件加载完成，共{len(mapping_df)}行")
        
        # 加载ERR3367723_final_merged_tpm.txt
        logging.info("正在加载ERR3367723_final_merged_tpm.txt...")
        tpm_df = pd.read_csv('ERR3367723_final_merged_tpm.txt', sep='\t')
        logging.info(f"TPM文件加载完成，共{len(tpm_df)}行")
        
        return mapping_df, tpm_df
        
    except Exception as e:
        logging.error(f"加载文件时出错: {str(e)}")
        raise

def compare_gene_ids(mapping_df, tpm_df):
    """比较两个文件中的基因ID"""
    try:
        # 获取两个文件中的唯一基因ID
        mapping_gene_ids = set(mapping_df['GENE ID'].dropna())
        tpm_gene_ids = set(tpm_df['ensembl_gene_id'].dropna())
        
        logging.info(f"mapping文件中唯一基因ID数量: {len(mapping_gene_ids)}")
        logging.info(f"TPM文件中唯一基因ID数量: {len(tpm_gene_ids)}")
        
        # 找出在mapping文件中但不在TPM文件中的基因ID
        only_in_mapping = mapping_gene_ids - tpm_gene_ids
        # 找出在TPM文件中但不在mapping文件中的基因ID
        only_in_tpm = tpm_gene_ids - mapping_gene_ids
        # 找出两个文件都有的基因ID
        common_gene_ids = mapping_gene_ids & tpm_gene_ids
        
        logging.info(f"仅在mapping文件中的基因ID数量: {len(only_in_mapping)}")
        logging.info(f"仅在TPM文件中的基因ID数量: {len(only_in_tpm)}")
        logging.info(f"两个文件共有的基因ID数量: {len(common_gene_ids)}")
        
        # 保存差异基因ID到文件
        if only_in_mapping:
            only_mapping_df = pd.DataFrame({'GENE_ID_only_in_mapping': list(only_in_mapping)})
            only_mapping_df.to_csv('gene_ids_only_in_mapping.csv', index=False)
            logging.info("仅在mapping文件中的基因ID已保存到gene_ids_only_in_mapping.csv")
        
        if only_in_tpm:
            only_tpm_df = pd.DataFrame({'GENE_ID_only_in_tpm': list(only_in_tpm)})
            only_tpm_df.to_csv('gene_ids_only_in_tpm.csv', index=False)
            logging.info("仅在TPM文件中的基因ID已保存到gene_ids_only_in_tpm.csv")
        
        return only_in_mapping, only_in_tpm, common_gene_ids
        
    except Exception as e:
        logging.error(f"比较基因ID时出错: {str(e)}")
        raise

def update_mapping_with_tpm_data(mapping_df, tpm_df, only_in_tpm):
    """使用TPM文件中的数据更新mapping文件"""
    try:
        # 从TPM文件中获取基因ID和基因符号的映射
        tpm_mapping = tpm_df[['ensembl_gene_id', 'external_gene_name']].drop_duplicates()
        tpm_mapping = tpm_mapping.dropna()
        
        logging.info(f"TPM文件中有效的基因ID-符号映射数量: {len(tpm_mapping)}")
        
        # 创建更新后的mapping数据框
        updated_mapping_df = mapping_df.copy()
        
        # 添加TPM文件中独有的基因ID
        new_rows = []
        for gene_id in only_in_tpm:
            gene_symbol_row = tpm_mapping[tpm_mapping['ensembl_gene_id'] == gene_id]
            if not gene_symbol_row.empty:
                gene_symbol = gene_symbol_row['external_gene_name'].iloc[0]
                new_rows.append({'GENE ID': gene_id, 'GENE symbol': gene_symbol})
        
        if new_rows:
            new_rows_df = pd.DataFrame(new_rows)
            updated_mapping_df = pd.concat([updated_mapping_df, new_rows_df], ignore_index=True)
            logging.info(f"添加了{len(new_rows)}个新的基因ID-符号映射")
        
        # 检查是否有基因符号需要更新
        updated_count = 0
        for index, row in updated_mapping_df.iterrows():
            gene_id = row['GENE ID']
            current_symbol = row['GENE symbol']
            
            # 在TPM文件中查找对应的基因符号
            tpm_symbol_row = tpm_mapping[tpm_mapping['ensembl_gene_id'] == gene_id]
            if not tpm_symbol_row.empty:
                tpm_symbol = tpm_symbol_row['external_gene_name'].iloc[0]
                if pd.notna(tpm_symbol) and current_symbol != tpm_symbol:
                    logging.info(f"更新基因ID {gene_id} 的符号: {current_symbol} -> {tpm_symbol}")
                    updated_mapping_df.at[index, 'GENE symbol'] = tpm_symbol
                    updated_count += 1
        
        logging.info(f"更新了{updated_count}个基因符号")
        
        # 保存更新后的文件
        output_file = 'unique_gene_id_symbol_mapping_updated.csv'
        updated_mapping_df.to_csv(output_file, index=False)
        logging.info(f"更新后的映射文件已保存到{output_file}")
        
        return updated_mapping_df
        
    except Exception as e:
        logging.error(f"更新映射文件时出错: {str(e)}")
        raise

def main():
    """主函数"""
    logging.info("开始执行基因ID比较和更新任务")
    logging.info(f"当前工作目录: {os.getcwd()}")
    
    try:
        # 加载文件
        mapping_df, tpm_df = load_files()
        
        # 比较基因ID
        only_in_mapping, only_in_tpm, common_gene_ids = compare_gene_ids(mapping_df, tpm_df)
        
        # 输出统计信息
        logging.info("\n=== 比较结果统计 ===")
        logging.info(f"mapping文件中的唯一基因ID数量: {len(set(mapping_df['GENE ID'].dropna()))}")
        logging.info(f"TPM文件中的唯一基因ID数量: {len(set(tpm_df['ensembl_gene_id'].dropna()))}")
        logging.info(f"仅在mapping文件中的基因ID数量: {len(only_in_mapping)}")
        logging.info(f"仅在TPM文件中的基因ID数量: {len(only_in_tpm)}")
        logging.info(f"两个文件共有的基因ID数量: {len(common_gene_ids)}")
        
        # 如果有不同的基因ID，进行更新
        if only_in_tpm:
            logging.info(f"\n发现{len(only_in_tpm)}个TPM文件独有的基因ID，正在更新mapping文件...")
            updated_mapping_df = update_mapping_with_tpm_data(mapping_df, tpm_df, only_in_tpm)
            logging.info("更新完成")
        else:
            logging.info("没有发现需要更新的基因ID")
        
        logging.info("任务完成")
        
    except Exception as e:
        logging.error(f"执行过程中出错: {str(e)}")
        raise

if __name__ == "__main__":
    main() 