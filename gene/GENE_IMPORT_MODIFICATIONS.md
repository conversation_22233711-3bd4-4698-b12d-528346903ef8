# Gene Import Script Modifications

## 修改概述

已成功修改 `gene/gene_info_import.py` 脚本，以支持新的数据源文件 `gene_with_translation_indices.csv` 并添加对 TR、EVI、TE 三列翻译指标的导入。

## 主要修改内容

### 1. 数据源文件更改
- **原文件**: `gene_count_by_project_results_with_info_with_chromosome.csv`
- **新文件**: `gene_with_translation_indices.csv`

### 2. 数据库表结构更新
在 `create_gene_table()` 函数中添加了三个新列：
```sql
TR DECIMAL(10,6),
EVI DECIMAL(10,6),
TE DECIMAL(10,6)
```

### 3. 列名映射更新
更新了CSV列名映射以匹配新文件格式：

| 原列名 | 新列名 |
|--------|--------|
| `GENE symbol` | `geneSymbol` |
| `GENE ID` | `geneId` |
| `Project id` | `projectId` |
| `Expressed Transcript Number` | `expressedTranscriptNumber` |
| `Tissue/Cell Type` | `tissueCellType` |
| `Cell line` | `cellLine` |
| `Disease` | `disease` |
| `Chromosome` | `chromosome` |
| - | `TR` (新增) |
| - | `EVI` (新增) |
| - | `TE` (新增) |

### 4. 插入语句更新
更新了 INSERT 语句以包含新的三列：
```sql
INSERT INTO gene (
    geneSymbol, geneId, projectId, expressedTranscriptNumber,
    tissueCellType, cellLine, disease, chromosome, TR, EVI, TE
) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
```

### 5. 数据处理逻辑增强
添加了 `process_float_value()` 函数来处理 TR、EVI、TE 的浮点数值：
- 正确处理 NaN 值和空字符串
- 将无效值转换为 None（数据库中的 NULL）
- 确保数值类型正确性

### 6. 统计信息增强
在 `show_statistics()` 函数中添加了翻译指标的统计信息：
- TR 值的记录数和百分比
- EVI 值的记录数和百分比  
- TE 值的记录数和百分比
- 至少有一个翻译指标的记录数和百分比

## 数据类型说明

- **TR, EVI, TE**: 使用 `DECIMAL(10,6)` 类型，支持最多10位数字，其中6位小数
- **NULL 值处理**: 空值或无效值将存储为数据库 NULL

## 使用方法

1. 确保 `gene_with_translation_indices.csv` 文件存在于脚本同目录下
2. 运行脚本：
   ```bash
   python3 gene_info_import.py
   ```

## 预期结果

脚本将：
1. 创建包含11个字段的新 gene 表（原8个 + 新增3个翻译指标）
2. 导入所有数据，包括翻译指标
3. 显示详细的统计信息，包括翻译指标的覆盖率
4. 生成日志文件 `gene_import.log`

## 兼容性说明

- 保持了原有的所有功能和数据完整性
- 新增的翻译指标列允许 NULL 值，不影响现有数据
- 所有原有的索引和查询功能保持不变
