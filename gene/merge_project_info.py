import pandas as pd

def merge_project_info():
    """
    将Project_processed.csv中的项目信息合并到gene_count_by_project_results.csv中
    """
    print("正在读取文件...")
    
    # 读取基因统计结果文件
    gene_results = pd.read_csv('gene_count_by_project_results.csv')
    print(f"基因统计结果文件行数: {len(gene_results)}")
    print("基因统计结果文件列名:", gene_results.columns.tolist())
    
    # 读取项目信息文件
    project_info = pd.read_csv('Project_processed.csv')
    print(f"项目信息文件行数: {len(project_info)}")
    
    # 显示项目信息文件的列名
    print("项目信息文件列名:", project_info.columns.tolist())
    
    # 检查Project id的匹配情况
    gene_projects = set(gene_results['Project id'].unique())
    info_projects = set(project_info['Project ID'].unique())
    
    print(f"\n基因结果文件中的项目数: {len(gene_projects)}")
    print(f"项目信息文件中的项目数: {len(info_projects)}")
    
    # 找出匹配和不匹配的项目
    matched_projects = gene_projects.intersection(info_projects)
    unmatched_in_gene = gene_projects - info_projects
    unmatched_in_info = info_projects - gene_projects
    
    print(f"匹配的项目数: {len(matched_projects)}")
    if unmatched_in_gene:
        print(f"基因结果中未在项目信息中找到的项目数: {len(unmatched_in_gene)}")
        print(f"前10个未匹配项目: {sorted(list(unmatched_in_gene))[:10]}")
    if unmatched_in_info:
        print(f"项目信息中未在基因结果中找到的项目数: {len(unmatched_in_info)}")
        print(f"前10个未匹配项目: {sorted(list(unmatched_in_info))[:10]}")
    
    # 准备合并的项目信息，只保留需要的列
    project_merge_info = project_info[['Project ID', 'Tissue/Cell Type', 'Cell line', 'Disease']].copy()
    
    # 重命名列以便合并
    project_merge_info = project_merge_info.rename(columns={'Project ID': 'Project id'})
    
    print("\n正在合并数据...")
    
    # 执行左连接，保留所有基因结果记录
    merged_results = gene_results.merge(
        project_merge_info, 
        on='Project id', 
        how='left'
    )
    
    print(f"合并后的行数: {len(merged_results)}")
    
    # 检查是否有缺失值
    missing_tissue = merged_results['Tissue/Cell Type'].isna().sum()
    missing_cell_line = merged_results['Cell line'].isna().sum()
    missing_disease = merged_results['Disease'].isna().sum()
    
    print(f"\n缺失值统计:")
    print(f"Tissue/Cell Type 缺失: {missing_tissue}")
    print(f"Cell line 缺失: {missing_cell_line}")
    print(f"Disease 缺失: {missing_disease}")
    
    # 重新排列列的顺序
    column_order = [
        'GENE ID', 
        'GENE symbol', 
        'Project id', 
        'count',
        'Tissue/Cell Type',
        'Cell line',
        'Disease'
    ]
    merged_results = merged_results[column_order]
    
    # 保存结果
    output_file = 'gene_count_by_project_results_with_info.csv'
    merged_results.to_csv(output_file, index=False)
    print(f"\n合并结果已保存到: {output_file}")
    
    # 显示结果示例
    print("\n合并结果示例(前10行):")
    print(merged_results.head(10).to_string(index=False))
    
    # 显示一些统计信息
    print(f"\n各组织/细胞类型的基因-项目组合数:")
    tissue_counts = merged_results.groupby('Tissue/Cell Type').size().sort_values(ascending=False)
    print(tissue_counts.head(10).to_string())
    
    print(f"\n各疾病类型的基因-项目组合数:")
    disease_counts = merged_results.groupby('Disease').size().sort_values(ascending=False)
    print(disease_counts.head(10).to_string())
    
    return merged_results

if __name__ == "__main__":
    result = merge_project_info() 