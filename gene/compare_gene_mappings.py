#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys

def compare_gene_mappings():
    """
    比较两个文件中基因ID与基因符号的对应关系
    """
    print("=== 基因ID与基因符号对应关系比较 ===\n")
    
    # 文件路径
    file1 = "gene/gene_count_by_project_results.csv"
    file2 = "gene/unique_genes.csv"
    
    try:
        # 读取第一个文件
        print(f"读取文件1: {file1}")
        df1 = pd.read_csv(file1)
        print(f"文件1形状: {df1.shape}")
        print(f"文件1列名: {list(df1.columns)}")
        
        # 检查文件1中是否有GENE ID和GENE symbol列
        if 'GENE ID' in df1.columns and 'GENE symbol' in df1.columns:
            # 提取基因ID和符号的对应关系
            mapping1 = df1[['GENE ID', 'GENE symbol']].drop_duplicates()
            # 重命名列以便后续处理
            mapping1 = mapping1.rename(columns={'GENE ID': 'geneId', 'GENE symbol': 'geneSymbol'})
            print(f"文件1中唯一的基因ID-符号对: {len(mapping1)}")
        else:
            print("文件1中未找到GENE ID和GENE symbol列")
            print("可用列:", list(df1.columns))
            return
            
    except Exception as e:
        print(f"读取文件1时出错: {e}")
        return
    
    try:
        # 读取第二个文件
        print(f"\n读取文件2: {file2}")
        df2 = pd.read_csv(file2)
        print(f"文件2形状: {df2.shape}")
        print(f"文件2列名: {list(df2.columns)}")
        
        # 检查文件2中是否有ensembl_gene_id和external_gene_name列
        if 'ensembl_gene_id' in df2.columns and 'external_gene_name' in df2.columns:
            # 提取基因ID和符号的对应关系
            mapping2 = df2[['ensembl_gene_id', 'external_gene_name']].drop_duplicates()
            print(f"文件2中唯一的基因ID-符号对: {len(mapping2)}")
            
            # 重命名列以便比较
            mapping2 = mapping2.rename(columns={
                'ensembl_gene_id': 'geneId',
                'external_gene_name': 'geneSymbol'
            })
        else:
            print("文件2中未找到ensembl_gene_id和external_gene_name列")
            print("可用列:", list(df2.columns))
            return
            
    except Exception as e:
        print(f"读取文件2时出错: {e}")
        return
    
    # 比较两个映射
    print("\n=== 比较结果 ===")
    
    # 基本统计
    print(f"文件1中的基因ID数量: {len(mapping1['geneId'].unique())}")
    print(f"文件2中的基因ID数量: {len(mapping2['geneId'].unique())}")
    
    # 找出共同的基因ID
    common_gene_ids = set(mapping1['geneId']) & set(mapping2['geneId'])
    print(f"共同的基因ID数量: {len(common_gene_ids)}")
    
    # 只在文件1中的基因ID
    only_in_file1 = set(mapping1['geneId']) - set(mapping2['geneId'])
    print(f"只在文件1中的基因ID数量: {len(only_in_file1)}")
    
    # 只在文件2中的基因ID
    only_in_file2 = set(mapping2['geneId']) - set(mapping1['geneId'])
    print(f"只在文件2中的基因ID数量: {len(only_in_file2)}")
    
    # 检查共同基因ID的符号是否一致
    print("\n=== 检查共同基因ID的符号一致性 ===")
    
    # 创建字典便于查找
    mapping1_dict = dict(zip(mapping1['geneId'], mapping1['geneSymbol']))
    mapping2_dict = dict(zip(mapping2['geneId'], mapping2['geneSymbol']))
    
    consistent_count = 0
    inconsistent_count = 0
    inconsistent_examples = []
    
    for gene_id in common_gene_ids:
        symbol1 = mapping1_dict.get(gene_id, '')
        symbol2 = mapping2_dict.get(gene_id, '')
        
        if symbol1 == symbol2:
            consistent_count += 1
        else:
            inconsistent_count += 1
            if len(inconsistent_examples) < 10:  # 只保存前10个例子
                inconsistent_examples.append({
                    'geneId': gene_id,
                    'symbol_file1': symbol1,
                    'symbol_file2': symbol2
                })
    
    print(f"符号一致的基因ID数量: {consistent_count}")
    print(f"符号不一致的基因ID数量: {inconsistent_count}")
    
    if inconsistent_examples:
        print("\n符号不一致的例子（前10个）:")
        for example in inconsistent_examples:
            print(f"  {example['geneId']}: '{example['symbol_file1']}' vs '{example['symbol_file2']}'")
    
    # 保存详细的比较结果
    print("\n=== 保存详细比较结果 ===")
    
    # 创建详细的比较DataFrame
    comparison_results = []
    
    # 所有基因ID的并集
    all_gene_ids = set(mapping1['geneId']) | set(mapping2['geneId'])
    
    for gene_id in all_gene_ids:
        symbol1 = mapping1_dict.get(gene_id, 'NOT_FOUND')
        symbol2 = mapping2_dict.get(gene_id, 'NOT_FOUND')
        
        status = 'CONSISTENT' if symbol1 == symbol2 and symbol1 != 'NOT_FOUND' else 'INCONSISTENT'
        if symbol1 == 'NOT_FOUND':
            status = 'ONLY_IN_FILE2'
        elif symbol2 == 'NOT_FOUND':
            status = 'ONLY_IN_FILE1'
        
        comparison_results.append({
            'geneId': gene_id,
            'symbol_file1': symbol1,
            'symbol_file2': symbol2,
            'status': status
        })
    
    # 保存比较结果
    comparison_df = pd.DataFrame(comparison_results)
    output_file = "gene/gene_mapping_comparison_results.csv"
    comparison_df.to_csv(output_file, index=False)
    print(f"详细比较结果已保存到: {output_file}")
    
    # 统计摘要
    print("\n=== 最终统计摘要 ===")
    status_counts = comparison_df['status'].value_counts()
    for status, count in status_counts.items():
        print(f"{status}: {count}")
    
    # 计算一致性百分比
    if len(common_gene_ids) > 0:
        consistency_rate = (consistent_count / len(common_gene_ids)) * 100
        print(f"\n共同基因ID的符号一致性: {consistency_rate:.2f}%")
    
    return comparison_df

def main():
    """主函数"""
    try:
        comparison_df = compare_gene_mappings()
        print("\n比较完成！")
    except Exception as e:
        print(f"程序执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
