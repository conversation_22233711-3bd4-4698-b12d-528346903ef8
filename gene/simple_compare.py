import pandas as pd
import sys

try:
    # 读取两个文件
    print("读取文件...")
    df1 = pd.read_csv("gene/gene_count_by_project_results.csv")
    print("文件1读取成功")
    df2 = pd.read_csv("gene/unique_genes.csv")
    print("文件2读取成功")
except Exception as e:
    print(f"读取文件时出错: {e}")
    sys.exit(1)

print(f"文件1形状: {df1.shape}")
print(f"文件1列名: {list(df1.columns)}")
print(f"文件2形状: {df2.shape}")
print(f"文件2列名: {list(df2.columns)}")

# 提取唯一的基因ID-符号对
mapping1 = df1[['GENE ID', 'GENE symbol']].drop_duplicates()
mapping1.columns = ['geneId', 'geneSymbol']

mapping2 = df2[['ensembl_gene_id', 'external_gene_name']].drop_duplicates()
mapping2.columns = ['geneId', 'geneSymbol']

print(f"\n文件1中唯一的基因ID-符号对: {len(mapping1)}")
print(f"文件2中唯一的基因ID-符号对: {len(mapping2)}")

# 基本统计
gene_ids_1 = set(mapping1['geneId'])
gene_ids_2 = set(mapping2['geneId'])

print(f"\n文件1中的基因ID数量: {len(gene_ids_1)}")
print(f"文件2中的基因ID数量: {len(gene_ids_2)}")

# 共同基因ID
common_ids = gene_ids_1 & gene_ids_2
print(f"共同的基因ID数量: {len(common_ids)}")

# 只在某个文件中的基因ID
only_in_1 = gene_ids_1 - gene_ids_2
only_in_2 = gene_ids_2 - gene_ids_1

print(f"只在文件1中的基因ID数量: {len(only_in_1)}")
print(f"只在文件2中的基因ID数量: {len(only_in_2)}")

# 检查符号一致性
mapping1_dict = dict(zip(mapping1['geneId'], mapping1['geneSymbol']))
mapping2_dict = dict(zip(mapping2['geneId'], mapping2['geneSymbol']))

consistent = 0
inconsistent = 0
examples = []

for gene_id in common_ids:
    symbol1 = mapping1_dict[gene_id]
    symbol2 = mapping2_dict[gene_id]
    
    if symbol1 == symbol2:
        consistent += 1
    else:
        inconsistent += 1
        if len(examples) < 10:
            examples.append((gene_id, symbol1, symbol2))

print(f"\n符号一致的基因ID数量: {consistent}")
print(f"符号不一致的基因ID数量: {inconsistent}")

if examples:
    print("\n符号不一致的例子:")
    for gene_id, sym1, sym2 in examples:
        print(f"  {gene_id}: '{sym1}' vs '{sym2}'")

# 计算一致性百分比
if len(common_ids) > 0:
    consistency_rate = (consistent / len(common_ids)) * 100
    print(f"\n共同基因ID的符号一致性: {consistency_rate:.2f}%")

print("\n比较完成！")
