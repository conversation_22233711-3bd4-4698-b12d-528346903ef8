#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计 gene_count_by_project_results_with_info.csv 文件中
GENE ID 和 GENE symbol 两列的唯一值数量，并将所有唯一的组合保存到新的CSV文件中。
"""

import pandas as pd
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extract_unique_gene_info.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

INPUT_FILE = 'gene_count_by_project_results_with_info.csv'
OUTPUT_FILE = 'unique_gene_id_symbol_mapping_2.csv'

def extract_unique_gene_info():
    """提取唯一的基因ID和基因符号组合"""
    try:
        logging.info('读取文件...')
        df = pd.read_csv(INPUT_FILE)
        logging.info(f'原始文件总行数: {len(df)}')
        
        # 统计两列的唯一值数量
        logging.info('=' * 60)
        logging.info('唯一值统计：')
        
        gene_id_unique = df['GENE ID'].nunique()
        gene_symbol_unique = df['GENE symbol'].nunique()
        
        logging.info(f'GENE ID 唯一值数量: {gene_id_unique:,}')
        logging.info(f'GENE symbol 唯一值数量: {gene_symbol_unique:,}')
        
        # 提取需要的两列
        gene_info_df = df[['GENE ID', 'GENE symbol']].copy()
        
        # 去除重复的组合
        unique_gene_info = gene_info_df.drop_duplicates()
        logging.info(f'唯一的 GENE ID-GENE symbol 组合数量: {len(unique_gene_info):,}')
        
        # 检查是否有 GENE ID 对应多个不同的 GENE symbol
        gene_id_groups = unique_gene_info.groupby('GENE ID')['GENE symbol'].nunique()
        multi_symbol_genes = gene_id_groups[gene_id_groups > 1]
        
        if len(multi_symbol_genes) > 0:
            logging.warning(f'发现 {len(multi_symbol_genes)} 个基因ID对应多个不同的基因符号:')
            for gene_id in multi_symbol_genes.head(10).index:
                symbols = unique_gene_info[unique_gene_info['GENE ID'] == gene_id]['GENE symbol'].unique()
                logging.warning(f'  {gene_id}: {list(symbols)}')
            if len(multi_symbol_genes) > 10:
                logging.warning(f'  ... 还有 {len(multi_symbol_genes) - 10} 个类似情况')
        else:
            logging.info('所有基因ID都只对应一个基因符号')
        
        # 检查是否有 GENE symbol 对应多个不同的 GENE ID
        symbol_groups = unique_gene_info.groupby('GENE symbol')['GENE ID'].nunique()
        multi_id_symbols = symbol_groups[symbol_groups > 1]
        
        if len(multi_id_symbols) > 0:
            logging.warning(f'发现 {len(multi_id_symbols)} 个基因符号对应多个不同的基因ID:')
            for symbol in multi_id_symbols.head(10).index:
                gene_ids = unique_gene_info[unique_gene_info['GENE symbol'] == symbol]['GENE ID'].unique()
                logging.warning(f'  {symbol}: {list(gene_ids)}')
            if len(multi_id_symbols) > 10:
                logging.warning(f'  ... 还有 {len(multi_id_symbols) - 10} 个类似情况')
        else:
            logging.info('所有基因符号都只对应一个基因ID')
        
        # 检查空值情况
        logging.info('=' * 60)
        logging.info('空值检查：')
        
        gene_id_null = unique_gene_info['GENE ID'].isnull().sum()
        gene_symbol_null = unique_gene_info['GENE symbol'].isnull().sum()
        gene_id_empty = (unique_gene_info['GENE ID'] == '').sum()
        gene_symbol_empty = (unique_gene_info['GENE symbol'] == '').sum()
        
        logging.info(f'GENE ID NULL 值数量: {gene_id_null}')
        logging.info(f'GENE symbol NULL 值数量: {gene_symbol_null}')
        logging.info(f'GENE ID 空字符串数量: {gene_id_empty}')
        logging.info(f'GENE symbol 空字符串数量: {gene_symbol_empty}')
        
        # 按 GENE ID 排序
        unique_gene_info = unique_gene_info.sort_values('GENE ID').reset_index(drop=True)
        
        # 保存结果
        logging.info(f'保存结果到 {OUTPUT_FILE}...')
        unique_gene_info.to_csv(OUTPUT_FILE, index=False)
        
        # 输出最终统计信息
        logging.info('=' * 60)
        logging.info('提取完成！最终统计信息：')
        logging.info(f'原始记录数: {len(df):,}')
        logging.info(f'唯一的 GENE ID 数量: {gene_id_unique:,}')
        logging.info(f'唯一的 GENE symbol 数量: {gene_symbol_unique:,}')
        logging.info(f'唯一的 GENE ID-GENE symbol 组合数量: {len(unique_gene_info):,}')
        logging.info(f'输出文件: {OUTPUT_FILE}')
        logging.info('=' * 60)
        
        return True
        
    except Exception as e:
        logging.error(f'处理过程中发生错误: {str(e)}')
        return False

if __name__ == '__main__':
    print('开始提取唯一基因信息...')
    success = extract_unique_gene_info()
    if success:
        print('提取完成！')
    else:
        print('提取失败，请查看日志文件。') 