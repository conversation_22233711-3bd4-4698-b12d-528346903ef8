#!/usr/bin/env python3
"""
提取显著的统计结果
"""

import csv

def extract_significant_results(input_file, output_file, p_threshold=0.05):
    """提取显著结果"""
    significant_results = []
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                # 检查FDR校正后的T检验是否显著
                try:
                    fdr_t = float(row['fdr_t']) if row['fdr_t'] and row['fdr_t'] != '' else None
                    if fdr_t is not None and fdr_t < p_threshold:
                        significant_results.append(row)
                except:
                    continue
        
        print(f"找到 {len(significant_results)} 个显著结果 (FDR校正T检验 p < {p_threshold})")
        
        # 按p值排序
        significant_results.sort(key=lambda x: float(x['fdr_t']) if x['fdr_t'] else 1.0)
        
        # 保存结果
        if significant_results:
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                fieldnames = significant_results[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(significant_results)
            
            print(f"显著结果已保存到: {output_file}")
            
            # 显示前10个最显著的结果
            print(f"\n前10个最显著的结果:")
            print(f"{'基因':<12} {'疾病':<25} {'变量':<4} {'FDR_p':<10} {'方向':<8}")
            print("-" * 70)
            
            for i, result in enumerate(significant_results[:10]):
                gene = result['geneSymbol'][:11]
                disease = result['disease_category'][:24]
                variable = result['variable']
                fdr_p = float(result['fdr_t'])
                direction = result['direction']
                
                print(f"{gene:<12} {disease:<25} {variable:<4} {fdr_p:<10.2e} {direction:<8}")
        
        else:
            print("没有找到显著结果")
        
        return significant_results
    
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def analyze_significant_patterns(significant_results):
    """分析显著结果的模式"""
    if not significant_results:
        return
    
    print(f"\n=== 显著结果模式分析 ===")
    
    # 按疾病统计
    disease_counts = {}
    for result in significant_results:
        disease = result['disease_category']
        disease_counts[disease] = disease_counts.get(disease, 0) + 1
    
    print(f"\n按疾病分布:")
    for disease, count in sorted(disease_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {disease}: {count} 个显著结果")
    
    # 按变量统计
    variable_counts = {}
    for result in significant_results:
        variable = result['variable']
        variable_counts[variable] = variable_counts.get(variable, 0) + 1
    
    print(f"\n按变量分布:")
    for variable, count in variable_counts.items():
        print(f"  {variable}: {count} 个显著结果")
    
    # 按方向统计
    direction_counts = {}
    for result in significant_results:
        direction = result['direction']
        direction_counts[direction] = direction_counts.get(direction, 0) + 1
    
    print(f"\n按表达变化方向:")
    for direction, count in direction_counts.items():
        print(f"  {direction}: {count} 个显著结果")
    
    # 最常出现的基因
    gene_counts = {}
    for result in significant_results:
        gene = result['geneSymbol']
        gene_counts[gene] = gene_counts.get(gene, 0) + 1
    
    print(f"\n显著结果最多的基因 (前10个):")
    sorted_genes = sorted(gene_counts.items(), key=lambda x: x[1], reverse=True)
    for gene, count in sorted_genes[:10]:
        print(f"  {gene}: {count} 个显著结果")

def main():
    """主函数"""
    input_file = "gene_statistical_analysis_results.csv"
    output_file = "significant_gene_results.csv"
    
    print("=== 提取显著的基因翻译效率差异结果 ===")
    
    # 提取显著结果
    significant_results = extract_significant_results(input_file, output_file)
    
    if significant_results:
        # 分析模式
        analyze_significant_patterns(significant_results)
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
