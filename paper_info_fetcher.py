import pandas as pd
import requests
import time
import xml.etree.ElementTree as ET
import json
from tqdm import tqdm

def fetch_pubmed_data(pmid):
    """
    Fetch detailed information from PubMed for a given PMID
    """
    base_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi"
    
    # Parameters for the API request
    params = {
        "db": "pubmed",
        "id": pmid,
        "retmode": "xml",
        "rettype": "abstract"
    }
    
    try:
        # Make the request with a small delay to avoid hitting rate limits
        response = requests.get(base_url, params=params)
        
        if response.status_code == 200:
            return response.text
        else:
            print(f"Error fetching PMID {pmid}: Status code {response.status_code}")
            return None
    except Exception as e:
        print(f"Exception when fetching PMID {pmid}: {str(e)}")
        return None

def process_csv_file(input_file, output_file):
    """
    Process the CSV file, fetch PubMed data for each PMID, and save to a new file
    """
    # Read the CSV file
    df = pd.read_csv(input_file)
    
    # Create a new column for PubMed data
    df['pubmed_data'] = None
    
    # Process each row
    for index, row in tqdm(df.iterrows(), total=len(df), desc="Fetching PubMed data"):
        # Extract PMID from the first column if it's a valid number
        try:
            pmid = str(row.iloc[0]).strip()
            if pmid.isdigit():
                # Fetch data from PubMed
                pubmed_data = fetch_pubmed_data(pmid)
                
                # Store the result
                if pubmed_data:
                    df.at[index, 'pubmed_data'] = pubmed_data
                
                # Add a small delay to avoid hitting API rate limits
                time.sleep(0.5)
        except Exception as e:
            print(f"Error processing row {index}: {str(e)}")
    
    # Save the updated dataframe to a new CSV file
    df.to_csv(output_file, index=False)
    print(f"Data saved to {output_file}")

if __name__ == "__main__":
    input_file = "Browse-Publication/paper_info.csv"
    output_file = "Browse-Publication/paper_info_with_pubmed.csv"
    
    process_csv_file(input_file, output_file) 