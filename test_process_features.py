#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 process_features_summary.py 代码的正确性
"""

import json
import re
from typing import Dict, Any, List

# 复制原代码中的关键函数
POS_REGEX = re.compile(r"(?P<start>\d+)\s*-\s*(?P<end>\d+)")

def parse_position_length(pos_str: str) -> int:
    """根据给定的字符串计算长度(end - start + 1)。"""
    # 提取所有数字
    nums = re.findall(r"\d+", pos_str)
    if len(nums) < 2:
        return 0

    # 使用最后两个数字作为 start, end，可适配带染色体号的格式
    start = int(nums[-2])
    end = int(nums[-1])

    if end < start:
        start, end = end, start

    return end - start + 1

def summarise_feature(raw_value: Any) -> Dict[str, Any]:
    """将原始字段值转换为 {count, position_avg_length}。"""
    if raw_value in (None, "", []):
        return ""  # 返回空字符串保持与原数据一致

    # 将字符串转换为列表
    records: List[Any] = []
    if isinstance(raw_value, str):
        raw_value = raw_value.strip()
        if raw_value.startswith("["):
            # 尝试解析为 JSON 数组
            try:
                records = json.loads(raw_value)
            except Exception:
                # 如果不是有效 JSON，则按换行分割
                records = [line for line in raw_value.split("\n") if line.strip()]
        else:
            # 按换行分割
            records = [line for line in raw_value.split("\n") if line.strip()]
    elif isinstance(raw_value, list):
        records = raw_value
    else:
        # 其它类型，包装成单记录
        records = [raw_value]

    count = len(records)
    if count == 0:
        return ""

    # 计算 position 长度
    total_len = 0
    for rec in records:
        pos_str = None
        # 如果是 dict，查找常见键
        if isinstance(rec, dict):
            pos_str = rec.get("Position") or rec.get("Genomic position") or rec.get("Genomic Position")
        else:
            pos_str = str(rec)
        if pos_str:
            total_len += parse_position_length(pos_str)

    position_avg_length = total_len / count if count else 0

    return {
        "count": count,
        "position_avg_length": position_avg_length
    }

# 测试用例
def test_parse_position_length():
    """测试位置长度解析函数"""
    print("测试 parse_position_length 函数:")
    
    test_cases = [
        ("4..7", 4),  # 7-4+1 = 4
        ("1471768-1471771", 4),  # 1471771-1471768+1 = 4
        ("1:1471768-1471771:+", 4),  # 1471771-1471768+1 = 4
        ("380..389", 10),  # 389-380+1 = 10
        ("1:1496197-1496206:+", 10),  # 1496206-1496197+1 = 10
        ("invalid", 0),  # 无法解析
        ("", 0),  # 空字符串
    ]
    
    for pos_str, expected in test_cases:
        result = parse_position_length(pos_str)
        status = "✓" if result == expected else "✗"
        print(f"  {status} '{pos_str}' -> {result} (期望: {expected})")

def test_summarise_feature():
    """测试特征总结函数"""
    print("\n测试 summarise_feature 函数:")
    
    # 测试空值
    print("  测试空值:")
    for empty_val in [None, "", []]:
        result = summarise_feature(empty_val)
        status = "✓" if result == "" else "✗"
        print(f"    {status} {empty_val} -> {result}")
    
    # 测试JSON数组格式
    print("  测试JSON数组格式:")
    json_data = '[{"Position": "4..7", "Genomic position": "1:1471768-1471771:+", "Description": "test"}, {"Position": "9..12", "Genomic position": "1:1471773-1471776:+", "Description": "test2"}]'
    result = summarise_feature(json_data)
    expected_count = 2
    expected_avg_length = (4 + 4) / 2  # 两个位置都是长度4
    
    if isinstance(result, dict):
        count_ok = result["count"] == expected_count
        avg_ok = abs(result["position_avg_length"] - expected_avg_length) < 0.001
        status = "✓" if count_ok and avg_ok else "✗"
        print(f"    {status} JSON数组 -> count: {result['count']}, avg_length: {result['position_avg_length']}")
    else:
        print(f"    ✗ JSON数组 -> {result} (期望字典)")
    
    # 测试单个位置
    print("  测试单个位置:")
    single_json = '[{"Position": "380..389", "Genomic position": "1:1496197-1496206:+"}]'
    result = summarise_feature(single_json)
    expected_count = 1
    expected_avg_length = 10  # 389-380+1 = 10
    
    if isinstance(result, dict):
        count_ok = result["count"] == expected_count
        avg_ok = abs(result["position_avg_length"] - expected_avg_length) < 0.001
        status = "✓" if count_ok and avg_ok else "✗"
        print(f"    {status} 单个位置 -> count: {result['count']}, avg_length: {result['position_avg_length']}")
    else:
        print(f"    ✗ 单个位置 -> {result} (期望字典)")

if __name__ == "__main__":
    test_parse_position_length()
    test_summarise_feature()
    print("\n测试完成！")
