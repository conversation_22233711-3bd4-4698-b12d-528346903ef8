import pandas as pd
import json

# Read the CSV file
print("Reading GSE_match_new.csv...")
df = pd.read_csv("GSE_match_new.csv")

# Function to merge Tissue and Cell Type and create a unique list
def get_tissue_cell_unique_values(df):
    # Create a new list to store merged values
    merged_values = []
    
    # Iterate through rows
    for _, row in df.iterrows():
        tissue = row["Tissue"]
        cell_type = row["Cell Type"]
        
        # Check if values are NA or empty
        tissue_empty = pd.isna(tissue) or str(tissue).strip() == ""
        cell_empty = pd.isna(cell_type) or str(cell_type).strip() == ""
        
        if tissue_empty and cell_empty:
            # Skip if both are empty or NA
            continue
        elif tissue_empty:
            merged_values.append(str(cell_type))
        elif cell_empty:
            merged_values.append(str(tissue))
        else:
            merged_values.append(f"{tissue}; {cell_type}")
    
    # Return unique values, sorted
    return sorted(list(set(merged_values)))

# Function to get unique values from a column, removing NA
def get_unique_values(df, column_name):
    values = df[column_name].dropna().unique()
    values = [str(value) for value in values if str(value).strip() != ""]
    return sorted(values)

# Get unique values for each required field
unique_values = {
    "Tissue/Cell": get_tissue_cell_unique_values(df),
    "Cell line": get_unique_values(df, "CelI line"),  # Note: using "CelI line" as in the file
    "Healthy Condition": get_unique_values(df, "Healthy Condition")
}

# Convert to JSON and print
json_output = json.dumps(unique_values, indent=2)
print("\nUnique values in JSON format:")
print(json_output)

# Optionally save to a file
with open("unique_values.json", "w") as f:
    f.write(json_output)
    
print("\nResults saved to unique_values.json")

# Print summary statistics
print("\nSummary:")
for key, values in unique_values.items():
    print(f"{key}: {len(values)} unique values") 