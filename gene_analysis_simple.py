#!/usr/bin/env python3
"""
基因翻译效率统计分析脚本 - 简化版本
使用内置库进行基本的统计分析
"""

import csv
import math
from collections import defaultdict
import statistics

def read_csv_file(filename):
    """读取CSV文件"""
    data = []
    headers = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)
            for row in reader:
                data.append(row)
        
        print(f"成功读取文件: {filename}")
        print(f"数据行数: {len(data)}")
        print(f"列数: {len(headers)}")
        print(f"列名: {headers}")
        
        return headers, data
    
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None, None

def parse_data(headers, data):
    """解析数据"""
    # 找到关键列的索引
    col_indices = {}
    for i, header in enumerate(headers):
        col_indices[header] = i
    
    # 检查必要的列
    required_cols = ['geneSymbol', 'geneId', 'disease']
    missing_cols = [col for col in required_cols if col not in col_indices]
    
    if missing_cols:
        print(f"缺少必要的列: {missing_cols}")
        return None
    
    # 检查分析变量列
    analysis_cols = []
    for col in ['TE', 'TR', 'EVI']:
        if col in col_indices:
            analysis_cols.append(col)
    
    if not analysis_cols:
        print("未找到TE、TR、EVI列")
        return None
    
    print(f"可用于分析的变量: {analysis_cols}")
    
    # 解析数据
    parsed_data = []
    diseases = set()
    
    for row in data:
        if len(row) != len(headers):
            continue
        
        record = {}
        for col, idx in col_indices.items():
            record[col] = row[idx]
        
        diseases.add(record['disease'])
        parsed_data.append(record)
    
    print(f"解析后的数据行数: {len(parsed_data)}")
    print(f"疾病类型: {sorted(diseases)}")
    
    return parsed_data, analysis_cols, sorted(diseases)

def safe_float(value):
    """安全转换为浮点数"""
    try:
        if value == '' or value is None:
            return None
        return float(value)
    except:
        return None

def t_test_simple(group1, group2):
    """简单的t检验实现"""
    try:
        if len(group1) < 2 or len(group2) < 2:
            return None
        
        mean1 = statistics.mean(group1)
        mean2 = statistics.mean(group2)
        
        var1 = statistics.variance(group1)
        var2 = statistics.variance(group2)
        
        n1, n2 = len(group1), len(group2)
        
        # 计算合并方差
        pooled_var = ((n1-1)*var1 + (n2-1)*var2) / (n1+n2-2)
        
        # 计算t统计量
        t_stat = (mean1 - mean2) / math.sqrt(pooled_var * (1/n1 + 1/n2))
        
        # 简单的p值估计（双尾检验）
        # 这是一个简化的实现，实际应该使用t分布
        df = n1 + n2 - 2
        
        # 使用正态分布近似（当df较大时）
        if df > 30:
            p_value = 2 * (1 - 0.5 * (1 + math.erf(abs(t_stat) / math.sqrt(2))))
        else:
            # 对于小样本，使用一个粗略的估计
            p_value = 2 * (1 - 0.5 * (1 + math.erf(abs(t_stat) / math.sqrt(2))))
        
        return p_value
    
    except Exception as e:
        print(f"t检验计算错误: {e}")
        return None

def wilcoxon_rank_sum_simple(group1, group2):
    """简单的Wilcoxon秩和检验实现"""
    try:
        if len(group1) < 2 or len(group2) < 2:
            return None
        
        # 合并数据并排序
        combined = [(val, 1) for val in group1] + [(val, 2) for val in group2]
        combined.sort(key=lambda x: x[0])
        
        # 计算秩
        ranks = []
        i = 0
        while i < len(combined):
            j = i
            while j < len(combined) and combined[j][0] == combined[i][0]:
                j += 1
            
            # 平均秩
            avg_rank = (i + j + 1) / 2
            for k in range(i, j):
                ranks.append((combined[k][1], avg_rank))
            i = j
        
        # 计算组1的秩和
        rank_sum1 = sum(rank for group, rank in ranks if group == 1)
        
        n1, n2 = len(group1), len(group2)
        
        # 计算U统计量
        u1 = rank_sum1 - n1 * (n1 + 1) / 2
        u2 = n1 * n2 - u1
        
        u = min(u1, u2)
        
        # 正态近似
        mean_u = n1 * n2 / 2
        var_u = n1 * n2 * (n1 + n2 + 1) / 12
        
        if var_u == 0:
            return None
        
        z = (u - mean_u) / math.sqrt(var_u)
        
        # 双尾p值
        p_value = 2 * (1 - 0.5 * (1 + math.erf(abs(z) / math.sqrt(2))))
        
        return p_value
    
    except Exception as e:
        print(f"Wilcoxon检验计算错误: {e}")
        return None

def ks_test_simple(group1, group2):
    """简单的KS检验实现"""
    try:
        if len(group1) < 2 or len(group2) < 2:
            return None
        
        # 合并并排序所有值
        all_values = sorted(set(group1 + group2))
        
        max_diff = 0
        
        for value in all_values:
            # 计算累积分布函数
            cdf1 = sum(1 for x in group1 if x <= value) / len(group1)
            cdf2 = sum(1 for x in group2 if x <= value) / len(group2)
            
            diff = abs(cdf1 - cdf2)
            max_diff = max(max_diff, diff)
        
        # 计算p值（简化版本）
        n1, n2 = len(group1), len(group2)
        n = (n1 * n2) / (n1 + n2)
        
        # Kolmogorov分布的近似
        lambda_val = max_diff * math.sqrt(n)
        
        # 简化的p值计算
        if lambda_val < 0.27:
            p_value = 1.0
        elif lambda_val < 1.0:
            p_value = 2 * math.exp(-2 * lambda_val * lambda_val)
        else:
            p_value = 2 * math.exp(-2 * lambda_val * lambda_val)
        
        return min(p_value, 1.0)
    
    except Exception as e:
        print(f"KS检验计算错误: {e}")
        return None

def benjamini_hochberg_correction(p_values):
    """Benjamini-Hochberg FDR校正"""
    try:
        # 过滤掉None值
        valid_p = [(i, p) for i, p in enumerate(p_values) if p is not None]
        
        if not valid_p:
            return [None] * len(p_values)
        
        # 按p值排序
        valid_p.sort(key=lambda x: x[1])
        
        # 计算校正后的p值
        m = len(valid_p)
        corrected = [None] * len(p_values)
        
        for rank, (original_idx, p_val) in enumerate(valid_p):
            corrected_p = p_val * m / (rank + 1)
            corrected[original_idx] = min(corrected_p, 1.0)
        
        # 确保单调性
        for i in range(len(valid_p) - 2, -1, -1):
            idx = valid_p[i][0]
            next_idx = valid_p[i + 1][0]
            if corrected[idx] is not None and corrected[next_idx] is not None:
                corrected[idx] = min(corrected[idx], corrected[next_idx])
        
        return corrected
    
    except Exception as e:
        print(f"FDR校正错误: {e}")
        return [None] * len(p_values)

def analyze_data(parsed_data, analysis_cols, diseases):
    """分析数据"""
    print("\n开始统计分析...")
    
    # 找到Normal组
    if 'Normal' not in diseases:
        print("错误: 未找到Normal对照组")
        return None
    
    test_diseases = [d for d in diseases if d != 'Normal']
    print(f"将分析的疾病组: {test_diseases}")
    
    results = []
    
    # 按基因分组
    gene_groups = defaultdict(list)
    for record in parsed_data:
        key = (record['geneSymbol'], record['geneId'])
        gene_groups[key].append(record)
    
    total_genes = len(gene_groups)
    print(f"总基因数: {total_genes}")
    
    processed = 0
    
    for (gene_symbol, gene_id), gene_records in gene_groups.items():
        processed += 1
        if processed % 1000 == 0:
            print(f"处理进度: {processed}/{total_genes}")
        
        # 分离Normal组和疾病组
        normal_records = [r for r in gene_records if r['disease'] == 'Normal']
        
        if not normal_records:
            continue
        
        # 对每个变量进行分析
        for variable in analysis_cols:
            normal_values = [safe_float(r[variable]) for r in normal_records]
            normal_values = [v for v in normal_values if v is not None]
            
            if len(normal_values) < 2:
                continue
            
            # 对每个疾病组进行比较
            for disease in test_diseases:
                disease_records = [r for r in gene_records if r['disease'] == disease]
                
                if not disease_records:
                    continue
                
                disease_values = [safe_float(r[variable]) for r in disease_records]
                disease_values = [v for v in disease_values if v is not None]
                
                if len(disease_values) < 2:
                    continue
                
                # 执行统计检验
                p_t = t_test_simple(disease_values, normal_values)
                p_wilcox = wilcoxon_rank_sum_simple(disease_values, normal_values)
                p_ks = ks_test_simple(disease_values, normal_values)
                
                # 确定方向
                disease_mean = statistics.mean(disease_values)
                normal_mean = statistics.mean(normal_values)
                direction = 'higher' if disease_mean > normal_mean else 'lower'
                
                result = {
                    'geneId': gene_id,
                    'geneSymbol': gene_symbol,
                    'disease_category': disease,
                    'variable': variable,
                    'p_t': p_t,
                    'p_wilcox': p_wilcox,
                    'p_ks': p_ks,
                    'direction': direction
                }
                
                results.append(result)
    
    print(f"生成了 {len(results)} 个分析结果")
    return results

def apply_fdr_correction_simple(results):
    """应用FDR校正"""
    print("应用FDR校正...")
    
    # 提取p值
    p_t_values = [r['p_t'] for r in results]
    p_wilcox_values = [r['p_wilcox'] for r in results]
    p_ks_values = [r['p_ks'] for r in results]
    
    # 应用校正
    fdr_t = benjamini_hochberg_correction(p_t_values)
    fdr_wilcox = benjamini_hochberg_correction(p_wilcox_values)
    fdr_ks = benjamini_hochberg_correction(p_ks_values)
    
    # 添加校正后的p值
    for i, result in enumerate(results):
        result['fdr_t'] = fdr_t[i]
        result['fdr_wilcox'] = fdr_wilcox[i]
        result['fdr_ks'] = fdr_ks[i]
    
    return results

def save_results_csv(results, filename):
    """保存结果为CSV"""
    print(f"保存结果到: {filename}")
    
    if not results:
        print("没有结果可保存")
        return
    
    # 定义列顺序
    columns = [
        'geneId', 'geneSymbol', 'disease_category',
        'p_t', 'fdr_t', 'p_wilcox', 'fdr_wilcox', 'p_ks', 'fdr_ks',
        'direction', 'variable'
    ]
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow(columns)
            
            # 写入数据
            for result in results:
                row = []
                for col in columns:
                    value = result.get(col, '')
                    if value is None:
                        value = ''
                    row.append(value)
                writer.writerow(row)
        
        print(f"成功保存 {len(results)} 行结果")
        
        # 统计摘要
        genes = set(r['geneSymbol'] for r in results)
        diseases = set(r['disease_category'] for r in results)
        variables = set(r['variable'] for r in results)
        
        print(f"分析的基因数: {len(genes)}")
        print(f"分析的疾病数: {len(diseases)}")
        print(f"分析的变量数: {len(variables)}")
        
        # 显著性统计
        for test_type in ['fdr_t', 'fdr_wilcox', 'fdr_ks']:
            significant = sum(1 for r in results if r.get(test_type) is not None and r[test_type] < 0.05)
            total = sum(1 for r in results if r.get(test_type) is not None)
            print(f"{test_type} 显著结果 (p<0.05): {significant}/{total}")
    
    except Exception as e:
        print(f"保存文件失败: {e}")

def main():
    """主函数"""
    input_file = "gene/gene_with_translation_indices.csv"
    output_file = "gene_statistical_analysis_results.csv"
    
    print("=== 基因翻译效率统计分析 (简化版) ===")
    
    # 1. 读取数据
    headers, data = read_csv_file(input_file)
    if headers is None:
        return
    
    # 2. 解析数据
    parse_result = parse_data(headers, data)
    if parse_result is None:
        return
    
    parsed_data, analysis_cols, diseases = parse_result
    
    # 3. 分析数据
    results = analyze_data(parsed_data, analysis_cols, diseases)
    if results is None:
        return
    
    # 4. 应用FDR校正
    results = apply_fdr_correction_simple(results)
    
    # 5. 保存结果
    save_results_csv(results, output_file)
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
