import os
import csv
import time
import pandas as pd
import mysql.connector
from mysql.connector import Error
import concurrent.futures
from tqdm import tqdm
import numpy as np
from functools import lru_cache
import pickle
import threading
import multiprocessing
from datetime import datetime

# 数据库配置信息
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306,
    'buffered': True,
    'connect_timeout': 60,  # 增加连接超时时间
    'use_pure': True,  # 使用纯Python实现，提高稳定性
}

# 全局缓存
PROJECT_DATA_CACHE = {}  # 项目数据缓存
TPM_CACHE = {}  # TPM数据缓存
RESULTS_BUFFER = []  # 结果缓存
RESULTS_BUFFER_LOCK = threading.Lock()  # 结果缓存锁
BUFFER_SIZE = 5000  # 结果缓冲区大小

# 配置
MAX_WORKERS = 12  # 线程池工作线程数
BATCH_SIZE = 200  # 每批处理的转录本数
MAX_PROJECTS_PER_ROUND = 10  # 每轮处理的项目数
FLUSH_INTERVAL = 60  # 刷新结果到文件的间隔(秒)
OUTPUT_FILE = 'translation_indices_results.csv'
TRANSCRIPT_LIMIT = None  # 设置为数字以限制处理的转录本数量(用于测试)
PROJECT_LIMIT = None  # 设置为数字以限制处理的项目数量(用于测试)

# 进度跟踪
PROGRESS = {
    'processed_combinations': 0,
    'last_flush_time': time.time()
}

def get_connection():
    """创建数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        print(f"数据库连接错误: {e}")
        return None

def close_connection(connection):
    """关闭数据库连接"""
    if connection and connection.is_connected():
        connection.close()

def get_unique_transcript_ids(file_path='Transcript_id.csv'):
    """获取唯一的转录本ID列表，使用内存高效的方式"""
    try:
        # 尝试读取缓存
        cache_file = file_path + '.cache'
        if os.path.exists(cache_file):
            with open(cache_file, 'rb') as f:
                unique_ids = pickle.load(f)
                print(f"从缓存加载 {len(unique_ids)} 个唯一转录本ID")
                return unique_ids
    except:
        pass
    
    # 使用更高效的读取方式
    chunk_size = 100000  # 每次读取的行数
    unique_ids = set()
    
    for chunk in pd.read_csv(file_path, chunksize=chunk_size):
        unique_ids.update(chunk['Transcript'].unique())
    
    unique_ids = list(unique_ids)
    print(f"发现 {len(unique_ids)} 个唯一转录本ID")
    
    # 保存缓存
    try:
        with open(cache_file, 'wb') as f:
            pickle.dump(unique_ids, f)
    except:
        pass
        
    return unique_ids

def get_unique_project_ids(file_path='GSE_match.csv'):
    """获取唯一的项目ID列表，使用缓存"""
    try:
        # 尝试读取缓存
        cache_file = file_path + '.cache'
        if os.path.exists(cache_file):
            with open(cache_file, 'rb') as f:
                unique_ids = pickle.load(f)
                print(f"从缓存加载 {len(unique_ids)} 个唯一项目ID")
                return unique_ids
    except:
        pass
    
    df = pd.read_csv(file_path)
    unique_ids = df['Project ID'].unique().tolist()
    print(f"发现 {len(unique_ids)} 个唯一项目ID")
    
    # 保存缓存
    try:
        with open(cache_file, 'wb') as f:
            pickle.dump(unique_ids, f)
    except:
        pass
        
    return unique_ids

def preload_project_data(connection, project_ids):
    """预加载项目数据到缓存"""
    global PROJECT_DATA_CACHE
    
    # 一次性获取所有项目的策略类型
    placeholders = ', '.join(['%s'] * len(project_ids))
    query = f"""
    SELECT projectId, strategy 
    FROM gseMatch 
    WHERE projectId IN ({placeholders})
    GROUP BY projectId, strategy
    """
    
    cursor = connection.cursor()
    cursor.execute(query, project_ids)
    results = cursor.fetchall()
    
    # 构建项目->策略映射
    for project_id, strategy in results:
        if project_id not in PROJECT_DATA_CACHE:
            PROJECT_DATA_CACHE[project_id] = {'strategies': set(), 'sra_bioproject': None}
        PROJECT_DATA_CACHE[project_id]['strategies'].add(strategy)
    
    # 一次性获取所有项目的SRA和BioProject信息
    query = f"""
    SELECT projectId, sraAccession, bioProjectId
    FROM gseMatch
    WHERE projectId IN ({placeholders})
    GROUP BY projectId
    """
    
    cursor.execute(query, project_ids)
    results = cursor.fetchall()
    
    # 更新项目->SRA/BioProject映射
    for project_id, sra, bioproject in results:
        if project_id in PROJECT_DATA_CACHE:
            PROJECT_DATA_CACHE[project_id]['sra_bioproject'] = (sra, bioproject)

def preload_tpm_data(connection, project_ids, transcript_batch):
    """预加载一批项目和转录本的TPM数据到缓存"""
    global TPM_CACHE
    
    # 构建查询参数
    project_placeholders = ', '.join(['%s'] * len(project_ids))
    transcript_placeholders = ', '.join(['%s'] * len(transcript_batch))
    
    query = f"""
    SELECT g.projectId, g.strategy, t.transcriptId, g.sraAccession, t.tpm 
    FROM gseMatch g
    JOIN tpmData t ON g.sraAccession = t.sraAccession
    WHERE g.projectId IN ({project_placeholders})
    AND t.transcriptId IN ({transcript_placeholders})
    AND t.tpm >= 1
    """
    
    params = project_ids + transcript_batch
    
    # 执行查询
    cursor = connection.cursor()
    cursor.execute(query, params)
    results = cursor.fetchall()
    
    # 组织数据到缓存: {project_id: {strategy: {transcript_id: [(sra, tpm), ...]}}}
    for project_id, strategy, transcript_id, sra, tpm in results:
        # 初始化缓存层次结构
        if project_id not in TPM_CACHE:
            TPM_CACHE[project_id] = {}
        
        if strategy not in TPM_CACHE[project_id]:
            TPM_CACHE[project_id][strategy] = {}
        
        if transcript_id not in TPM_CACHE[project_id][strategy]:
            TPM_CACHE[project_id][strategy][transcript_id] = []
        
        # 添加TPM数据
        TPM_CACHE[project_id][strategy][transcript_id].append((sra, tpm))

def calculate_indices(p_value, m_value, r_value):
    """计算翻译指标"""
    indices = {}
    
    # 检查哪些指标可以计算
    can_calc_tr = r_value is not None and m_value is not None and m_value != 0
    can_calc_evi = r_value is not None and m_value is not None and p_value is not None and m_value != 0 and p_value != 0
    can_calc_te = p_value is not None and m_value is not None and m_value != 0
    
    # 计算指标
    if can_calc_tr:
        indices['TR'] = r_value / m_value
    else:
        indices['TR'] = None
    
    if can_calc_evi:
        indices['EVI'] = (r_value ** 2) / (m_value * p_value)
    else:
        indices['EVI'] = None
    
    if can_calc_te:
        indices['TE'] = p_value / m_value
    else:
        indices['TE'] = None
    
    return indices

def calculate_strategy_value(project_id, strategy, transcript_id):
    """从缓存计算特定策略的值"""
    try:
        # 从缓存获取TPM数据
        tpm_data = TPM_CACHE.get(project_id, {}).get(strategy, {}).get(transcript_id, [])
        
        if not tpm_data:
            return None
        
        # 计算平均值
        tpm_values = [item[1] for item in tpm_data]
        sra_count = len(set([item[0] for item in tpm_data]))
        
        if tpm_values and sra_count > 0:
            return sum(tpm_values) / sra_count
        
        return None
    except Exception as e:
        print(f"计算策略值错误: {e}")
        return None

def process_transcript(transcript_id, project_id):
    """处理单个转录本"""
    try:
        # 获取项目数据
        project_data = PROJECT_DATA_CACHE.get(project_id)
        if not project_data:
            return None
        
        strategies = project_data['strategies']
        sra_bioproject = project_data['sra_bioproject']
        
        if not strategies or not sra_bioproject:
            return None
        
        sra_accession, bioproject_id = sra_bioproject
        
        # 策略映射
        strategy_map = {
            'Ribo': 'P',
            'RNA': 'M',
            'RNC': 'R'
        }
        
        # 获取每种策略的值
        values = {}
        for strategy in strategies:
            if strategy in strategy_map:
                key = strategy_map[strategy]
                value = calculate_strategy_value(project_id, strategy, transcript_id)
                if value is not None:
                    values[key] = value
        
        # 如果至少有两种策略，计算指标
        if len(values) >= 2:
            p_value = values.get('P')
            m_value = values.get('M')
            r_value = values.get('R')
            
            indices = calculate_indices(p_value, m_value, r_value)
            tr = indices['TR']
            evi = indices['EVI']
            te = indices['TE']
        else:
            tr, evi, te = None, None, None
        
        return (transcript_id, project_id, sra_accession, bioproject_id, tr, evi, te)
    
    except Exception as e:
        print(f"处理转录本 {transcript_id}-{project_id} 时出错: {e}")
        return None

def process_batch(args):
    """处理一批转录本"""
    project_id, transcript_batch = args
    
    results = []
    for transcript_id in transcript_batch:
        result = process_transcript(transcript_id, project_id)
        if result:
            results.append(result)
    
    # 更新进度
    with RESULTS_BUFFER_LOCK:
        global RESULTS_BUFFER
        RESULTS_BUFFER.extend(results)
        PROGRESS['processed_combinations'] += len(transcript_batch)
    
    return len(results)

def flush_results_to_file():
    """将缓冲区结果写入文件"""
    global RESULTS_BUFFER
    
    with RESULTS_BUFFER_LOCK:
        if not RESULTS_BUFFER:
            return 0
        
        # 复制缓冲区内容并清空
        results_to_write = RESULTS_BUFFER.copy()
        RESULTS_BUFFER = []
        PROGRESS['last_flush_time'] = time.time()
    
    # 写入文件
    with open(OUTPUT_FILE, 'a', newline='') as csvfile:
        writer = csv.writer(csvfile)
        for result in results_to_write:
            writer.writerow(result)
    
    return len(results_to_write)

def process_project_rounds(transcript_ids, project_ids):
    """分轮处理项目"""
    # 初始化结果CSV文件
    with open(OUTPUT_FILE, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['transcript_id', 'project_id', 'sra_accession', 'bioproject_id', 'TR', 'EVI', 'TE'])
    
    # 计算总组合数
    total_transcripts = len(transcript_ids)
    total_projects = len(project_ids)
    total_combinations = total_transcripts * total_projects
    print(f"总计需要处理 {total_combinations} 个组合")
    
    # 为提高性能，把项目分成多轮处理
    connection = get_connection()
    if not connection:
        return
    
    # 创建总进度条
    with tqdm(total=total_combinations, desc="总体进度") as pbar:
        # 处理每一轮项目
        for i in range(0, total_projects, MAX_PROJECTS_PER_ROUND):
            round_projects = project_ids[i:i+MAX_PROJECTS_PER_ROUND]
            print(f"\n处理项目批次 {i//MAX_PROJECTS_PER_ROUND + 1}/{(total_projects-1)//MAX_PROJECTS_PER_ROUND + 1}")
            print(f"当前批次项目: {', '.join(round_projects)}")
            
            # 预加载本轮项目数据
            preload_project_data(connection, round_projects)
            
            # 分批处理所有转录本
            for j in range(0, total_transcripts, BATCH_SIZE):
                transcript_batch = transcript_ids[j:j+BATCH_SIZE]
                
                # 预加载TPM数据
                preload_tpm_data(connection, round_projects, transcript_batch)
                
                # 创建任务
                tasks = [(project_id, transcript_batch) for project_id in round_projects]
                
                # 使用线程池处理任务
                with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                    # 提交所有任务
                    futures = [executor.submit(process_batch, task) for task in tasks]
                    
                    # 等待所有任务完成
                    for future in concurrent.futures.as_completed(futures):
                        try:
                            # 获取结果(已处理的条目数)
                            num_processed = future.result()
                        except Exception as e:
                            print(f"任务执行错误: {e}")
                
                # 检查是否需要刷新结果到文件
                current_time = time.time()
                if (current_time - PROGRESS['last_flush_time'] > FLUSH_INTERVAL 
                    or len(RESULTS_BUFFER) >= BUFFER_SIZE):
                    flushed = flush_results_to_file()
                    if flushed:
                        print(f"已写入 {flushed} 条结果到文件")
                
                # 更新进度条
                pbar.update(len(transcript_batch) * len(round_projects))
                
                # 显示当前处理速度
                processed = PROGRESS['processed_combinations']
                elapsed = time.time() - start_time
                speed = processed / elapsed if elapsed > 0 else 0
                remaining = (total_combinations - processed) / speed if speed > 0 else 0
                
                # 转换为小时:分钟:秒
                remaining_hours = int(remaining // 3600)
                remaining_minutes = int((remaining % 3600) // 60)
                remaining_seconds = int(remaining % 60)
                
                print(f"速度: {speed:.2f} 组合/秒, 预计剩余时间: {remaining_hours}:{remaining_minutes:02d}:{remaining_seconds:02d}")
            
            # 清空TPM缓存，避免内存过度使用
            TPM_CACHE.clear()
    
    # 最后刷新剩余结果
    flushed = flush_results_to_file()
    if flushed:
        print(f"最终写入 {flushed} 条结果到文件")
    
    close_connection(connection)

def main():
    global start_time
    
    # 设置开始时间
    start_time = time.time()
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 获取唯一的转录本ID和项目ID
    transcript_ids = get_unique_transcript_ids()
    project_ids = get_unique_project_ids()
    
    # 应用限制条件（用于测试）
    if TRANSCRIPT_LIMIT:
        transcript_ids = transcript_ids[:TRANSCRIPT_LIMIT]
        print(f"已限制为前 {TRANSCRIPT_LIMIT} 个转录本")
        
    if PROJECT_LIMIT:
        project_ids = project_ids[:PROJECT_LIMIT]
        print(f"已限制为前 {PROJECT_LIMIT} 个项目")
    
    # 处理所有组合
    process_project_rounds(transcript_ids, project_ids)
    
    # 打印总运行时间
    elapsed_time = time.time() - start_time
    hours, remainder = divmod(elapsed_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    print("\n处理完成!")
    print(f"总运行时间: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
    print(f"每组合平均时间: {elapsed_time/PROGRESS['processed_combinations']:.6f} 秒")
    print(f"结果已保存到: {OUTPUT_FILE}")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main() 