#!/usr/bin/env python3
import pandas as pd
import csv

print("检查Sample.csv文件中的TRANSLATED TRANSCRIPTS NUMBER列...")

# 读取CSV文件
csv_path = 'Browse-Sample/Sample.csv'

print(f"读取文件: {csv_path}")
df = pd.read_csv(csv_path)

print(f"总行数: {len(df)}")
print(f"列名: {list(df.columns)}")

# 检查TRANSLATED TRANSCRIPTS NUMBER列
col_name = 'TRANSLATED TRANSCRIPTS NUMBER'
if col_name in df.columns:
    print(f"\n{col_name}列存在")
    
    # 统计非空值
    non_null_count = df[col_name].notna().sum()
    print(f"非空值数量: {non_null_count}")
    
    # 统计不是'NA'的值
    not_na_count = (df[col_name] != 'NA').sum()
    print(f"不是'NA'的值数量: {not_na_count}")
    
    # 显示前10个值
    print(f"\n前10个值:")
    for i, value in enumerate(df[col_name].head(10)):
        print(f"第{i+1}行: '{value}' (类型: {type(value)})")
    
    # 检查有效数值的数量
    valid_numbers = 0
    for value in df[col_name]:
        if pd.notna(value) and str(value) != 'NA':
            try:
                int(value)
                valid_numbers += 1
            except (ValueError, TypeError):
                pass
    
    print(f"\n可转换为整数的值数量: {valid_numbers}")
    
    # 显示一些统计信息
    if valid_numbers > 0:
        numeric_values = []
        for value in df[col_name]:
            if pd.notna(value) and str(value) != 'NA':
                try:
                    numeric_values.append(int(value))
                except (ValueError, TypeError):
                    pass
        
        if numeric_values:
            print(f"数值范围: {min(numeric_values)} - {max(numeric_values)}")
            print(f"平均值: {sum(numeric_values)/len(numeric_values):.2f}")

else:
    print(f"错误: 找不到列 '{col_name}'")

# 用CSV reader再检查一次，以确保没有编码问题
print("\n\n使用CSV reader再次检查...")
with open(csv_path, 'r', encoding='utf-8') as f:
    reader = csv.DictReader(f)
    headers = reader.fieldnames
    print(f"CSV headers: {headers}")
    
    if 'TRANSLATED TRANSCRIPTS NUMBER' in headers:
        count = 0
        valid_count = 0
        for row in reader:
            count += 1
            if count > 10:  # 只检查前10行
                break
                
            value = row['TRANSLATED TRANSCRIPTS NUMBER']
            print(f"第{count}行: '{value}'")
            
            if value and value != 'NA':
                try:
                    int(value)
                    valid_count += 1
                except ValueError:
                    pass
        
        print(f"前10行中有效数值: {valid_count}")