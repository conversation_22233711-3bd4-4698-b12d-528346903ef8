# Ensembl基因信息爬取器

从Ensembl网站批量获取基因的基本信息，包括：
- **GENE symbol**: 基因符号
- **Approved Name**: 基因全名/描述  
- **Locus Type**: 基因类型
- **Chromosome**: 染色体位置

## 文件说明

### 主要脚本
- `ensembl_gene_info_scraper.py` - 基因信息爬取核心模块
- `run_gene_info_scraper.py` - 启动脚本，包含多种预设配置
- `test_gene_info_scraper.py` - 测试脚本

### 调试工具
- `debug_html_structure.py` - 调试页面HTML结构
- `debug_redirect.py` - 调试重定向链接

## 快速开始

### 1. 查看可用配置
```bash
python run_gene_info_scraper.py --list-configs
```

### 2. 使用测试配置运行（推荐首次使用）
```bash
python run_gene_info_scraper.py --config test
```

### 3. 使用默认配置运行
```bash
python run_gene_info_scraper.py
```

### 4. 使用快速配置运行（网络良好时）
```bash
python run_gene_info_scraper.py --config fast
```

## 预设配置

| 配置名 | 线程数 | 超时时间 | 延迟 | 说明 |
|--------|--------|----------|------|------|
| test | 2 | 15s | 1.0s | 测试配置，稳定可靠 |
| conservative | 3 | 12s | 0.8s | 保守配置，网络不稳定环境 |
| default | 4 | 10s | 0.5s | 默认配置，平衡性能和稳定性 |
| fast | 6 | 8s | 0.3s | 快速配置，网络稳定环境 |
| aggressive | 8 | 6s | 0.2s | 激进配置，最快速度 |

## 自定义参数

```bash
# 自定义线程数和延迟
python run_gene_info_scraper.py --workers 6 --delay 0.3

# 指定输入输出文件
python run_gene_info_scraper.py --input my_genes.csv --output results.csv

# 组合使用
python run_gene_info_scraper.py --config conservative --workers 5
```

## 输入文件格式

输入CSV文件必须包含 `ensembl_gene_id` 列：

```csv
ensembl_gene_id
ENSG00000003056
ENSG00000004059
ENSG00000173153
...
```

## 输出文件格式

输出CSV文件将包含原始列加上新增的基因信息列：

```csv
ensembl_gene_id,GENE symbol,Approved Name,Locus Type,Chromosome
ENSG00000003056,M6PR,mannose-6-phosphate receptor...,Protein coding,12
ENSG00000004059,ARF5,ADP ribosylation factor 5...,Protein coding,7
...
```

## 功能特性

### ✅ 已实现
- **多线程并发处理** - 显著提升处理速度
- **基因符号提取** - 从页面title准确提取基因符号
- **智能重定向处理** - 自动跟随页面重定向
- **错误处理和重试** - 网络异常时的容错机制
- **进度跟踪** - 实时显示处理进度和成功率
- **多种预设配置** - 适应不同网络环境
- **命令行界面** - 灵活的参数配置
- **日志记录** - 详细的操作日志

### 🚧 部分实现
- **基因描述提取** - 需要跟随重定向到Summary页面
- **基因类型提取** - 需要访问详细的基因页面
- **染色体位置提取** - 需要解析页面中的位置信息

### ❗ 注意事项
由于Ensembl网站使用动态JavaScript生成详细信息，目前主要能可靠提取基因符号。其他信息（描述、类型、染色体）的提取成功率可能较低。

## 使用建议

1. **首次使用**：建议使用 `test` 配置进行小规模测试
2. **网络不稳定**：使用 `conservative` 配置
3. **大规模处理**：使用 `default` 或 `fast` 配置
4. **性能优化**：根据网络情况调整线程数和延迟时间

## 常见问题

### Q: 为什么有些基因信息获取不到？
A: 某些基因可能：
- 不存在于Ensembl数据库
- 页面结构发生变化
- 网络超时导致访问失败

### Q: 如何提高成功率？
A: 建议：
- 使用较长的超时时间（15s以上）
- 减少并发线程数（2-4个）
- 增加请求间延迟（1s以上）

### Q: 程序中断后如何恢复？
A: 程序会自动记录进度，重新运行时会从中断处继续（如果使用相同的输出文件名）。

## 技术实现

- **语言**: Python 3.7+
- **主要依赖**: requests, BeautifulSoup4, pandas
- **并发模型**: ThreadPoolExecutor
- **解析器**: HTML文本解析（非浏览器自动化）

## 性能参考

- **处理速度**: 约500-1000个基因/小时（取决于网络和配置）
- **成功率**: 基因符号提取 >95%，其他信息 <50%
- **资源占用**: 内存 <100MB，网络带宽较低 