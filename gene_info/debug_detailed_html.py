#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import re
import time

def debug_html_structure(gene_id):
    """详细分析HTML结构，查找信息提取失败的原因"""
    
    url = f"http://www.ensembl.org/id/{gene_id}"
    print(f"分析基因 {gene_id} 的HTML结构...")
    print(f"URL: {url}")
    print("="*80)
    
    try:
        # 发送请求
        response = requests.get(url, timeout=15)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 1. 检查页面title
            print("1. 页面Title:")
            title = soup.find('title')
            if title:
                print(f"   {title.get_text()}")
            else:
                print("   未找到title标签")
            
            # 2. 查找所有div元素带有特定ID
            print("\n2. 查找关键面板:")
            panel_ids = ['ensembl_panel_1', 'ensembl_panel_2', 'GeneSummary']
            for panel_id in panel_ids:
                panel = soup.find('div', {'id': panel_id})
                if panel:
                    print(f"   找到面板: {panel_id}")
                else:
                    print(f"   未找到面板: {panel_id}")
            
            # 3. 查找summary_panel类
            print("\n3. 查找summary_panel:")
            summary_panels = soup.find_all('div', class_='summary_panel')
            print(f"   找到 {len(summary_panels)} 个summary_panel")
            
            # 4. 查找所有的row类
            print("\n4. 查找row结构:")
            rows = soup.find_all('div', class_='row')
            print(f"   找到 {len(rows)} 个row元素")
            
            if rows:
                print("   前10个row的标签内容:")
                for i, row in enumerate(rows[:10]):
                    lhs = row.find('div', class_='lhs')
                    rhs = row.find('div', class_='rhs')
                    if lhs and rhs:
                        label = lhs.get_text(strip=True)
                        content = rhs.get_text(strip=True)[:100]  # 只显示前100个字符
                        print(f"     Row {i+1}: {label} -> {content}...")
            
            # 5. 检查h1标签
            print("\n5. 查找H1标签:")
            h1_tags = soup.find_all('h1')
            for i, h1 in enumerate(h1_tags):
                print(f"   H1 {i+1}: {h1.get_text(strip=True)}")
                if h1.get('class'):
                    print(f"           类: {h1.get('class')}")
            
            # 6. 搜索特定文本模式
            print("\n6. 搜索关键词:")
            keywords = ['Description', 'Location', 'Gene type', 'Chromosome', 'Protein coding']
            for keyword in keywords:
                # 查找包含关键词的所有元素
                elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
                print(f"   '{keyword}': 找到 {len(elements)} 个匹配")
                if elements:
                    # 显示第一个匹配的上下文
                    first_match = elements[0]
                    parent = first_match.parent
                    if parent:
                        print(f"     上下文: {parent.get_text(strip=True)[:150]}...")
            
            # 7. 查找所有twocol类（这可能包含我们需要的信息）
            print("\n7. 查找twocol结构:")
            twocols = soup.find_all('div', class_='twocol')
            print(f"   找到 {len(twocols)} 个twocol元素")
            
            for i, twocol in enumerate(twocols):
                print(f"   Twocol {i+1}:")
                rows = twocol.find_all('div', class_='row')
                for j, row in enumerate(rows[:5]):  # 只显示前5行
                    lhs = row.find('div', class_='lhs')
                    rhs = row.find('div', class_='rhs')
                    if lhs and rhs:
                        label = lhs.get_text(strip=True)
                        content = rhs.get_text(strip=True)[:80]
                        print(f"     {label}: {content}...")
            
            # 8. 保存完整HTML用于分析（可选）
            print(f"\n8. HTML内容长度: {len(response.content)} 字节")
            
        else:
            print(f"请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"调试时出错: {e}")

if __name__ == "__main__":
    # 测试一个基因
    test_gene = "ENSG00000003056"  # M6PR
    debug_html_structure(test_gene)
    
    print("\n" + "="*80)
    print("调试完成！请检查上面的输出，查看HTML结构是否符合预期。") 