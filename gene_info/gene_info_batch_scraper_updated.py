import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import os
import json
import random
from tqdm import tqdm

def fetch_gene_info(gene_id, max_retries=3, retry_delay=5):
    """
    Fetch gene information from NCBI Gene database with retry logic
    
    Args:
        gene_id: The gene ID or transcript ID (e.g., ENST00000673477)
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds
        
    Returns:
        Dictionary containing the gene information
    """
    url = f"https://www.ncbi.nlm.nih.gov/gene/?term={gene_id}"
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Initialize the results dictionary
                gene_info = {}
                gene_info['gene_id'] = gene_id
                
                # Extract information from the summary section
                summary_div = soup.find('div', class_='rprt-section gene-summary')
                if summary_div:
                    dl = summary_div.find('dl', id='summaryDl')
                    if dl:
                        # Find all dt and dd elements
                        dt_elements = dl.find_all('dt')
                        dd_elements = dl.find_all('dd')
                        
                        # Process each dt-dd pair until we hit a non-summary field
                        for i in range(len(dt_elements)):
                            if i < len(dd_elements):
                                # Get the key (remove extra whitespace and newlines)
                                key = dt_elements[i].get_text(strip=True)
                                
                                # For multi-line dt elements, normalize the whitespace
                                key = ' '.join(key.split())
                                
                                # Skip all fields after Summary
                                if key == 'Expression' or key == 'Orthologs' or 'NEW' in key:
                                    continue
                                
                                # Get the value
                                dd = dd_elements[i]
                                
                                # Handle special cases for fields with links
                                if "See related" in key:
                                    # Get all links
                                    links = dd.find_all('a')
                                    link_texts = [link.get_text(strip=True) for link in links]
                                    gene_info[key] = "; ".join(link_texts)
                                elif "Primary source" in key:
                                    # Get the link text
                                    link = dd.find('a')
                                    if link:
                                        gene_info[key] = link.get_text(strip=True)
                                    else:
                                        gene_info[key] = dd.get_text(strip=True)
                                # Handle both naming conventions for gene symbols and names
                                elif any(field in key for field in ["Official Symbol", "Gene symbol"]):
                                    text = dd.get_text(strip=True)
                                    if "provided by" in text:
                                        gene_info[key] = text.split("provided by")[0].strip()
                                    else:
                                        gene_info[key] = text
                                elif any(field in key for field in ["Official Full Name", "Gene description"]):
                                    text = dd.get_text(strip=True)
                                    if "provided by" in text:
                                        gene_info[key] = text.split("provided by")[0].strip()
                                    else:
                                        gene_info[key] = text
                                else:
                                    gene_info[key] = dd.get_text(strip=True)
                                
                                # Stop after processing Summary
                                if "Summary" in key:
                                    break
                
                # Extract chromosome location
                chr_info = soup.find('dl', class_='dl-chr-info')
                if chr_info:
                    location_dt = chr_info.find('dt', string=lambda text: text and "Location" in text)
                    if location_dt:
                        location_dd = location_dt.find_next_sibling('dd')
                        if location_dd:
                            gene_info["Location"] = location_dd.get_text(strip=True)
                
                # If we have at least one piece of information beyond the gene_id, consider it successful
                if len(gene_info) > 1:
                    return gene_info
                else:
                    print(f"No gene information found for {gene_id}")
                    return {"gene_id": gene_id, "Status": "No information found"}
            
            elif response.status_code == 429:  # Too Many Requests
                print(f"Rate limit exceeded. Waiting {retry_delay} seconds before retry...")
                time.sleep(retry_delay * (attempt + 1))  # Increasing delay for each retry
                continue
            else:
                print(f"Error fetching {gene_id}: Status code {response.status_code}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                return {"gene_id": gene_id, "Status": f"Error: HTTP {response.status_code}"}
        
        except Exception as e:
            print(f"Exception when fetching {gene_id}: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            return {"gene_id": gene_id, "Status": f"Error: {str(e)}"}
    
    # If we've exhausted all retries
    return {"gene_id": gene_id, "Status": "Failed after retries"}

def process_gene_batch(gene_ids, output_file, batch_size=100, checkpoint_interval=50):
    """
    Process gene IDs in batches with checkpointing
    
    Args:
        gene_ids: List of gene IDs to process
        output_file: Path to the output CSV/JSON file
        batch_size: Number of genes to process in a batch before pausing
        checkpoint_interval: Number of genes to process before saving progress
    """
    results = []
    checkpoint_file = f"{output_file}.checkpoint"
    
    # Check if checkpoint exists and load it
    processed_ids = set()
    if os.path.exists(checkpoint_file):
        if checkpoint_file.endswith('.json.checkpoint'):
            with open(checkpoint_file, 'r') as f:
                checkpoint_data = json.load(f)
                results = checkpoint_data
                processed_ids = set(item['gene_id'] for item in checkpoint_data)
        else:
            checkpoint_df = pd.read_csv(checkpoint_file)
            results = checkpoint_df.to_dict('records')
            processed_ids = set(checkpoint_df['gene_id'].tolist())
        print(f"Loaded {len(processed_ids)} already processed genes from checkpoint")
    
    # Filter out already processed IDs
    remaining_ids = [gene_id for gene_id in gene_ids if gene_id not in processed_ids]
    print(f"Processing {len(remaining_ids)} remaining genes")
    
    # Process genes in batches
    for i in range(0, len(remaining_ids), batch_size):
        batch = remaining_ids[i:i+batch_size]
        print(f"\nProcessing batch {i//batch_size + 1}/{(len(remaining_ids) + batch_size - 1)//batch_size}")
        
        for j, gene_id in enumerate(tqdm(batch, desc="Fetching gene information")):
            gene_info = fetch_gene_info(gene_id)
            if gene_info:
                results.append(gene_info)
            
            # Add a small random delay to avoid predictable request patterns
            time.sleep(0.5 + random.random() * 0.5)
            
            # Checkpoint after every checkpoint_interval genes
            if (j + 1) % checkpoint_interval == 0 or j == len(batch) - 1:
                # Save checkpoint based on output file format
                if output_file.endswith('.json'):
                    with open(checkpoint_file, 'w') as f:
                        json.dump(results, f, indent=2)
                else:
                    temp_df = pd.DataFrame(results)
                    temp_df.to_csv(checkpoint_file, index=False)
                print(f"Saved checkpoint ({len(results)} genes processed)")
        
        # Pause between batches to avoid overloading the server
        if i + batch_size < len(remaining_ids):
            pause_time = 30 + random.randint(0, 30)  # 30-60 seconds
            print(f"Pausing for {pause_time} seconds before next batch...")
            time.sleep(pause_time)
    
    # Save the final results
    if results:
        if output_file.endswith('.json'):
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2)
        else:
            df = pd.DataFrame(results)
            df.to_csv(output_file, index=False)
        print(f"Data saved to {output_file}")
        
        # Remove checkpoint file if successful
        if os.path.exists(checkpoint_file):
            os.remove(checkpoint_file)
    else:
        print("No results to save.")

def process_gene_file(input_file, output_file, id_column, batch_size=100, checkpoint_interval=50):
    """
    Process a CSV file containing gene IDs
    
    Args:
        input_file: Path to the input CSV file
        output_file: Path to the output file (CSV or JSON)
        id_column: Name of the column containing gene IDs
        batch_size: Number of genes to process in a batch
        checkpoint_interval: Number of genes to process before saving progress
    """
    df = pd.read_csv(input_file)
    gene_ids = df[id_column].tolist()
    
    # Remove any NaN values
    gene_ids = [str(gene_id) for gene_id in gene_ids if str(gene_id) != 'nan']
    
    print(f"Loaded {len(gene_ids)} gene IDs from {input_file}")
    process_gene_batch(gene_ids, output_file, batch_size, checkpoint_interval)

# For testing with a single gene ID
def test_fetch_gene(gene_id):
    """
    Test the fetch_gene_info function with a single gene ID
    
    Args:
        gene_id: The gene ID to test with
    """
    print(f"Testing with gene ID: {gene_id}")
    gene_info = fetch_gene_info(gene_id)
    print(json.dumps(gene_info, indent=2))
    return gene_info

# Directly run the code with sensible defaults for the large dataset
if __name__ == "__main__":
    # Test with a single gene first to verify the format handling
    test_gene_id = "ENSG00000290203"  # The example gene with "Gene symbol" format
    test_fetch_gene(test_gene_id)
    
    # Full processing configuration
    input_file = "ensembl_gene_id_unique.csv"
    output_file = "gene_info_results.json"  # Output in JSON format
    id_column = "ensembl_gene_id"
    batch_size = 50  # Process 50 genes per batch
    checkpoint_interval = 10  # Save every 10 genes
    
    print(f"\nStarting batch processing of gene IDs from {input_file}")
    try:
        process_gene_file(
            input_file, 
            output_file, 
            id_column, 
            batch_size, 
            checkpoint_interval
        )
    except KeyboardInterrupt:
        print("\nProcess interrupted by user. Progress has been saved in the checkpoint file.")
    except Exception as e:
        print(f"\nError: {str(e)}")
        print("Progress has been saved in the checkpoint file.") 