#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import pandas as pd
import json
import time
import logging
import sys
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
import queue
import signal
from contextlib import contextmanager

# 设置日志
def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s',
        handlers=[
            logging.FileHandler('batch_transcript_scraper.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

setup_logging()

# 全局变量
thread_local = threading.local()
progress_lock = Lock()
processed_count = 0
success_count = 0
shutdown_event = threading.Event()

class TranscriptScraper:
    """转录本爬取器类"""
    
    def __init__(self, max_workers=4, batch_size=500, timeout=8, max_retries=3):
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.timeout = timeout
        self.max_retries = max_retries
        self.checkpoint_file = 'transcript_checkpoint.csv'
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        chrome_options.add_argument('--disable-logging')
        chrome_options.add_argument('--log-level=3')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')
        chrome_options.add_argument('--disable-javascript')
        chrome_options.add_argument('--disable-css')
        chrome_options.add_argument('--disable-background-timer-throttling')
        chrome_options.add_argument('--disable-backgrounding-occluded-windows')
        chrome_options.add_argument('--disable-renderer-backgrounding')
        
        try:
            driver = webdriver.Chrome(options=chrome_options)
            driver.set_page_load_timeout(self.timeout)
            return driver
        except Exception as e:
            logging.error(f"Chrome驱动设置失败: {e}")
            return None
    
    def get_thread_driver(self):
        """获取线程本地的WebDriver"""
        if shutdown_event.is_set():
            return None
            
        if not hasattr(thread_local, 'driver') or thread_local.driver is None:
            thread_local.driver = self.setup_driver()
        return thread_local.driver
    
    def close_thread_driver(self):
        """关闭线程本地的WebDriver"""
        if hasattr(thread_local, 'driver') and thread_local.driver:
            try:
                thread_local.driver.quit()
            except:
                pass
            thread_local.driver = None
    
    def scrape_single_gene_attempt(self, gene_id, attempt=1):
        """单次尝试爬取基因转录本数据"""
        driver = self.get_thread_driver()
        if not driver:
            return None
        
        url = f"http://www.ensembl.org/id/{gene_id}"
        
        try:
            # 访问页面
            driver.get(url)
            
            # 等待页面加载
            wait = WebDriverWait(driver, self.timeout)
            
            # 查找并点击"Show transcript table"按钮
            try:
                transcript_button = wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, 'a[rel="transcripts_table"].button.toggle'))
                )
                
                # 检查按钮状态并点击
                if "closed" in transcript_button.get_attribute("class"):
                    driver.execute_script("arguments[0].click();", transcript_button)
                    time.sleep(0.5)
                    
            except TimeoutException:
                return None
            
            # 等待转录本表格出现
            try:
                transcript_table = wait.until(
                    EC.presence_of_element_located((By.ID, "transcripts_table"))
                )
            except TimeoutException:
                return None
            
            # 提取表格数据
            transcript_data = []
            
            try:
                tbody = transcript_table.find_element(By.TAG_NAME, "tbody")
                rows = tbody.find_elements(By.TAG_NAME, "tr")
                
                for row in rows:
                    try:
                        cells = row.find_elements(By.TAG_NAME, "td")
                        if len(cells) >= 2:
                            # 提取Transcript ID（第一列）
                            transcript_id_cell = cells[0]
                            transcript_link = transcript_id_cell.find_element(By.TAG_NAME, "a")
                            transcript_id = transcript_link.text.strip()
                            
                            # 提取Name（第二列）
                            name_cell = cells[1]
                            name = name_cell.text.strip()
                            
                            transcript_data.append({
                                "transcript_id": transcript_id,
                                "name": name
                            })
                            
                    except Exception:
                        continue
                        
            except NoSuchElementException:
                pass
            
            # 将转录本数据转换为JSON字符串
            transcripts_json = json.dumps(transcript_data, ensure_ascii=False)
            return len(transcript_data), transcripts_json
            
        except WebDriverException as e:
            # WebDriver异常，重新创建驱动
            if attempt == 1:  # 只在第一次尝试时记录详细错误
                logging.warning(f"WebDriver异常 {gene_id} (尝试 {attempt}): {str(e)[:100]}...")
            self.close_thread_driver()
            return None
            
        except Exception as e:
            if attempt == 1:  # 只在第一次尝试时记录详细错误
                logging.warning(f"爬取异常 {gene_id} (尝试 {attempt}): {str(e)[:100]}...")
            return None
    
    def scrape_single_gene(self, gene_id, total_genes):
        """爬取单个基因的转录本数据，支持多次重试"""
        global processed_count, success_count
        
        if shutdown_event.is_set():
            return gene_id, 0, "[]"
        
        # 尝试多次爬取
        for attempt in range(1, self.max_retries + 1):
            if shutdown_event.is_set():
                break
                
            result = self.scrape_single_gene_attempt(gene_id, attempt)
            
            if result is not None:
                transcript_count, transcripts_json = result
                
                # 更新进度统计
                with progress_lock:
                    processed_count += 1
                    if transcript_count > 0:
                        success_count += 1
                        if attempt > 1:
                            logging.info(f"✓ {gene_id} 在第 {attempt} 次尝试成功: {transcript_count} 个转录本")
                    
                    if processed_count % 50 == 0:
                        success_rate = (success_count / processed_count * 100) if processed_count > 0 else 0
                        logging.info(f"进度: {processed_count}/{total_genes} ({processed_count/total_genes*100:.1f}%) - 成功: {success_count} ({success_rate:.1f}%)")
                
                return gene_id, transcript_count, transcripts_json
            
            # 如果失败且还有重试机会，等待一段时间后重试
            if attempt < self.max_retries:
                wait_time = attempt * 0.5  # 递增等待时间：0.5s, 1s, 1.5s...
                time.sleep(wait_time)
                
                # 每次重试前强制重新创建WebDriver
                self.close_thread_driver()
        
        # 所有尝试都失败
        with progress_lock:
            processed_count += 1
            if processed_count % 50 == 0:
                success_rate = (success_count / processed_count * 100) if processed_count > 0 else 0
                logging.info(f"进度: {processed_count}/{total_genes} ({processed_count/total_genes*100:.1f}%) - 成功: {success_count} ({success_rate:.1f}%)")
        
        logging.warning(f"✗ {gene_id} 在 {self.max_retries} 次尝试后仍然失败")
        return gene_id, 0, "[]"
    
    def save_checkpoint(self, df):
        """保存检查点"""
        try:
            df.to_csv(self.checkpoint_file, index=False)
            logging.info(f"检查点已保存: {self.checkpoint_file}")
        except Exception as e:
            logging.error(f"保存检查点失败: {e}")
    
    def process_genes_batch(self, gene_ids, df):
        """批量处理基因，使用多线程"""
        global processed_count, success_count
        processed_count = 0
        success_count = 0
        total_genes = len(gene_ids)
        
        logging.info(f"开始使用 {self.max_workers} 个线程处理 {total_genes} 个基因")
        logging.info(f"批次大小: {self.batch_size}, 超时时间: {self.timeout}秒, 最大重试次数: {self.max_retries}")
        
        results = {}
        
        try:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_gene = {
                    executor.submit(self.scrape_single_gene, gene_id, total_genes): gene_id 
                    for gene_id in gene_ids
                }
                
                # 收集结果
                for future in as_completed(future_to_gene):
                    if shutdown_event.is_set():
                        logging.info("收到关闭信号，停止处理...")
                        break
                        
                    gene_id = future_to_gene[future]
                    try:
                        gene_id_result, transcript_count, transcripts_json = future.result(timeout=self.timeout+5)
                        results[gene_id_result] = {
                            'transcript_count': transcript_count,
                            'transcripts': transcripts_json
                        }
                    except Exception as e:
                        logging.error(f"处理基因 {gene_id} 时出错: {e}")
                        results[gene_id] = {
                            'transcript_count': 0,
                            'transcripts': "[]"
                        }
                    
                    # 每处理一定数量的基因保存一次检查点
                    if len(results) % self.batch_size == 0:
                        logging.info(f"保存检查点 - 已处理 {len(results)} 个基因...")
                        self.update_dataframe(df, results)
                        self.save_checkpoint(df)
                        
        except KeyboardInterrupt:
            logging.info("收到中断信号，正在保存进度...")
            shutdown_event.set()
        finally:
            # 清理所有线程的驱动
            for _ in range(self.max_workers):
                try:
                    self.close_thread_driver()
                except:
                    pass
        
        return results
    
    def update_dataframe(self, df, results):
        """更新DataFrame"""
        for gene_id, data in results.items():
            if gene_id in df['ensembl_gene_id'].values:
                idx = df[df['ensembl_gene_id'] == gene_id].index[0]
                df.at[idx, 'transcript_count'] = data['transcript_count']
                df.at[idx, 'transcripts'] = data['transcripts']
    
    def run(self, input_file="ensembl_gene_id_with_info.csv", 
            output_file="ensembl_gene_id_with_transcript_info.csv"):
        """运行批量处理"""
        
        # 检查输入文件
        if not os.path.exists(input_file):
            logging.error(f"输入文件不存在: {input_file}")
            return False
        
        # 检查是否有检查点文件
        if os.path.exists(self.checkpoint_file):
            logging.info(f"发现检查点文件，继续之前的进度...")
            df = pd.read_csv(self.checkpoint_file)
        else:
            # 读取输入文件
            logging.info(f"读取输入文件: {input_file}")
            df = pd.read_csv(input_file)
            
            # 添加新列
            df['transcript_count'] = 0
            df['transcripts'] = "[]"
        
        # 获取需要处理的基因ID列表
        if 'transcript_count' not in df.columns:
            df['transcript_count'] = 0
            df['transcripts'] = "[]"
            
        # 找出还未处理的基因（transcript_count为0或空的）
        unprocessed_mask = (df['transcript_count'] == 0) | (df['transcript_count'].isna())
        gene_ids_to_process = df[unprocessed_mask]['ensembl_gene_id'].tolist()
        
        if not gene_ids_to_process:
            logging.info("所有基因已处理完成")
            return True
            
        logging.info(f"需要处理 {len(gene_ids_to_process)} 个基因")
        
        # 设置信号处理
        def signal_handler(signum, frame):
            logging.info("收到中断信号，正在安全退出...")
            shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 批量处理基因
            results = self.process_genes_batch(gene_ids_to_process, df)
            
            # 更新DataFrame
            logging.info("更新数据框...")
            self.update_dataframe(df, results)
            
            # 保存最终结果
            logging.info(f"保存最终结果到: {output_file}")
            df.to_csv(output_file, index=False)
            
            # 删除检查点文件
            if os.path.exists(self.checkpoint_file):
                os.remove(self.checkpoint_file)
                logging.info("检查点文件已删除")
                
        except Exception as e:
            logging.error(f"处理过程中出错: {e}")
            # 保存当前进度
            self.save_checkpoint(df)
            return False
        
        # 统计信息
        total_genes = len(df)
        successful_genes = len(df[df['transcript_count'] > 0])
        total_transcripts = df['transcript_count'].sum()
        
        logging.info("\n" + "="*50)
        logging.info("最终统计结果")
        logging.info("="*50)
        logging.info(f"总基因数: {total_genes:,}")
        logging.info(f"成功获取转录本的基因数: {successful_genes:,}")
        logging.info(f"失败的基因数: {total_genes - successful_genes:,}")
        logging.info(f"总转录本数: {total_transcripts:,}")
        logging.info(f"成功率: {(successful_genes/total_genes*100):.2f}%")
        
        # 显示前几个结果示例
        logging.info("\n结果示例:")
        successful_df = df[df['transcript_count'] > 0].head(5)
        for _, row in successful_df.iterrows():
            gene_id = row['ensembl_gene_id']
            gene_symbol = row['GENE symbol']
            transcript_count = row['transcript_count']
            logging.info(f"  {gene_id} ({gene_symbol}): {transcript_count} 个转录本")
        
        return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='批量爬取Ensembl转录本数据')
    parser.add_argument('--workers', type=int, default=4, help='线程数 (默认: 4)')
    parser.add_argument('--batch-size', type=int, default=500, help='批次大小 (默认: 500)')
    parser.add_argument('--timeout', type=int, default=8, help='超时时间秒数 (默认: 8)')
    parser.add_argument('--max-retries', type=int, default=5, help='最大重试次数 (默认: 3)')
    parser.add_argument('--input', default='ensembl_gene_id_with_full_info_2.csv', help='输入文件')
    parser.add_argument('--output', default='ensembl_gene_id_with_transcript_info_2.csv', help='输出文件')
    
    args = parser.parse_args()
    
    # 创建爬取器实例
    scraper = TranscriptScraper(
        max_workers=args.workers,
        batch_size=args.batch_size,
        timeout=args.timeout,
        max_retries=args.max_retries
    )
    
    # 运行爬取
    success = scraper.run(args.input, args.output)
    
    if success:
        logging.info("批量处理完成!")
        sys.exit(0)
    else:
        logging.error("批量处理失败!")
        sys.exit(1)

if __name__ == "__main__":
    main() 