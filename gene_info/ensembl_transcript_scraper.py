#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import pandas as pd
import json
import time
import logging
import sys
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ensembl_transcript_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

# 线程本地存储和锁
thread_local = threading.local()
progress_lock = Lock()
processed_count = 0

def setup_driver():
    """设置Chrome浏览器驱动"""
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
    chrome_options.add_argument('--disable-logging')
    chrome_options.add_argument('--log-level=3')
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--disable-plugins')
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        logging.error(f"Chrome驱动设置失败: {e}")
        return None

def get_thread_driver():
    """获取线程本地的WebDriver"""
    if not hasattr(thread_local, 'driver'):
        thread_local.driver = setup_driver()
    return thread_local.driver

def close_thread_driver():
    """关闭线程本地的WebDriver"""
    if hasattr(thread_local, 'driver') and thread_local.driver:
        try:
            thread_local.driver.quit()
        except:
            pass
        thread_local.driver = None

def scrape_transcript_data(gene_id, total_genes):
    """爬取指定基因ID的转录本数据"""
    global processed_count
    
    driver = get_thread_driver()
    if not driver:
        return gene_id, 0, "[]"
    
    url = f"http://www.ensembl.org/id/{gene_id}"
    
    try:
        # 访问页面
        driver.get(url)
        
        # 等待页面加载（缩短等待时间）
        wait = WebDriverWait(driver, 8)
        
        # 查找并点击"Show transcript table"按钮
        try:
            transcript_button = wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'a[rel="transcripts_table"].button.toggle'))
            )
            
            # 检查按钮是否处于关闭状态（需要点击）
            if "closed" in transcript_button.get_attribute("class"):
                driver.execute_script("arguments[0].click();", transcript_button)
                time.sleep(1)  # 缩短等待时间
                
        except TimeoutException:
            with progress_lock:
                processed_count += 1
                if processed_count % 100 == 0:
                    logging.info(f"已处理 {processed_count}/{total_genes} 个基因")
            return gene_id, 0, "[]"
        
        # 等待转录本表格出现
        try:
            transcript_table = wait.until(
                EC.presence_of_element_located((By.ID, "transcripts_table"))
            )
        except TimeoutException:
            with progress_lock:
                processed_count += 1
                if processed_count % 100 == 0:
                    logging.info(f"已处理 {processed_count}/{total_genes} 个基因")
            return gene_id, 0, "[]"
        
        # 提取表格数据
        transcript_data = []
        
        try:
            # 查找表格的tbody
            tbody = transcript_table.find_element(By.TAG_NAME, "tbody")
            rows = tbody.find_elements(By.TAG_NAME, "tr")
            
            for row in rows:
                try:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if len(cells) >= 2:  # 至少需要Transcript ID和Name列
                        # 提取Transcript ID（第一列）
                        transcript_id_cell = cells[0]
                        transcript_link = transcript_id_cell.find_element(By.TAG_NAME, "a")
                        transcript_id = transcript_link.text.strip()
                        
                        # 提取Name（第二列）
                        name_cell = cells[1]
                        name = name_cell.text.strip()
                        
                        transcript_data.append({
                            "transcript_id": transcript_id,
                            "name": name
                        })
                        
                except Exception:
                    continue
                    
        except NoSuchElementException:
            pass
        
        # 更新进度
        with progress_lock:
            processed_count += 1
            if processed_count % 100 == 0:
                logging.info(f"已处理 {processed_count}/{total_genes} 个基因")
        
        # 将转录本数据转换为JSON字符串
        transcripts_json = json.dumps(transcript_data, ensure_ascii=False)
        return gene_id, len(transcript_data), transcripts_json
        
    except Exception as e:
        with progress_lock:
            processed_count += 1
            if processed_count % 100 == 0:
                logging.info(f"已处理 {processed_count}/{total_genes} 个基因")
        return gene_id, 0, "[]"

def save_checkpoint(df, checkpoint_file):
    """保存检查点"""
    try:
        df.to_csv(checkpoint_file, index=False)
        logging.info(f"检查点已保存: {checkpoint_file}")
    except Exception as e:
        logging.error(f"保存检查点失败: {e}")

def process_genes_batch(gene_ids, df, max_workers=4):
    """批量处理基因，使用多线程"""
    global processed_count
    processed_count = 0
    total_genes = len(gene_ids)
    
    logging.info(f"开始使用 {max_workers} 个线程处理 {total_genes} 个基因")
    
    # 创建结果字典
    results = {}
    
    try:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_gene = {
                executor.submit(scrape_transcript_data, gene_id, total_genes): gene_id 
                for gene_id in gene_ids
            }
            
            # 收集结果
            for future in as_completed(future_to_gene):
                gene_id = future_to_gene[future]
                try:
                    gene_id_result, transcript_count, transcripts_json = future.result()
                    results[gene_id_result] = {
                        'transcript_count': transcript_count,
                        'transcripts': transcripts_json
                    }
                except Exception as e:
                    logging.error(f"处理基因 {gene_id} 时出错: {e}")
                    results[gene_id] = {
                        'transcript_count': 0,
                        'transcripts': "[]"
                    }
                
                # 每处理1000个基因保存一次检查点
                if len(results) % 1000 == 0:
                    # 更新DataFrame
                    for gid in results:
                        if gid in df['ensembl_gene_id'].values:
                            idx = df[df['ensembl_gene_id'] == gid].index[0]
                            df.at[idx, 'transcript_count'] = results[gid]['transcript_count']
                            df.at[idx, 'transcripts'] = results[gid]['transcripts']
                    
                    save_checkpoint(df, 'transcript_checkpoint.csv')
                    
    finally:
        # 确保清理所有线程的驱动
        for _ in range(max_workers):
            try:
                close_thread_driver()
            except:
                pass
    
    return results

def main():
    """主函数"""
    input_file = "ensembl_gene_id_with_info.csv"
    output_file = "ensembl_gene_id_with_transcript_info.csv"
    checkpoint_file = "transcript_checkpoint.csv"
    
    # 检查输入文件
    if not os.path.exists(input_file):
        logging.error(f"输入文件不存在: {input_file}")
        return
    
    # 检查是否有检查点文件
    if os.path.exists(checkpoint_file):
        logging.info(f"发现检查点文件，继续之前的进度...")
        df = pd.read_csv(checkpoint_file)
    else:
        # 读取输入文件
        logging.info(f"读取输入文件: {input_file}")
        df = pd.read_csv(input_file)
        
        # 添加新列
        df['transcript_count'] = 0
        df['transcripts'] = "[]"
    
    # 获取需要处理的基因ID列表
    if 'transcript_count' not in df.columns or df['transcript_count'].isna().any():
        # 找出还未处理的基因
        unprocessed_mask = (df['transcript_count'] == 0) | (df['transcript_count'].isna())
        gene_ids_to_process = df[unprocessed_mask]['ensembl_gene_id'].tolist()
    else:
        gene_ids_to_process = []
    
    if gene_ids_to_process:
        logging.info(f"需要处理 {len(gene_ids_to_process)} 个基因")
        
        # 批量处理基因
        results = process_genes_batch(gene_ids_to_process, df, max_workers=4)
        
        # 更新DataFrame
        logging.info("更新数据框...")
        for gene_id, data in results.items():
            if gene_id in df['ensembl_gene_id'].values:
                idx = df[df['ensembl_gene_id'] == gene_id].index[0]
                df.at[idx, 'transcript_count'] = data['transcript_count']
                df.at[idx, 'transcripts'] = data['transcripts']
    else:
        logging.info("所有基因已处理完成")
    
    # 保存最终结果
    logging.info(f"保存最终结果到: {output_file}")
    df.to_csv(output_file, index=False)
    
    # 删除检查点文件
    if os.path.exists(checkpoint_file):
        os.remove(checkpoint_file)
    
    # 统计信息
    total_genes = len(df)
    successful_genes = len(df[df['transcript_count'] > 0])
    total_transcripts = df['transcript_count'].sum()
    
    logging.info("\n=== 最终统计 ===")
    logging.info(f"总基因数: {total_genes}")
    logging.info(f"成功获取转录本的基因数: {successful_genes}")
    logging.info(f"失败的基因数: {total_genes - successful_genes}")
    logging.info(f"总转录本数: {total_transcripts}")
    logging.info(f"成功率: {(successful_genes/total_genes*100):.1f}%")
    
    # 显示前几个结果示例
    logging.info("\n=== 结果示例 ===")
    successful_df = df[df['transcript_count'] > 0].head(5)
    for _, row in successful_df.iterrows():
        gene_id = row['ensembl_gene_id']
        gene_symbol = row['GENE symbol']
        transcript_count = row['transcript_count']
        logging.info(f"{gene_id} ({gene_symbol}): {transcript_count} 个转录本")

if __name__ == "__main__":
    main() 