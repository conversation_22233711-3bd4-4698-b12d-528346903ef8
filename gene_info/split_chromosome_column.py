#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def split_chromosome_column(input_file, output_file):
    """
    拆分Chromosome列为Chromosome和Chromosome_URL两列
    
    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径
    """
    
    logging.info(f"读取文件: {input_file}")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        
        logging.info(f"文件包含 {len(df)} 行数据")
        logging.info(f"列名: {list(df.columns)}")
        
        # 检查是否存在Chromosome列
        if 'Chromosome' not in df.columns:
            logging.error("文件中没有找到Chromosome列")
            return False
        
        # 创建新列
        df['Chromosome_URL'] = ''
        
        # 处理每一行的Chromosome数据
        processed_count = 0
        split_count = 0
        
        for idx, row in df.iterrows():
            chromosome_value = str(row['Chromosome'])
            
            # 跳过空值
            if pd.isna(row['Chromosome']) or chromosome_value == 'nan' or chromosome_value == '':
                continue
                
            processed_count += 1
            
            # 查找最后一个冒号，分割位置信息和URL
            # 格式: "Chromosome 22: 37,570,248-37,582,616:https://ensembl.org/..."
            if ':https://' in chromosome_value:
                # 找到最后一个 :https:// 之前的部分作为染色体信息
                parts = chromosome_value.split(':https://')
                if len(parts) >= 2:
                    chromosome_info = parts[0]  # "Chromosome 22: 37,570,248-37,582,616"
                    url = 'https://' + ':https://'.join(parts[1:])  # 重新组合URL部分
                    
                    # 更新数据
                    df.at[idx, 'Chromosome'] = chromosome_info
                    df.at[idx, 'Chromosome_URL'] = url
                    split_count += 1
                    
                    if split_count <= 5:  # 显示前5个处理示例
                        logging.info(f"示例 {split_count}:")
                        logging.info(f"  原始: {chromosome_value[:100]}...")
                        logging.info(f"  拆分后 - 染色体: {chromosome_info}")
                        logging.info(f"  拆分后 - URL: {url}")
            
            # 进度显示
            if processed_count % 1000 == 0:
                logging.info(f"已处理 {processed_count} 行，成功拆分 {split_count} 行")
        
        # 重新排列列的顺序
        # 将Chromosome_URL放在Chromosome后面
        columns = list(df.columns)
        if 'Chromosome_URL' in columns:
            columns.remove('Chromosome_URL')
            chromosome_idx = columns.index('Chromosome')
            columns.insert(chromosome_idx + 1, 'Chromosome_URL')
            df = df[columns]
        
        # 保存结果
        logging.info(f"保存结果到: {output_file}")
        df.to_csv(output_file, index=False)
        
        # 统计信息
        logging.info("\n" + "="*50)
        logging.info("处理统计结果")
        logging.info("="*50)
        logging.info(f"总行数: {len(df):,}")
        logging.info(f"处理的行数: {processed_count:,}")
        logging.info(f"成功拆分的行数: {split_count:,}")
        logging.info(f"拆分成功率: {(split_count/processed_count*100):.2f}%" if processed_count > 0 else "0%")
        
        # 显示结果示例
        logging.info("\n结果示例:")
        sample_df = df[df['Chromosome_URL'] != ''].head(3)
        for _, row in sample_df.iterrows():
            logging.info(f"基因ID: {row['ensembl_gene_id']}")
            logging.info(f"  染色体: {row['Chromosome']}")
            logging.info(f"  URL: {row['Chromosome_URL']}")
            logging.info("")
        
        return True
        
    except Exception as e:
        logging.error(f"处理过程中出错: {e}")
        return False

def main():
    """主函数"""
    input_file = "ensembl_gene_id_with_transcript_info_2.csv"
    output_file = "ensembl_gene_id_with_full_info_split_2.csv"
    
    print("="*60)
    print("Chromosome列拆分工具")
    print("="*60)
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print("="*60)
    
    success = split_chromosome_column(input_file, output_file)
    
    if success:
        print("\n✅ 处理完成！")
        print(f"结果已保存到: {output_file}")
    else:
        print("\n❌ 处理失败！")
        print("请检查日志信息")

if __name__ == "__main__":
    main() 