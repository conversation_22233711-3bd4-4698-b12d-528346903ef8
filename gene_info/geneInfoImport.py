#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
import sys
import time
import json

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306,
    'autocommit': False,
    'connection_timeout': 600,  # 10分钟连接超时
    'use_unicode': True,
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gene_info_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def create_connection():
    """创建数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        logging.info("成功连接到MySQL数据库")
        return connection
    except Error as e:
        logging.error(f"连接数据库时出错: {e}")
        return None

def check_connection(connection):
    """检查数据库连接状态"""
    try:
        if connection and connection.is_connected():
            connection.ping(reconnect=True, attempts=3, delay=1)
            return True
        return False
    except Error as e:
        logging.error(f"数据库连接检查失败: {e}")
        return False

def create_gene_info_table(connection):
    """创建geneInfo表"""
    cursor = connection.cursor()
    
    try:
        # 删除已存在的表（如果存在）
        drop_table_query = "DROP TABLE IF EXISTS geneInfo"
        cursor.execute(drop_table_query)
        logging.info("已删除现有的geneInfo表（如果存在）")
        
        # 创建新表
        create_table_query = """
        CREATE TABLE geneInfo (
            geneId VARCHAR(255) PRIMARY KEY COMMENT '基因ID，如ENSG00000100079',
            geneSymbol VARCHAR(255) NOT NULL COMMENT '基因符号，如LGALS2',
            approvedName TEXT COMMENT '基因全名',
            locusType VARCHAR(255) COMMENT '基因位点类型',
            chromosome TEXT COMMENT '染色体位置信息',
            transcriptCount INT COMMENT '转录本数量',
            transcripts JSON COMMENT '转录本详细信息'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        
        cursor.execute(create_table_query)
        logging.info("成功创建geneInfo表")
        
        # 为GENE symbol等字段添加索引以提高检索速度（geneId作为主键已自动索引）
        indexes = [
            "CREATE INDEX idx_gene_symbol ON geneInfo (geneSymbol)",
            "CREATE INDEX idx_transcript_count ON geneInfo (transcriptCount)",
            "CREATE INDEX idx_locus_type ON geneInfo (locusType)",
            "CREATE INDEX idx_gene_id_symbol ON geneInfo (geneId, geneSymbol)"  # 复合索引
        ]
        
        for index_query in indexes:
            cursor.execute(index_query)
            logging.info(f"成功创建索引: {index_query.split('INDEX ')[1].split(' ON')[0]}")
        
        # 提交事务
        connection.commit()
        
    except Error as e:
        logging.error(f"创建表时出错: {e}")
        connection.rollback()
        raise
    finally:
        cursor.close()

def import_gene_info_data(connection, csv_file_path):
    """导入基因信息数据到数据库"""
    try:
        # 读取CSV文件
        logging.info(f"开始读取CSV文件: {csv_file_path}")
        df = pd.read_csv(csv_file_path)
        
        # 显示数据基本信息
        logging.info(f"CSV文件包含 {len(df)} 行数据")
        logging.info(f"列名: {list(df.columns)}")
        
        # 清理数据 - 处理NaN值
        df = df.fillna('')  # 将NaN值替换为空字符串
        
        # 准备插入语句
        insert_query = """
        INSERT INTO geneInfo (
            geneId, geneSymbol, approvedName, locusType, chromosome,
            transcriptCount, transcripts
        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        # 批量插入数据
        batch_size = 500  # 减小批次大小以避免超时
        total_rows = len(df)
        success_count = 0
        
        for i in range(0, total_rows, batch_size):
            # 检查连接状态
            if not check_connection(connection):
                logging.error("数据库连接检查失败，停止导入")
                break
                
            batch_df = df.iloc[i:i+batch_size]
            cursor = connection.cursor()
            
            try:
                # 准备批量数据
                batch_data = []
                for _, row in batch_df.iterrows():
                    # 处理转录本数量
                    transcript_count = row['transcript_count']
                    if pd.isna(transcript_count) or transcript_count == '':
                        transcript_count = 0
                    else:
                        try:
                            transcript_count = int(transcript_count)
                        except (ValueError, TypeError):
                            transcript_count = 0
                    
                    # 处理转录本JSON数据
                    transcripts = row['transcripts']
                    if pd.isna(transcripts) or transcripts == '':
                        transcripts_json = None
                    else:
                        try:
                            # 如果transcripts是字符串形式的JSON，需要解析后重新序列化
                            if isinstance(transcripts, str):
                                # 尝试解析JSON字符串
                                transcripts_data = json.loads(transcripts)
                                transcripts_json = json.dumps(transcripts_data, ensure_ascii=False)
                            else:
                                transcripts_json = json.dumps(transcripts, ensure_ascii=False)
                        except (json.JSONDecodeError, TypeError):
                            logging.warning(f"无法解析转录本JSON数据: {transcripts}")
                            transcripts_json = None
                    
                    batch_data.append((
                        str(row['ensembl_gene_id']),
                        str(row['GENE symbol']),
                        str(row['Approved Name']),
                        str(row['Locus Type']),
                        str(row['Chromosome']),
                        transcript_count,
                        transcripts_json
                    ))
                
                # 执行批量插入
                cursor.executemany(insert_query, batch_data)
                connection.commit()
                
                success_count += len(batch_data)
                logging.info(f"已导入 {min(i + batch_size, total_rows)}/{total_rows} 行数据 (成功: {success_count})")
                
                # 每10个批次休息一下，避免连接超时
                if (i // batch_size + 1) % 10 == 0:
                    time.sleep(0.1)
                    
            except Error as e:
                logging.error(f"导入批次数据时出错: {e}")
                connection.rollback()
                break
            finally:
                cursor.close()
        
        logging.info(f"数据导入完成！总共成功导入 {success_count} 行数据")
        
    except Exception as e:
        logging.error(f"导入数据时出错: {e}")
        raise

def show_statistics(connection):
    """显示导入统计信息"""
    if not check_connection(connection):
        logging.error("无法连接数据库，跳过统计信息显示")
        return
        
    cursor = connection.cursor()
    
    try:
        # 总记录数
        cursor.execute("SELECT COUNT(*) FROM geneInfo")
        total_count = cursor.fetchone()[0]
        logging.info(f"表中总记录数: {total_count}")
        
        # 唯一基因数（按GENE ID）
        cursor.execute("SELECT COUNT(DISTINCT geneId) FROM geneInfo")
        unique_genes = cursor.fetchone()[0]
        logging.info(f"唯一基因ID数: {unique_genes}")
        
        # 唯一基因符号数
        cursor.execute("SELECT COUNT(DISTINCT geneSymbol) FROM geneInfo")
        unique_symbols = cursor.fetchone()[0]
        logging.info(f"唯一基因符号数: {unique_symbols}")
        
        # 按基因类型分组统计
        cursor.execute("""
            SELECT locusType, COUNT(*) as count 
            FROM geneInfo 
            WHERE locusType != '' 
            GROUP BY locusType 
            ORDER BY count DESC 
            LIMIT 10
        """)
        
        locus_stats = cursor.fetchall()
        logging.info("前10种基因位点类型统计:")
        for locus_type, count in locus_stats:
            logging.info(f"  {locus_type}: {count}")
        
        # 转录本数量统计
        cursor.execute("""
            SELECT 
                AVG(transcriptCount) as avg_transcripts,
                MIN(transcriptCount) as min_transcripts,
                MAX(transcriptCount) as max_transcripts
            FROM geneInfo 
            WHERE transcriptCount IS NOT NULL
        """)
        
        transcript_stats = cursor.fetchone()
        if transcript_stats:
            avg_count, min_count, max_count = transcript_stats
            logging.info(f"转录本统计:")
            logging.info(f"  平均转录本数: {avg_count:.2f}")
            logging.info(f"  最少转录本数: {min_count}")
            logging.info(f"  最多转录本数: {max_count}")
            
    except Error as e:
        logging.error(f"显示统计信息时出错: {e}")
    finally:
        cursor.close()

def main():
    """主函数"""
    csv_file_path = "gene_Info.csv"
    
    # 检查文件是否存在
    import os
    if not os.path.exists(csv_file_path):
        logging.error(f"CSV文件不存在: {csv_file_path}")
        return
    
    # 创建数据库连接
    connection = create_connection()
    if connection is None:
        logging.error("无法连接到数据库，程序退出")
        return
    
    try:
        # 创建表
        create_gene_info_table(connection)
        
        # 导入数据
        import_gene_info_data(connection, csv_file_path)
        
        # 显示统计信息
        show_statistics(connection)
        
        logging.info("基因信息导入任务完成！")
        
    except Exception as e:
        logging.error(f"程序执行出错: {e}")
    finally:
        if connection and connection.is_connected():
            connection.close()
            logging.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 