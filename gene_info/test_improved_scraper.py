#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ensembl_gene_info_scraper import EnsemblGeneInfoScraper
import logging

# 设置简单日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

def test_improved_scraper():
    """测试改进后的基因信息爬取器"""
    
    # 测试基因ID列表
    test_genes = [
        'ENSG00000003056',  # M6PR
        'ENSG00000004478',  # ARF5  
        'ENSG00000173153',  # ESRRA
        'ENSG00000004487',  # FKBP4
        'ENSG00000003137'   # CYP26B1
    ]
    
    print("="*60)
    print("测试改进后的基因信息爬取器")
    print("="*60)
    
    # 创建爬取器实例 - 使用较保守的设置
    scraper = EnsemblGeneInfoScraper(
        max_workers=2,    # 减少线程数避免被限制
        timeout=15,       # 增加超时时间
        delay=1.0         # 增加延迟
    )
    
    # 测试单个基因爬取
    print(f"\n测试 {len(test_genes)} 个基因...")
    
    results = {}
    for i, gene_id in enumerate(test_genes, 1):
        print(f"\n[{i}/{len(test_genes)}] 测试基因: {gene_id}")
        
        try:
            gene_id_result, gene_info = scraper.scrape_single_gene(gene_id, len(test_genes))
            results[gene_id] = gene_info
            
            # 显示结果
            print(f"  基因符号: '{gene_info['GENE symbol']}'")
            print(f"  批准名称: '{gene_info['Approved Name'][:50]}{'...' if len(gene_info['Approved Name']) > 50 else ''}'")
            print(f"  基因类型: '{gene_info['Locus Type']}'")
            print(f"  染色体: '{gene_info['Chromosome']}'")
            
            # 检查信息完整性
            filled_fields = sum(1 for value in gene_info.values() if value.strip())
            print(f"  信息完整度: {filled_fields}/4 个字段")
            
        except Exception as e:
            print(f"  错误: {e}")
            results[gene_id] = {
                'GENE symbol': '',
                'Approved Name': '',
                'Locus Type': '',
                'Chromosome': ''
            }
    
    # 统计结果
    print("\n" + "="*60)
    print("测试结果统计")
    print("="*60)
    
    total_genes = len(test_genes)
    successful_genes = 0
    field_stats = {'GENE symbol': 0, 'Approved Name': 0, 'Locus Type': 0, 'Chromosome': 0}
    
    for gene_id, info in results.items():
        has_any_info = any(info.values())
        if has_any_info:
            successful_genes += 1
        
        # 统计每个字段的成功率
        for field, value in info.items():
            if value.strip():
                field_stats[field] += 1
    
    print(f"总基因数: {total_genes}")
    print(f"至少获取到部分信息的基因数: {successful_genes}")
    print(f"总体成功率: {(successful_genes/total_genes*100):.1f}%")
    print("\n各字段成功率:")
    for field, count in field_stats.items():
        success_rate = (count / total_genes * 100)
        print(f"  {field}: {count}/{total_genes} ({success_rate:.1f}%)")
    
    # 显示成功的示例
    print("\n成功获取信息的基因示例:")
    success_count = 0
    for gene_id, info in results.items():
        if any(info.values()) and success_count < 3:
            success_count += 1
            print(f"\n  {gene_id}:")
            for field, value in info.items():
                if value:
                    print(f"    {field}: {value}")
    
    return results

if __name__ == "__main__":
    results = test_improved_scraper() 