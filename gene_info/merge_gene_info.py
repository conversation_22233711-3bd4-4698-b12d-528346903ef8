import json
import os

def merge_gene_info_files(original_file, recovered_file, output_file):
    """
    Merge original gene info file with recovered gene info.
    Replace entries in original file that have "Status": "No information found"
    with corresponding entries from the recovered file if available.
    
    Args:
        original_file (str): Path to the original gene info JSON file
        recovered_file (str): Path to the recovered gene info JSON file
        output_file (str): Path to save the merged JSON file
    """
    # Load the original data
    with open(original_file, 'r') as f:
        original_data = json.load(f)
    
    # Load the recovered data
    with open(recovered_file, 'r') as f:
        recovered_data = json.load(f)
    
    # Create a dictionary of recovered gene info for quick lookup
    recovered_dict = {item['gene_id']: item for item in recovered_data}
    
    # Count for reporting
    replaced_count = 0
    
    # Process each entry in the original data
    for i, gene_info in enumerate(original_data):
        # Check if this entry has "No information found" status
        if gene_info.get('Status') == 'No information found':
            gene_id = gene_info['gene_id']
            
            # Look for this gene_id in the recovered data
            if gene_id in recovered_dict:
                # Replace the entry with the recovered data
                original_data[i] = recovered_dict[gene_id]
                replaced_count += 1
                print(f"Replaced info for gene ID: {gene_id}")
    
    # Save the merged data
    with open(output_file, 'w') as f:
        json.dump(original_data, f, indent=2)
    
    print(f"Merge complete. Replaced {replaced_count} entries.")
    print(f"Merged file saved to: {output_file}")

if __name__ == "__main__":
    # File paths
    original_file = "gene_info_results.json"
    recovered_file = "gene_info_results_recovered.json"
    output_file = "gene_info_results_merged.json"
    
    # Create directory if needed (only if there's a directory component)
    dir_name = os.path.dirname(output_file)
    if dir_name:  # Only try to create directory if path has a directory component
        os.makedirs(dir_name, exist_ok=True)
    
    # Execute the merge
    merge_gene_info_files(original_file, recovered_file, output_file) 