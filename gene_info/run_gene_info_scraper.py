#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import logging
from ensembl_gene_info_scraper import EnsemblGeneInfoScraper

def get_preset_config(preset_name):
    """获取预设配置"""
    presets = {
        'test': {
            'max_workers': 2,
            'batch_size': 100,
            'timeout': 10,
            'max_retries': 2,
            'delay': 1.0,
            'description': '测试配置 - 少量线程，适合测试'
        },
        'default': {
            'max_workers': 4,
            'batch_size': 500, 
            'timeout': 12,
            'max_retries': 3,
            'delay': 0.8,
            'description': '默认配置 - 平衡速度和稳定性'
        },
        'aggressive': {
            'max_workers': 6,
            'batch_size': 1000,
            'timeout': 15,
            'max_retries': 4,
            'delay': 0.5,
            'description': '激进配置 - 高并发，适合网络良好环境'
        },
        'conservative': {
            'max_workers': 2,
            'batch_size': 200,
            'timeout': 20,
            'max_retries': 5,
            'delay': 1.5,
            'description': '保守配置 - 低并发，适合网络不稳定环境'
        },
        'fast': {
            'max_workers': 8,
            'batch_size': 1500,
            'timeout': 8,
            'max_retries': 2,
            'delay': 0.3,
            'description': '快速配置 - 最高并发，适合高速网络环境'
        }
    }
    
    return presets.get(preset_name, presets['default'])

def main():
    parser = argparse.ArgumentParser(
        description='运行Ensembl基因信息爬取器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
预设配置说明:
  test        - 测试配置 (2线程, 100批次, 10s超时, 2次重试, 1.0s延迟)
  default     - 默认配置 (4线程, 500批次, 12s超时, 3次重试, 0.8s延迟) 
  aggressive  - 激进配置 (6线程, 1000批次, 15s超时, 4次重试, 0.5s延迟)
  conservative- 保守配置 (2线程, 200批次, 20s超时, 5次重试, 1.5s延迟)
  fast        - 快速配置 (8线程, 1500批次, 8s超时, 2次重试, 0.3s延迟)

使用示例:
  python run_gene_info_scraper.py --preset test
  python run_gene_info_scraper.py --preset default  
  python run_gene_info_scraper.py --input custom.csv --output custom_results.csv
  python run_gene_info_scraper.py --max-workers 6 --timeout 15
        """
    )
    
    # 预设配置
    parser.add_argument('--preset', choices=['test', 'default', 'aggressive', 'conservative', 'fast'],
                       default='default', help='使用预设配置')
    
    # 输入输出文件
    parser.add_argument('--input', default='../ensembl_gene_id_unique.csv',
                       help='输入CSV文件路径 (默认: ../ensembl_gene_id_unique.csv)')
    parser.add_argument('--output', default='../gene_info_results.csv', 
                       help='输出CSV文件路径 (默认: ../gene_info_results.csv)')
    
    # 自定义参数
    parser.add_argument('--max-workers', type=int, help='最大线程数')
    parser.add_argument('--batch-size', type=int, help='批处理大小') 
    parser.add_argument('--timeout', type=int, help='请求超时时间(秒)')
    parser.add_argument('--max-retries', type=int, help='最大重试次数')
    parser.add_argument('--delay', type=float, help='请求间延迟(秒)')
    
    # 其他选项
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    parser.add_argument('--start-from', type=int, default=0,
                       help='从第几行开始处理 (用于断点续传)')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('gene_info_scraper.log'),
            logging.StreamHandler()
        ]
    )
    
    # 获取预设配置
    config = get_preset_config(args.preset)
    
    # 用命令行参数覆盖预设配置
    if args.max_workers is not None:
        config['max_workers'] = args.max_workers
    if args.batch_size is not None:
        config['batch_size'] = args.batch_size  
    if args.timeout is not None:
        config['timeout'] = args.timeout
    if args.max_retries is not None:
        config['max_retries'] = args.max_retries
    if args.delay is not None:
        config['delay'] = args.delay
    
    print("="*60)
    print("Ensembl基因信息爬取器")
    print("="*60)
    print(f"使用预设: {args.preset} - {config['description']}")
    print(f"输入文件: {args.input}")
    print(f"输出文件: {args.output}")
    print(f"配置参数:")
    print(f"  线程数: {config['max_workers']}")
    print(f"  批处理大小: {config['batch_size']}")
    print(f"  超时时间: {config['timeout']}秒")
    print(f"  最大重试: {config['max_retries']}次")
    print(f"  请求延迟: {config['delay']}秒")
    if args.start_from > 0:
        print(f"  断点续传: 从第{args.start_from}行开始")
    print("="*60)
    
    try:
        # 创建爬取器实例
        scraper = EnsemblGeneInfoScraper(
            max_workers=config['max_workers'],
            batch_size=config['batch_size'],
            timeout=config['timeout'],
            max_retries=config['max_retries'],
            delay=config['delay']
        )
        
        # 运行爬取任务
        scraper.scrape_genes_from_file(
            input_file=args.input,
            output_file=args.output,
            start_from=args.start_from
        )
        
        print("\n基因信息爬取完成！")
        
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        logging.info("用户中断操作")
        
    except Exception as e:
        print(f"\n爬取过程中出错: {e}")
        logging.error(f"爬取过程中出错: {e}")

if __name__ == "__main__":
    main() 