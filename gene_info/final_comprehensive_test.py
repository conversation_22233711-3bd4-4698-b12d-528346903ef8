#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ensembl_gene_info_scraper import EnsemblGeneInfoScraper
import logging
import pandas as pd
import time

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

def final_comprehensive_test():
    """最终综合测试 - 测试更多基因样本"""
    
    # 扩展的测试基因列表 - 包含不同类型的基因
    test_genes = [
        'ENSG00000003056',  # M6PR - Protein coding
        'ENSG00000004478',  # FKBP4 - Protein coding  
        'ENSG00000173153',  # ESRRA - Protein coding
        'ENSG00000004487',  # KDM1A - Protein coding
        'ENSG00000003137',  # CYP26B1 - Protein coding
        'ENSG00000005339',  # CREBBP - Protein coding
        'ENSG00000012048',  # BRCA1 - Protein coding (癌症相关)
        'ENSG00000139618',  # BRCA2 - Protein coding (癌症相关)
        'ENSG00000141510',  # TP53 - Protein coding (癌症相关)
        'ENSG00000171862',  # PTEN - Protein coding (癌症相关)
    ]
    
    print("="*70)
    print("最终综合测试 - Ensembl基因信息爬取器")
    print("="*70)
    print(f"测试基因数量: {len(test_genes)}")
    print("包含类型: 蛋白编码基因、癌症相关基因等")
    print("="*70)
    
    # 创建爬取器实例 - 使用平衡的配置
    scraper = EnsemblGeneInfoScraper(
        max_workers=3,    # 中等线程数
        timeout=12,       # 中等超时时间
        delay=0.8         # 中等延迟
    )
    
    # 开始测试
    start_time = time.time()
    results = {}
    success_count = 0
    field_success = {'GENE symbol': 0, 'Approved Name': 0, 'Locus Type': 0, 'Chromosome': 0}
    
    print(f"\n开始测试 {len(test_genes)} 个基因...")
    print("-" * 70)
    
    for i, gene_id in enumerate(test_genes, 1):
        print(f"[{i:2d}/{len(test_genes)}] 处理: {gene_id}")
        
        try:
            gene_id_result, gene_info = scraper.scrape_single_gene(gene_id, len(test_genes))
            results[gene_id] = gene_info
            
            # 检查成功情况
            filled_fields = sum(1 for value in gene_info.values() if value.strip())
            if filled_fields > 0:
                success_count += 1
            
            # 统计各字段成功率
            for field, value in gene_info.items():
                if value.strip():
                    field_success[field] += 1
            
            # 显示结果摘要
            print(f"       符号: {gene_info['GENE symbol'][:15]:15} | "
                  f"名称: {gene_info['Approved Name'][:25]:25} | "
                  f"类型: {gene_info['Locus Type'][:12]:12} | "
                  f"染色体: {gene_info['Chromosome']:3}")
            
        except Exception as e:
            print(f"       错误: {e}")
            results[gene_id] = {
                'GENE symbol': '',
                'Approved Name': '',
                'Locus Type': '',
                'Chromosome': ''
            }
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # 详细统计结果
    print("\n" + "="*70)
    print("测试结果统计")
    print("="*70)
    
    total_genes = len(test_genes)
    overall_success = (success_count / total_genes * 100)
    
    print(f"总基因数:       {total_genes}")
    print(f"成功基因数:     {success_count}")
    print(f"总体成功率:     {overall_success:.1f}%")
    print(f"处理时间:       {elapsed_time:.1f}秒")
    print(f"平均每基因:     {elapsed_time/total_genes:.1f}秒")
    
    print(f"\n各字段成功率:")
    for field, count in field_success.items():
        success_rate = (count / total_genes * 100)
        print(f"  {field:15}: {count:2d}/{total_genes} ({success_rate:5.1f}%)")
    
    # 显示成功示例
    print(f"\n成功获取信息的基因详情:")
    print("-" * 70)
    successful_examples = [(gene_id, info) for gene_id, info in results.items() 
                          if any(info.values())][:5]  # 只显示前5个
    
    for gene_id, info in successful_examples:
        print(f"\n{gene_id}:")
        for field, value in info.items():
            if value:
                print(f"  {field:15}: {value}")
    
    # 检查失败情况
    failed_genes = [gene_id for gene_id, info in results.items() 
                   if not any(info.values())]
    
    if failed_genes:
        print(f"\n未获取到信息的基因:")
        for gene_id in failed_genes:
            print(f"  {gene_id}")
    
    # 性能评估
    print(f"\n性能评估:")
    print(f"  线程数: {scraper.max_workers}")
    print(f"  超时设置: {scraper.timeout}秒")
    print(f"  请求延迟: {scraper.delay}秒")
    print(f"  处理速度: {(total_genes/elapsed_time*3600):.0f} 基因/小时")
    
    # 数据质量评估
    print(f"\n数据质量评估:")
    complete_genes = sum(1 for info in results.values() 
                        if all(info.values()))
    partial_genes = sum(1 for info in results.values() 
                       if any(info.values()) and not all(info.values()))
    
    print(f"  完整信息基因: {complete_genes} ({complete_genes/total_genes*100:.1f}%)")
    print(f"  部分信息基因: {partial_genes} ({partial_genes/total_genes*100:.1f}%)")
    print(f"  无信息基因:   {len(failed_genes)} ({len(failed_genes)/total_genes*100:.1f}%)")
    
    # 保存测试结果到文件
    df_results = pd.DataFrame.from_dict(results, orient='index')
    df_results.index.name = 'gene_id'
    output_file = 'comprehensive_test_results.csv'
    df_results.to_csv(output_file)
    print(f"\n测试结果已保存到: {output_file}")
    
    return results, overall_success >= 80  # 80%以上认为测试通过

if __name__ == "__main__":
    results, test_passed = final_comprehensive_test()
    
    if test_passed:
        print("\n" + "="*70)
        print("✅ 综合测试通过！爬取器运行正常，可以用于生产环境。")
        print("="*70)
    else:
        print("\n" + "="*70)
        print("❌ 综合测试未通过，请检查网络连接或调整配置参数。")
        print("="*70) 