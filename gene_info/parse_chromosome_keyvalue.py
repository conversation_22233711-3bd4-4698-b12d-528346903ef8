#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
染色体key-value格式解析工具

用于解析爬取结果中的染色体字段，提取名称和链接信息。
"""

import pandas as pd
import sys

def parse_chromosome_info(chromosome_str):
    """
    解析染色体key-value格式字符串
    
    Args:
        chromosome_str: 格式如 "Chromosome 11: 64,305,497-64,316,743:http://www.ensembl.org/..."
        
    Returns:
        dict: {'name': '名称部分', 'url': '链接部分'}
    """
    if not chromosome_str or ':http' not in chromosome_str:
        return {'name': chromosome_str, 'url': ''}
    
    # 找到最后一个 :http 的位置
    split_pos = chromosome_str.rfind(':http')
    name_part = chromosome_str[:split_pos]
    url_part = chromosome_str[split_pos+1:]  # 去掉冒号
    
    return {'name': name_part, 'url': url_part}

def process_csv_file(input_file, output_file=None):
    """
    处理CSV文件，拆分染色体字段为名称和链接两列
    
    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径，如果为None则在原文件名基础上添加_parsed
    """
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file)
        print(f"读取文件: {input_file}")
        print(f"总行数: {len(df)}")
        
        if 'Chromosome' not in df.columns:
            print("错误: 文件中没有找到 'Chromosome' 列")
            return False
        
        # 创建新列
        df['Chromosome_Name'] = ''
        df['Chromosome_URL'] = ''
        
        # 解析每一行的染色体信息
        parsed_count = 0
        for idx, row in df.iterrows():
            chromosome_str = str(row['Chromosome'])
            parsed = parse_chromosome_info(chromosome_str)
            
            df.at[idx, 'Chromosome_Name'] = parsed['name']
            df.at[idx, 'Chromosome_URL'] = parsed['url']
            
            if parsed['url']:  # 有链接的才算解析成功
                parsed_count += 1
        
        print(f"成功解析染色体链接: {parsed_count}/{len(df)} ({parsed_count/len(df)*100:.1f}%)")
        
        # 确定输出文件名
        if output_file is None:
            if input_file.endswith('.csv'):
                output_file = input_file[:-4] + '_parsed.csv'
            else:
                output_file = input_file + '_parsed.csv'
        
        # 保存结果
        df.to_csv(output_file, index=False)
        print(f"结果已保存到: {output_file}")
        
        # 显示示例
        print("\n解析结果示例 (前3行):")
        for i in range(min(3, len(df))):
            row = df.iloc[i]
            print(f"\n第{i+1}行:")
            print(f"  基因ID: {row['ensembl_gene_id']}")
            print(f"  原始染色体字段: {row['Chromosome'][:80]}...")
            print(f"  解析后名称: {row['Chromosome_Name']}")
            print(f"  解析后链接: {row['Chromosome_URL'][:80]}...")
        
        return True
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python parse_chromosome_keyvalue.py <输入CSV文件> [输出CSV文件]")
        print("\n示例:")
        print("  python parse_chromosome_keyvalue.py gene_results.csv")
        print("  python parse_chromosome_keyvalue.py gene_results.csv gene_results_parsed.csv")
        print("\n功能:")
        print("  将染色体字段 'Chromosome 11: 64,305,497-64,316,743:http://...' 拆分为:")
        print("  - Chromosome_Name: 'Chromosome 11: 64,305,497-64,316,743'")
        print("  - Chromosome_URL: 'http://www.ensembl.org/...'")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    print("="*60)
    print("染色体key-value格式解析工具")
    print("="*60)
    
    success = process_csv_file(input_file, output_file)
    
    if success:
        print("\n✅ 解析完成!")
    else:
        print("\n❌ 解析失败!")

if __name__ == "__main__":
    main() 