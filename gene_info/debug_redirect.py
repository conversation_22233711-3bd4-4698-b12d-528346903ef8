#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import re

def follow_gene_redirect(gene_id):
    """跟随基因ID重定向到真正的Summary页面"""
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # 第一步：访问基因ID页面
    initial_url = f"http://www.ensembl.org/id/{gene_id}"
    print(f"步骤1: 访问 {initial_url}")
    
    try:
        response1 = session.get(initial_url, timeout=10)
        response1.raise_for_status()
        
        soup1 = BeautifulSoup(response1.text, 'html.parser')
        
        # 查找重定向链接
        summary_link = None
        for link in soup1.find_all('a', href=True):
            href = link.get('href')
            if '/Gene/Summary?' in href and gene_id in href:
                summary_link = href
                break
        
        if summary_link:
            if summary_link.startswith('/'):
                summary_url = f"http://www.ensembl.org{summary_link}"
            else:
                summary_url = summary_link
                
            print(f"步骤2: 跟随重定向到 {summary_url}")
            
            response2 = session.get(summary_url, timeout=10)
            response2.raise_for_status()
            
            print("步骤3: 分析Summary页面")
            analyze_summary_page(response2.text, gene_id)
            
        else:
            print("未找到Summary页面链接，分析当前页面")
            analyze_current_page(response1.text, gene_id)
            
    except Exception as e:
        print(f"错误: {e}")

def analyze_summary_page(html_content, gene_id):
    """分析Summary页面内容"""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    print(f"\n分析基因 {gene_id} 的Summary页面:")
    print("="*50)
    
    # 提取标题信息
    title = soup.find('title')
    if title:
        print(f"页面标题: {title.get_text()}")
    
    # 查找所有可能的信息面板
    panels = soup.find_all('div', class_='js_panel')
    print(f"\n找到 {len(panels)} 个面板")
    
    # 查找GeneSummary面板
    gene_summary = soup.find('div', {'id': 'GeneSummary'})
    if gene_summary:
        print("\n✓ 找到GeneSummary面板")
        rows = gene_summary.find_all('div', class_='row')
        for row in rows:
            lhs = row.find('div', class_='lhs')
            rhs = row.find('div', class_='rhs')
            if lhs and rhs:
                label = lhs.get_text(strip=True)
                content = rhs.get_text(strip=True)[:100]
                print(f"  {label}: {content}...")
    else:
        print("✗ 未找到GeneSummary面板")
    
    # 查找ensembl_panel_2面板
    panel2 = soup.find('div', {'id': 'ensembl_panel_2'})
    if panel2:
        print("\n✓ 找到ensembl_panel_2面板")
        summary_panel = panel2.find('div', class_='summary_panel')
        if summary_panel:
            print("  ✓ 找到summary_panel")
            rows = summary_panel.find_all('div', class_='row')
            for row in rows:
                lhs = row.find('div', class_='lhs')
                rhs = row.find('div', class_='rhs')
                if lhs and rhs:
                    label = lhs.get_text(strip=True)
                    content = rhs.get_text(strip=True)[:100]
                    print(f"    {label}: {content}...")
        else:
            print("  ✗ 未找到summary_panel")
    else:
        print("✗ 未找到ensembl_panel_2面板")
    
    # 保存部分HTML
    with open(f"debug_summary_{gene_id}.html", 'w', encoding='utf-8') as f:
        f.write(html_content[:30000])
    print(f"\n已保存HTML到 debug_summary_{gene_id}.html")

def analyze_current_page(html_content, gene_id):
    """分析当前页面（可能是重定向页面）"""
    soup = BeautifulSoup(html_content, 'html.parser')
    
    print(f"\n分析当前页面:")
    print("="*30)
    
    title = soup.find('title')
    if title:
        print(f"页面标题: {title.get_text()}")
    
    # 查找基因符号
    gene_symbol_match = re.search(r'Gene:\s*([A-Z0-9\-_]{1,20})\s*\(', title.get_text() if title else '')
    if gene_symbol_match:
        print(f"基因符号: {gene_symbol_match.group(1)}")
    
    # 查找所有链接
    links = soup.find_all('a', href=True)
    summary_links = [link.get('href') for link in links if '/Gene/Summary?' in link.get('href')]
    if summary_links:
        print(f"找到 {len(summary_links)} 个Summary链接:")
        for link in summary_links[:3]:
            print(f"  {link}")

if __name__ == "__main__":
    follow_gene_redirect("ENSG00000003056")  # M6PR 