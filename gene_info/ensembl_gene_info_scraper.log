2025-06-18 14:12:03,108 - INFO - [MainThread] - 开始测试Ensembl基因信息爬取器...
2025-06-18 14:12:03,111 - INFO - [MainThread] - 创建测试文件: test_gene_ids.csv
2025-06-18 14:12:03,111 - INFO - [MainThread] - 读取输入文件: test_gene_ids.csv
2025-06-18 14:12:03,114 - INFO - [MainThread] - 需要处理 5 个基因
2025-06-18 14:12:03,114 - INFO - [MainThread] - 开始使用 2 个线程处理 5 个基因
2025-06-18 14:12:03,114 - INFO - [MainThread] - 请求延迟: 1.0秒, 超时时间: 15秒
2025-06-18 14:12:12,506 - INFO - [MainThread] - 更新数据框...
2025-06-18 14:12:12,512 - INFO - [MainThread] - 保存最终结果到: test_gene_info_results.csv
2025-06-18 14:12:12,513 - INFO - [MainThread] - 
==================================================
2025-06-18 14:12:12,513 - INFO - [MainThread] - 最终统计结果
2025-06-18 14:12:12,513 - INFO - [MainThread] - ==================================================
2025-06-18 14:12:12,513 - INFO - [MainThread] - 总基因数: 5
2025-06-18 14:12:12,513 - INFO - [MainThread] - 成功获取信息的基因数: 5
2025-06-18 14:12:12,513 - INFO - [MainThread] - 失败的基因数: 0
2025-06-18 14:12:12,513 - INFO - [MainThread] - 成功率: 100.00%
2025-06-18 14:12:12,513 - INFO - [MainThread] - 
结果示例:
2025-06-18 14:12:12,514 - INFO - [MainThread] -   ENSG00000003056 (M6PRENSG00000003056): ...
2025-06-18 14:12:12,514 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:12:12,514 - INFO - [MainThread] -   ENSG00000004059 (ARF5ENSG00000004059): ...
2025-06-18 14:12:12,514 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:12:12,514 - INFO - [MainThread] -   ENSG00000173153 (ESRRAENSG00000173153): ...
2025-06-18 14:12:12,514 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:12:12,514 - INFO - [MainThread] -   ENSG00000004478 (FKBP4ENSG00000004478): ...
2025-06-18 14:12:12,514 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:12:12,514 - INFO - [MainThread] -   ENSG00000003137 (CYP26B1ENSG00000003137): ...
2025-06-18 14:12:12,514 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:12:12,515 - INFO - [MainThread] - 
测试结果:
2025-06-18 14:12:12,515 - INFO - [MainThread] - 处理的基因数: 5
2025-06-18 14:12:12,516 - INFO - [MainThread] - ✓ ENSG00000003056:
2025-06-18 14:12:12,516 - INFO - [MainThread] -     基因符号: M6PRENSG00000003056
2025-06-18 14:12:12,516 - ERROR - [MainThread] - 测试过程中出错: 'float' object is not subscriptable
2025-06-18 14:12:12,516 - INFO - [MainThread] - 清理文件: test_gene_ids.csv
2025-06-18 14:12:12,516 - INFO - [MainThread] - 清理文件: test_gene_info_results.csv
2025-06-18 14:13:49,821 - INFO - [MainThread] - 开始测试Ensembl基因信息爬取器...
2025-06-18 14:13:49,825 - INFO - [MainThread] - 创建测试文件: test_gene_ids.csv
2025-06-18 14:13:49,825 - INFO - [MainThread] - 读取输入文件: test_gene_ids.csv
2025-06-18 14:13:49,828 - INFO - [MainThread] - 需要处理 5 个基因
2025-06-18 14:13:49,828 - INFO - [MainThread] - 开始使用 2 个线程处理 5 个基因
2025-06-18 14:13:49,828 - INFO - [MainThread] - 请求延迟: 1.0秒, 超时时间: 15秒
2025-06-18 14:13:59,394 - INFO - [MainThread] - 更新数据框...
2025-06-18 14:13:59,404 - INFO - [MainThread] - 保存最终结果到: test_gene_info_results.csv
2025-06-18 14:13:59,406 - INFO - [MainThread] - 
==================================================
2025-06-18 14:13:59,406 - INFO - [MainThread] - 最终统计结果
2025-06-18 14:13:59,406 - INFO - [MainThread] - ==================================================
2025-06-18 14:13:59,406 - INFO - [MainThread] - 总基因数: 5
2025-06-18 14:13:59,406 - INFO - [MainThread] - 成功获取信息的基因数: 5
2025-06-18 14:13:59,406 - INFO - [MainThread] - 失败的基因数: 0
2025-06-18 14:13:59,406 - INFO - [MainThread] - 成功率: 100.00%
2025-06-18 14:13:59,406 - INFO - [MainThread] - 
结果示例:
2025-06-18 14:13:59,406 - INFO - [MainThread] -   ENSG00000003056 (M6PRENSG00000003056): ...
2025-06-18 14:13:59,406 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:13:59,406 - INFO - [MainThread] -   ENSG00000004059 (ARF5ENSG00000004059): ...
2025-06-18 14:13:59,406 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:13:59,406 - INFO - [MainThread] -   ENSG00000173153 (ESRRAENSG00000173153): ...
2025-06-18 14:13:59,406 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:13:59,406 - INFO - [MainThread] -   ENSG00000004478 (FKBP4ENSG00000004478): ...
2025-06-18 14:13:59,406 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:13:59,407 - INFO - [MainThread] -   ENSG00000003137 (CYP26B1ENSG00000003137): ...
2025-06-18 14:13:59,407 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:13:59,408 - INFO - [MainThread] - 
测试结果:
2025-06-18 14:13:59,408 - INFO - [MainThread] - 处理的基因数: 5
2025-06-18 14:13:59,408 - INFO - [MainThread] - ✓ ENSG00000003056:
2025-06-18 14:13:59,408 - INFO - [MainThread] -     基因符号: M6PRENSG00000003056
2025-06-18 14:13:59,408 - INFO - [MainThread] - ✓ ENSG00000004059:
2025-06-18 14:13:59,408 - INFO - [MainThread] -     基因符号: ARF5ENSG00000004059
2025-06-18 14:13:59,408 - INFO - [MainThread] - ✓ ENSG00000173153:
2025-06-18 14:13:59,408 - INFO - [MainThread] -     基因符号: ESRRAENSG00000173153
2025-06-18 14:13:59,408 - INFO - [MainThread] - ✓ ENSG00000004478:
2025-06-18 14:13:59,408 - INFO - [MainThread] -     基因符号: FKBP4ENSG00000004478
2025-06-18 14:13:59,408 - INFO - [MainThread] - ✓ ENSG00000003137:
2025-06-18 14:13:59,408 - INFO - [MainThread] -     基因符号: CYP26B1ENSG00000003137
2025-06-18 14:13:59,409 - INFO - [MainThread] - 
测试统计: 5/5 成功 (100.0%)
2025-06-18 14:13:59,409 - INFO - [MainThread] - ✓ 测试通过! 静态爬虫工作正常
2025-06-18 14:13:59,409 - INFO - [MainThread] - 清理文件: test_gene_ids.csv
2025-06-18 14:13:59,410 - INFO - [MainThread] - 清理文件: test_gene_info_results.csv
2025-06-18 14:14:35,929 - INFO - [MainThread] - 开始测试Ensembl基因信息爬取器...
2025-06-18 14:14:35,933 - INFO - [MainThread] - 创建测试文件: test_gene_ids.csv
2025-06-18 14:14:35,933 - INFO - [MainThread] - 读取输入文件: test_gene_ids.csv
2025-06-18 14:14:35,936 - INFO - [MainThread] - 需要处理 5 个基因
2025-06-18 14:14:35,936 - INFO - [MainThread] - 开始使用 2 个线程处理 5 个基因
2025-06-18 14:14:35,936 - INFO - [MainThread] - 请求延迟: 1.0秒, 超时时间: 15秒
2025-06-18 14:15:12,688 - WARNING - [ThreadPoolExecutor-0_1] - 请求基因 ENSG00000004059 失败: HTTPConnectionPool(host='127.0.0.1', port=1082): Read timed out.
2025-06-18 14:15:12,692 - INFO - [MainThread] - 更新数据框...
2025-06-18 14:15:12,723 - INFO - [MainThread] - 保存最终结果到: test_gene_info_results.csv
2025-06-18 14:15:12,727 - INFO - [MainThread] - 
==================================================
2025-06-18 14:15:12,727 - INFO - [MainThread] - 最终统计结果
2025-06-18 14:15:12,727 - INFO - [MainThread] - ==================================================
2025-06-18 14:15:12,727 - INFO - [MainThread] - 总基因数: 5
2025-06-18 14:15:12,727 - INFO - [MainThread] - 成功获取信息的基因数: 4
2025-06-18 14:15:12,727 - INFO - [MainThread] - 失败的基因数: 1
2025-06-18 14:15:12,727 - INFO - [MainThread] - 成功率: 80.00%
2025-06-18 14:15:12,727 - INFO - [MainThread] - 
结果示例:
2025-06-18 14:15:12,728 - INFO - [MainThread] -   ENSG00000003056 (VEP): ...
2025-06-18 14:15:12,728 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:15:12,728 - INFO - [MainThread] -   ENSG00000173153 (VEP): ...
2025-06-18 14:15:12,728 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:15:12,728 - INFO - [MainThread] -   ENSG00000004478 (VEP): ...
2025-06-18 14:15:12,728 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:15:12,728 - INFO - [MainThread] -   ENSG00000003137 (VEP): ...
2025-06-18 14:15:12,728 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:15:12,731 - INFO - [MainThread] - 
测试结果:
2025-06-18 14:15:12,731 - INFO - [MainThread] - 处理的基因数: 5
2025-06-18 14:15:12,732 - INFO - [MainThread] - ✓ ENSG00000003056:
2025-06-18 14:15:12,732 - INFO - [MainThread] -     基因符号: VEP
2025-06-18 14:15:12,732 - WARNING - [MainThread] - ✗ ENSG00000004059: 未获取到信息
2025-06-18 14:15:12,732 - INFO - [MainThread] - ✓ ENSG00000173153:
2025-06-18 14:15:12,732 - INFO - [MainThread] -     基因符号: VEP
2025-06-18 14:15:12,732 - INFO - [MainThread] - ✓ ENSG00000004478:
2025-06-18 14:15:12,732 - INFO - [MainThread] -     基因符号: VEP
2025-06-18 14:15:12,732 - INFO - [MainThread] - ✓ ENSG00000003137:
2025-06-18 14:15:12,732 - INFO - [MainThread] -     基因符号: VEP
2025-06-18 14:15:12,734 - INFO - [MainThread] - 
测试统计: 5/5 成功 (100.0%)
2025-06-18 14:15:12,734 - INFO - [MainThread] - ✓ 测试通过! 静态爬虫工作正常
2025-06-18 14:15:12,734 - INFO - [MainThread] - 清理文件: test_gene_ids.csv
2025-06-18 14:15:12,734 - INFO - [MainThread] - 清理文件: test_gene_info_results.csv
2025-06-18 14:16:55,102 - INFO - [MainThread] - 开始测试Ensembl基因信息爬取器...
2025-06-18 14:16:55,105 - INFO - [MainThread] - 创建测试文件: test_gene_ids.csv
2025-06-18 14:16:55,105 - INFO - [MainThread] - 读取输入文件: test_gene_ids.csv
2025-06-18 14:16:55,108 - INFO - [MainThread] - 需要处理 5 个基因
2025-06-18 14:16:55,109 - INFO - [MainThread] - 开始使用 2 个线程处理 5 个基因
2025-06-18 14:16:55,109 - INFO - [MainThread] - 请求延迟: 1.0秒, 超时时间: 15秒
2025-06-18 14:17:06,643 - INFO - [MainThread] - 更新数据框...
2025-06-18 14:17:06,649 - INFO - [MainThread] - 保存最终结果到: test_gene_info_results.csv
2025-06-18 14:17:06,650 - INFO - [MainThread] - 
==================================================
2025-06-18 14:17:06,650 - INFO - [MainThread] - 最终统计结果
2025-06-18 14:17:06,650 - INFO - [MainThread] - ==================================================
2025-06-18 14:17:06,650 - INFO - [MainThread] - 总基因数: 5
2025-06-18 14:17:06,650 - INFO - [MainThread] - 成功获取信息的基因数: 5
2025-06-18 14:17:06,650 - INFO - [MainThread] - 失败的基因数: 0
2025-06-18 14:17:06,650 - INFO - [MainThread] - 成功率: 100.00%
2025-06-18 14:17:06,650 - INFO - [MainThread] - 
结果示例:
2025-06-18 14:17:06,651 - INFO - [MainThread] -   ENSG00000003056 (M6PR): ...
2025-06-18 14:17:06,651 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:17:06,651 - INFO - [MainThread] -   ENSG00000004059 (ARF5): ...
2025-06-18 14:17:06,651 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:17:06,651 - INFO - [MainThread] -   ENSG00000173153 (ESRRA): ...
2025-06-18 14:17:06,651 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:17:06,651 - INFO - [MainThread] -   ENSG00000004478 (FKBP4): ...
2025-06-18 14:17:06,651 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:17:06,651 - INFO - [MainThread] -   ENSG00000003137 (CYP26B1): ...
2025-06-18 14:17:06,651 - INFO - [MainThread] -     类型: , 染色体: 
2025-06-18 14:17:06,653 - INFO - [MainThread] - 
测试结果:
2025-06-18 14:17:06,653 - INFO - [MainThread] - 处理的基因数: 5
2025-06-18 14:17:06,653 - INFO - [MainThread] - ✓ ENSG00000003056:
2025-06-18 14:17:06,653 - INFO - [MainThread] -     基因符号: M6PR
2025-06-18 14:17:06,653 - INFO - [MainThread] - ✓ ENSG00000004059:
2025-06-18 14:17:06,653 - INFO - [MainThread] -     基因符号: ARF5
2025-06-18 14:17:06,653 - INFO - [MainThread] - ✓ ENSG00000173153:
2025-06-18 14:17:06,653 - INFO - [MainThread] -     基因符号: ESRRA
2025-06-18 14:17:06,653 - INFO - [MainThread] - ✓ ENSG00000004478:
2025-06-18 14:17:06,653 - INFO - [MainThread] -     基因符号: FKBP4
2025-06-18 14:17:06,653 - INFO - [MainThread] - ✓ ENSG00000003137:
2025-06-18 14:17:06,653 - INFO - [MainThread] -     基因符号: CYP26B1
2025-06-18 14:17:06,654 - INFO - [MainThread] - 
测试统计: 5/5 成功 (100.0%)
2025-06-18 14:17:06,654 - INFO - [MainThread] - ✓ 测试通过! 静态爬虫工作正常
2025-06-18 14:17:06,654 - INFO - [MainThread] - 清理文件: test_gene_ids.csv
2025-06-18 14:17:06,654 - INFO - [MainThread] - 清理文件: test_gene_info_results.csv
2025-06-18 16:04:47,069 - ERROR - [MainThread] - 爬取过程中出错: EnsemblGeneInfoScraper.__init__() got an unexpected keyword argument 'batch_size'
2025-06-18 16:10:39,389 - ERROR - [MainThread] - 爬取过程中出错: EnsemblGeneInfoScraper.__init__() got an unexpected keyword argument 'batch_size'
2025-06-18 16:20:41,622 - INFO - [MainThread] - 读取输入文件: gene_ids/ensembl_gene_id_unique.csv
2025-06-18 16:20:41,631 - INFO - [MainThread] - 需要处理 20435 个基因 (从第0行开始)
2025-06-18 16:20:41,631 - INFO - [MainThread] - 开始使用 4 个线程处理 20435 个基因
2025-06-18 16:20:41,631 - INFO - [MainThread] - 请求延迟: 0.8秒, 超时时间: 12秒
2025-06-18 16:21:29,207 - INFO - [ThreadPoolExecutor-0_0] - 进度: 50/20435 (0.2%) - 成功: 50 (100.0%)
2025-06-18 16:22:15,945 - INFO - [ThreadPoolExecutor-0_0] - 进度: 100/20435 (0.5%) - 成功: 100 (100.0%)
2025-06-18 16:22:59,230 - INFO - [ThreadPoolExecutor-0_2] - 进度: 150/20435 (0.7%) - 成功: 150 (100.0%)
2025-06-18 16:23:44,837 - INFO - [ThreadPoolExecutor-0_0] - 进度: 200/20435 (1.0%) - 成功: 200 (100.0%)
2025-06-18 16:24:30,873 - INFO - [ThreadPoolExecutor-0_2] - 进度: 250/20435 (1.2%) - 成功: 250 (100.0%)
2025-06-18 16:25:19,431 - INFO - [ThreadPoolExecutor-0_2] - 进度: 300/20435 (1.5%) - 成功: 300 (100.0%)
2025-06-18 16:25:56,387 - WARNING - [ThreadPoolExecutor-0_2] - 跟随重定向失败: HTTPConnectionPool(host='127.0.0.1', port=1082): Read timed out. (read timeout=12)
2025-06-18 16:26:04,598 - WARNING - [ThreadPoolExecutor-0_0] - 请求基因 ENSG00000086548 失败: HTTPConnectionPool(host='127.0.0.1', port=1082): Read timed out.
2025-06-18 16:26:09,068 - WARNING - [ThreadPoolExecutor-0_1] - 请求基因 ENSG00000078018 失败: HTTPConnectionPool(host='127.0.0.1', port=1082): Read timed out. (read timeout=12)
2025-06-18 16:26:14,803 - WARNING - [ThreadPoolExecutor-0_3] - 请求基因 ENSG00000086506 失败: HTTPConnectionPool(host='127.0.0.1', port=1082): Read timed out.
2025-06-18 16:26:22,858 - WARNING - [ThreadPoolExecutor-0_0] - 请求基因 ENSG00000087074 失败: HTTPConnectionPool(host='127.0.0.1', port=1082): Read timed out. (read timeout=12)
2025-06-18 16:26:39,934 - INFO - [ThreadPoolExecutor-0_0] - 进度: 350/20435 (1.7%) - 成功: 345 (98.6%)
2025-06-18 16:26:40,837 - WARNING - [ThreadPoolExecutor-0_1] - 跟随重定向失败: HTTPConnectionPool(host='127.0.0.1', port=1082): Max retries exceeded with url: http://www.ensembl.org/Homo_sapiens/Gene/Summary?db=core;g=ENSG00000073670;r=17:44758988-44781846 (Caused by ProxyError('Unable to connect to proxy', RemoteDisconnected('Remote end closed connection without response')))
2025-06-18 16:27:28,654 - INFO - [ThreadPoolExecutor-0_3] - 进度: 400/20435 (2.0%) - 成功: 394 (98.5%)
2025-06-18 16:28:13,777 - INFO - [ThreadPoolExecutor-0_0] - 进度: 450/20435 (2.2%) - 成功: 444 (98.7%)
2025-06-18 16:28:58,570 - INFO - [ThreadPoolExecutor-0_1] - 进度: 500/20435 (2.4%) - 成功: 494 (98.8%)
2025-06-18 16:29:43,623 - INFO - [ThreadPoolExecutor-0_0] - 进度: 550/20435 (2.7%) - 成功: 544 (98.9%)
2025-06-18 16:30:33,032 - INFO - [ThreadPoolExecutor-0_2] - 进度: 600/20435 (2.9%) - 成功: 594 (99.0%)
2025-06-18 16:31:16,567 - INFO - [ThreadPoolExecutor-0_0] - 进度: 650/20435 (3.2%) - 成功: 644 (99.1%)
2025-06-18 16:32:02,438 - INFO - [ThreadPoolExecutor-0_2] - 进度: 700/20435 (3.4%) - 成功: 694 (99.1%)
2025-06-18 16:32:49,170 - INFO - [ThreadPoolExecutor-0_0] - 进度: 750/20435 (3.7%) - 成功: 744 (99.2%)
2025-06-18 16:33:36,868 - INFO - [ThreadPoolExecutor-0_1] - 进度: 800/20435 (3.9%) - 成功: 794 (99.2%)
2025-06-18 16:34:24,465 - INFO - [ThreadPoolExecutor-0_2] - 进度: 850/20435 (4.2%) - 成功: 844 (99.3%)
2025-06-18 16:35:07,737 - INFO - [ThreadPoolExecutor-0_1] - 进度: 900/20435 (4.4%) - 成功: 894 (99.3%)
2025-06-18 16:35:52,827 - INFO - [ThreadPoolExecutor-0_3] - 进度: 950/20435 (4.6%) - 成功: 944 (99.4%)
2025-06-18 16:36:38,778 - INFO - [ThreadPoolExecutor-0_0] - 进度: 1000/20435 (4.9%) - 成功: 994 (99.4%)
2025-06-18 16:37:24,088 - INFO - [ThreadPoolExecutor-0_3] - 进度: 1050/20435 (5.1%) - 成功: 1044 (99.4%)
2025-06-18 16:38:07,529 - INFO - [ThreadPoolExecutor-0_2] - 进度: 1100/20435 (5.4%) - 成功: 1094 (99.5%)
2025-06-18 16:38:55,904 - INFO - [ThreadPoolExecutor-0_2] - 进度: 1150/20435 (5.6%) - 成功: 1144 (99.5%)
2025-06-18 16:39:41,272 - INFO - [ThreadPoolExecutor-0_0] - 进度: 1200/20435 (5.9%) - 成功: 1194 (99.5%)
2025-06-18 16:40:27,080 - INFO - [ThreadPoolExecutor-0_0] - 进度: 1250/20435 (6.1%) - 成功: 1244 (99.5%)
2025-06-18 16:41:13,193 - INFO - [ThreadPoolExecutor-0_3] - 进度: 1300/20435 (6.4%) - 成功: 1294 (99.5%)
2025-06-18 16:41:57,342 - INFO - [ThreadPoolExecutor-0_0] - 进度: 1350/20435 (6.6%) - 成功: 1344 (99.6%)
2025-06-18 16:42:42,153 - INFO - [ThreadPoolExecutor-0_3] - 进度: 1400/20435 (6.9%) - 成功: 1394 (99.6%)
2025-06-18 16:43:26,089 - INFO - [ThreadPoolExecutor-0_0] - 进度: 1450/20435 (7.1%) - 成功: 1444 (99.6%)
2025-06-18 16:44:11,339 - INFO - [ThreadPoolExecutor-0_3] - 进度: 1500/20435 (7.3%) - 成功: 1494 (99.6%)
2025-06-18 16:44:54,533 - INFO - [ThreadPoolExecutor-0_3] - 进度: 1550/20435 (7.6%) - 成功: 1544 (99.6%)
2025-06-18 16:45:38,213 - INFO - [ThreadPoolExecutor-0_3] - 进度: 1600/20435 (7.8%) - 成功: 1594 (99.6%)
2025-06-18 16:46:22,736 - INFO - [ThreadPoolExecutor-0_0] - 进度: 1650/20435 (8.1%) - 成功: 1644 (99.6%)
2025-06-18 16:47:08,526 - INFO - [ThreadPoolExecutor-0_2] - 进度: 1700/20435 (8.3%) - 成功: 1694 (99.6%)
2025-06-18 16:47:52,548 - INFO - [ThreadPoolExecutor-0_0] - 进度: 1750/20435 (8.6%) - 成功: 1744 (99.7%)
2025-06-18 16:48:36,948 - INFO - [ThreadPoolExecutor-0_0] - 进度: 1800/20435 (8.8%) - 成功: 1794 (99.7%)
2025-06-18 16:49:24,659 - INFO - [ThreadPoolExecutor-0_2] - 进度: 1850/20435 (9.1%) - 成功: 1844 (99.7%)
2025-06-18 16:50:12,974 - INFO - [ThreadPoolExecutor-0_3] - 进度: 1900/20435 (9.3%) - 成功: 1894 (99.7%)
2025-06-18 16:50:57,731 - INFO - [ThreadPoolExecutor-0_0] - 进度: 1950/20435 (9.5%) - 成功: 1944 (99.7%)
2025-06-18 16:51:42,692 - INFO - [ThreadPoolExecutor-0_3] - 进度: 2000/20435 (9.8%) - 成功: 1994 (99.7%)
2025-06-18 16:52:27,454 - INFO - [ThreadPoolExecutor-0_0] - 进度: 2050/20435 (10.0%) - 成功: 2044 (99.7%)
2025-06-18 16:53:11,849 - INFO - [ThreadPoolExecutor-0_3] - 进度: 2100/20435 (10.3%) - 成功: 2094 (99.7%)
