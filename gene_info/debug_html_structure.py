#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from bs4 import BeautifulSoup
import sys

def debug_ensembl_page(gene_id):
    """调试Ensembl页面结构"""
    url = f"http://www.ensembl.org/id/{gene_id}"
    
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        print(f"调试基因: {gene_id}")
        print(f"URL: {url}")
        print("="*50)
        
        # 查找页面标题
        title = soup.find('h1', class_='summary-heading')
        if title:
            print(f"页面标题: {title.get_text(strip=True)}")
        else:
            print("未找到页面标题")
        
        # 查找所有h1标签
        h1_tags = soup.find_all('h1')
        print(f"\n所有H1标签 ({len(h1_tags)}个):")
        for i, h1 in enumerate(h1_tags):
            print(f"  {i+1}. {h1.get_text(strip=True)}")
        
        # 查找Summary面板
        summary_panel = soup.find('div', {'id': 'GeneSummary'})
        if summary_panel:
            print(f"\n找到GeneSummary面板")
            rows = summary_panel.find_all('div', class_='row')
            print(f"行数: {len(rows)}")
            for row in rows:
                lhs = row.find('div', class_='lhs')
                rhs = row.find('div', class_='rhs')
                if lhs and rhs:
                    label = lhs.get_text(strip=True)
                    content = rhs.get_text(strip=True)[:100]
                    print(f"  {label}: {content}...")
        else:
            print("\n未找到GeneSummary面板")
        
        # 查找Gene面板
        gene_panel = soup.find('div', {'id': 'ensembl_panel_2'})
        if gene_panel:
            print(f"\n找到ensembl_panel_2面板")
            summary_panel_div = gene_panel.find('div', class_='summary_panel')
            if summary_panel_div:
                print("找到summary_panel div")
                rows = summary_panel_div.find_all('div', class_='row')
                print(f"行数: {len(rows)}")
                for row in rows:
                    lhs = row.find('div', class_='lhs')
                    rhs = row.find('div', class_='rhs')
                    if lhs and rhs:
                        label = lhs.get_text(strip=True)
                        content = rhs.get_text(strip=True)[:100]
                        print(f"  {label}: {content}...")
            else:
                print("未找到summary_panel div")
        else:
            print("\n未找到ensembl_panel_2面板")
        
        # 查找所有可能包含基因符号的地方
        print(f"\n搜索基因符号...")
        import re
        
        # 在HTML中搜索可能的基因符号
        gene_symbol_patterns = [
            r'Gene:\s*([A-Z0-9\-_]{1,15})\s+ENS',
            r'"name":\s*"([A-Z0-9\-_]{1,15})"',
            r'<h1[^>]*>Gene:\s*([A-Z0-9\-_]{1,15})',
        ]
        
        for pattern in gene_symbol_patterns:
            matches = re.findall(pattern, response.text)
            if matches:
                print(f"  模式 '{pattern}' 匹配: {matches}")
        
        # 保存部分HTML用于分析
        print(f"\n保存HTML片段到 debug_{gene_id}.html")
        with open(f"debug_{gene_id}.html", 'w', encoding='utf-8') as f:
            # 只保存前20000字符避免文件太大
            f.write(response.text[:20000])
            
    except Exception as e:
        print(f"调试过程中出错: {e}")

if __name__ == "__main__":
    # 调试一个基因
    debug_ensembl_page("ENSG00000003056")  # M6PR 