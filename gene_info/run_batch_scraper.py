#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
批量转录本爬取器启动脚本
支持多种配置参数的快速启动
"""

import subprocess
import sys
import os
import argparse

def run_scraper(config_name="default"):
    """运行批量转录本爬取器"""
    
    # 预定义的配置
    configs = {
        "test": {
            "workers": 2,
            "batch_size": 100,
            "timeout": 10,
            "max_retries": 2,
            "description": "测试配置 - 适合小批量验证"
        },
        "default": {
            "workers": 4,
            "batch_size": 500,
            "timeout": 8,
            "max_retries": 3,
            "description": "默认配置 - 适合一般使用"
        },
        "aggressive": {
            "workers": 6,
            "batch_size": 1000,
            "timeout": 12,
            "max_retries": 5,
            "description": "激进配置 - 更多线程和重试次数，适合网络较差的环境"
        },
        "conservative": {
            "workers": 2,
            "batch_size": 200,
            "timeout": 15,
            "max_retries": 4,
            "description": "保守配置 - 较少线程但更多重试，适合系统资源有限的情况"
        },
        "fast": {
            "workers": 8,
            "batch_size": 1500,
            "timeout": 6,
            "max_retries": 2,
            "description": "快速配置 - 最大并发，适合网络条件良好的环境"
        }
    }
    
    if config_name not in configs:
        print(f"错误: 未知配置 '{config_name}'")
        print("可用配置:")
        for name, config in configs.items():
            print(f"  {name}: {config['description']}")
        return False
    
    config = configs[config_name]
    
    print(f"启动配置: {config_name}")
    print(f"描述: {config['description']}")
    print(f"参数: {config}")
    print("-" * 50)
    
    # 构建命令
    cmd = [
        "python", "batch_transcript_scraper_optimized.py",
        "--workers", str(config["workers"]),
        "--batch-size", str(config["batch_size"]),
        "--timeout", str(config["timeout"]),
        "--max-retries", str(config["max_retries"])
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        # 运行脚本
        result = subprocess.run(cmd, check=True)
        print("\n✓ 批量处理完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n✗ 批量处理失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⚠ 用户中断处理")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量转录本爬取器启动脚本')
    parser.add_argument('config', nargs='?', default='default', 
                       help='配置名称: test, default, aggressive, conservative, fast')
    parser.add_argument('--list-configs', action='store_true', 
                       help='列出所有可用配置')
    parser.add_argument('--dry-run', action='store_true', 
                       help='仅显示将要执行的命令，不实际运行')
    
    args = parser.parse_args()
    
    # 配置定义
    configs = {
        "test": {
            "workers": 2,
            "batch_size": 100,
            "timeout": 10,
            "max_retries": 2,
            "description": "测试配置 - 适合小批量验证"
        },
        "default": {
            "workers": 4,
            "batch_size": 500,
            "timeout": 8,
            "max_retries": 3,
            "description": "默认配置 - 适合一般使用"
        },
        "aggressive": {
            "workers": 6,
            "batch_size": 1000,
            "timeout": 12,
            "max_retries": 5,
            "description": "激进配置 - 更多线程和重试次数，适合网络较差的环境"
        },
        "conservative": {
            "workers": 2,
            "batch_size": 200,
            "timeout": 15,
            "max_retries": 4,
            "description": "保守配置 - 较少线程但更多重试，适合系统资源有限的情况"
        },
        "fast": {
            "workers": 8,
            "batch_size": 1500,
            "timeout": 6,
            "max_retries": 2,
            "description": "快速配置 - 最大并发，适合网络条件良好的环境"
        }
    }
    
    if args.list_configs:
        print("可用配置:")
        print("=" * 60)
        for name, config in configs.items():
            print(f"\n配置名: {name}")
            print(f"描述: {config['description']}")
            print(f"线程数: {config['workers']}")
            print(f"批次大小: {config['batch_size']}")
            print(f"超时时间: {config['timeout']}秒")
            print(f"最大重试: {config['max_retries']}次")
        print("\n使用方法:")
        print("  python run_batch_scraper.py <配置名>")
        print("  例如: python run_batch_scraper.py aggressive")
        return
    
    if args.config not in configs:
        print(f"错误: 未知配置 '{args.config}'")
        print("使用 --list-configs 查看所有可用配置")
        sys.exit(1)
    
    config = configs[args.config]
    
    print("批量转录本爬取器")
    print("=" * 60)
    print(f"配置: {args.config}")
    print(f"描述: {config['description']}")
    print(f"线程数: {config['workers']}")
    print(f"批次大小: {config['batch_size']}")
    print(f"超时时间: {config['timeout']}秒")
    print(f"最大重试: {config['max_retries']}次")
    print("=" * 60)
    
    # 构建命令
    cmd = [
        "python", "batch_transcript_scraper_optimized.py",
        "--workers", str(config["workers"]),
        "--batch-size", str(config["batch_size"]),
        "--timeout", str(config["timeout"]),
        "--max-retries", str(config["max_retries"])
    ]
    
    if args.dry_run:
        print("将要执行的命令:")
        print(" ".join(cmd))
        print("\n使用 --dry-run 标志仅预览命令，实际运行请去掉此标志")
        return
    
    print(f"执行命令: {' '.join(cmd)}")
    print("=" * 60)
    
    # 确认执行
    try:
        response = input("是否开始处理? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("取消执行")
            return
    except KeyboardInterrupt:
        print("\n取消执行")
        return
    
    try:
        # 运行脚本
        result = subprocess.run(cmd, check=True)
        print("\n" + "=" * 60)
        print("✓ 批量处理完成!")
    except subprocess.CalledProcessError as e:
        print(f"\n✗ 批量处理失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠ 用户中断处理")
        sys.exit(1)

if __name__ == "__main__":
    main() 