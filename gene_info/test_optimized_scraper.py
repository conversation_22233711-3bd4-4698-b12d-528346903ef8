#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import logging
import sys
import os
from batch_transcript_scraper_optimized import TranscriptScraper

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def create_test_csv():
    """创建测试用的CSV文件"""
    test_data = {
        'ensembl_gene_id': [
            'ENSG00000004059',  # ARF5
            'ENSG00000003056',  # M6PR
            'ENSG00000173153',  # ESRRA
            'ENSG00000004478',  # FKBP4
            'ENSG00000003137',  # CYP26B1
        ],
        'GENE symbol': ['ARF5', 'M6PR', 'ESRRA', 'FKBP4', 'CYP26B1'],
        'Approved Name': [
            'ARF GTPase 5',
            'mannose-6-phosphate receptor, cation dependent',
            'estrogen related receptor alpha',
            'FKBP prolyl isomerase 4',
            'cytochrome P450 family 26 subfamily B member 1'
        ],
        'Locus Type': ['protein coding'] * 5,
        'Chromosome': ['7q32.1', '12p13.31', '11q13.1', '12p13.33', '2p13.2'],
        'Gene Summary': ['Test summary'] * 5
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_genes.csv'
    df.to_csv(test_file, index=False)
    logging.info(f"创建测试文件: {test_file}")
    return test_file

def main():
    """测试主函数"""
    logging.info("开始测试优化版转录本爬取器...")
    
    # 创建测试文件
    test_input = create_test_csv()
    test_output = 'test_genes_with_transcripts.csv'
    
    try:
        # 创建爬取器实例（使用较少的线程进行测试）
        scraper = TranscriptScraper(
            max_workers=2,
            batch_size=3,
            timeout=10,
            max_retries=2  # 测试时使用较少的重试次数
        )
        
        # 运行测试
        success = scraper.run(test_input, test_output)
        
        if success and os.path.exists(test_output):
            # 检查结果
            result_df = pd.read_csv(test_output)
            logging.info("\n测试结果:")
            logging.info(f"处理的基因数: {len(result_df)}")
            
            # 显示每个基因的结果
            for _, row in result_df.iterrows():
                gene_id = row['ensembl_gene_id']
                gene_symbol = row['GENE symbol']
                transcript_count = row['transcript_count']
                
                if transcript_count > 0:
                    logging.info(f"✓ {gene_id} ({gene_symbol}): {transcript_count} 个转录本")
                    
                    # 显示前2个转录本
                    import json
                    transcripts = json.loads(row['transcripts'])
                    for i, transcript in enumerate(transcripts[:2]):
                        logging.info(f"    {i+1}. {transcript['transcript_id']}: {transcript['name']}")
                    if len(transcripts) > 2:
                        logging.info(f"    ... 还有 {len(transcripts)-2} 个转录本")
                else:
                    logging.warning(f"✗ {gene_id} ({gene_symbol}): 未获取到转录本")
            
            # 统计
            successful = len(result_df[result_df['transcript_count'] > 0])
            total = len(result_df)
            logging.info(f"\n测试统计: {successful}/{total} 成功 ({successful/total*100:.1f}%)")
            
            if successful >= 3:  # 至少成功3个
                logging.info("✓ 测试通过! 爬取器工作正常")
                return True
            else:
                logging.warning("⚠ 测试部分通过，成功率较低")
                return False
                
        else:
            logging.error("✗ 测试失败!")
            return False
            
    except Exception as e:
        logging.error(f"测试过程中出错: {e}")
        return False
        
    finally:
        # 清理测试文件
        for file in [test_input, test_output, 'transcript_checkpoint.csv']:
            if os.path.exists(file):
                os.remove(file)
                logging.info(f"清理文件: {file}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 