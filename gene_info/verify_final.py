import pandas as pd

df = pd.read_csv('ensembl_gene_id_with_full_info_final.csv')
print('='*60)
print('最终文件验证')
print('='*60)
print(f'总行数: {len(df):,}')
print(f'列名: {list(df.columns)}')

print('\n前3行示例:')
for i in range(3):
    row = df.iloc[i]
    print(f'\n基因ID: {row["ensembl_gene_id"]}')
    print(f'基因符号: {row["GENE symbol"]}')
    print(f'染色体: {row["Chromosome"]}')
    print(f'染色体URL: {row["Chromosome_URL"][:60]}...')
    print(f'转录本数量: {row["transcript_count"]}')
    print(f'转录本信息: {str(row["transcripts"])[:80]}...')
    print('-' * 40)

# 统计转录本信息
has_transcript = df[df['transcript_count'] != '']
print(f'\n统计信息:')
print(f'有转录本信息的基因数: {len(has_transcript):,}')
print(f'转录本信息完整率: {len(has_transcript)/len(df)*100:.2f}%')

# 转录本数量分布
if len(has_transcript) > 0:
    print(f'转录本数量统计:')
    transcript_counts = has_transcript['transcript_count'].astype(int)
    print(f'  平均转录本数: {transcript_counts.mean():.2f}')
    print(f'  最少转录本数: {transcript_counts.min()}')
    print(f'  最多转录本数: {transcript_counts.max()}') 