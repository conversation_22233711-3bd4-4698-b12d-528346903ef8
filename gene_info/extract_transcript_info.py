#!/usr/bin/env python3
"""
从HTML文件中提取转录本信息
从Ensembl基因页面的HTML内容中提取Transcript ID和Name，输出为JSON格式
"""

import re
import json
from bs4 import BeautifulSoup

def extract_transcript_info(html_file_path):
    """
    从HTML文件中提取转录本信息
    
    Args:
        html_file_path: HTML文件路径
        
    Returns:
        list: 包含转录本信息的字典列表
    """
    transcripts = []
    
    try:
        # 读取HTML文件
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找转录本表格
        table = soup.find('table', id='transcripts_table')
        if not table:
            print("未找到转录本表格")
            return transcripts
        
        # 查找表格体
        tbody = table.find('tbody')
        if not tbody:
            print("未找到表格体")
            return transcripts
        
        # 遍历表格行
        rows = tbody.find_all('tr')
        for row in rows:
            cells = row.find_all('td')
            if len(cells) >= 2:
                # 第一列是Transcript ID
                transcript_id_cell = cells[0]
                transcript_id_link = transcript_id_cell.find('a')
                if transcript_id_link:
                    transcript_id = transcript_id_link.get_text(strip=True)
                else:
                    continue
                
                # 第二列是Name
                name_cell = cells[1]
                name = name_cell.get_text(strip=True)
                
                # 添加到结果列表
                if transcript_id and name:
                    transcripts.append({
                        "transcript_id": transcript_id,
                        "name": name
                    })
        
        print(f"成功提取了 {len(transcripts)} 个转录本信息")
        
    except Exception as e:
        print(f"提取转录本信息时出错: {e}")
    
    return transcripts

def main():
    """主函数"""
    html_file = "a.txt"
    
    print(f"从文件 {html_file} 提取转录本信息...")
    
    # 提取转录本信息
    transcripts = extract_transcript_info(html_file)
    
    if transcripts:
        # 输出JSON格式（紧凑格式，不换行）
        json_output = json.dumps(transcripts, ensure_ascii=False, separators=(',', ':'))
        print("\n提取的转录本信息（JSON格式）:")
        print(json_output)
        
        # 保存到文件（紧凑格式）
        output_file = "transcript_info.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(transcripts, f, ensure_ascii=False, separators=(',', ':'))
        print(f"\n结果已保存到 {output_file}")
        
        # 显示统计信息
        print(f"\n统计信息:")
        print(f"- 总转录本数量: {len(transcripts)}")
        
        # 显示前几个示例
        print(f"\n前5个转录本示例:")
        for i, transcript in enumerate(transcripts[:5]):
            print(f"{i+1}. {transcript['transcript_id']} -> {transcript['name']}")
    else:
        print("未提取到任何转录本信息")

if __name__ == "__main__":
    main() 