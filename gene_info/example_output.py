#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ensembl_gene_info_scraper import EnsemblGeneInfoScraper

def show_output_example():
    """展示最终输出格式示例"""
    
    print("="*80)
    print("Ensembl基因信息爬取器 - 输出格式示例")
    print("="*80)
    
    # 创建爬取器实例
    scraper = EnsemblGeneInfoScraper(max_workers=1, timeout=15, delay=1.0)
    
    # 获取一个基因的信息作为示例
    gene_id = 'ENSG00000173153'  # ESRRA
    print(f"示例基因: {gene_id}")
    print("-" * 80)
    
    try:
        gene_id_result, gene_info = scraper.scrape_single_gene(gene_id, 1)
        
        print("提取到的4个字段信息:")
        print()
        
        print(f"1. GENE symbol (基因符号):")
        print(f"   {gene_info['GENE symbol']}")
        print()
        
        print(f"2. Approved Name (批准名称):")
        print(f"   {gene_info['Approved Name']}")
        print()
        
        print(f"3. Locus Type (基因类型):")
        print(f"   {gene_info['Locus Type']}")
        print()
        
        print(f"4. Chromosome (染色体 - key:value格式):")
        chromosome_info = gene_info['Chromosome']
        print(f"   {chromosome_info}")
        
        # 解析key-value格式
        if ':http' in chromosome_info:
            split_pos = chromosome_info.rfind(':http')
            key_part = chromosome_info[:split_pos]
            value_part = chromosome_info[split_pos+1:]
            
            print()
            print("   解析后的key-value:")
            print(f"   Key (染色体名称): {key_part}")
            print(f"   Value (链接): {value_part}")
        
        print()
        print("="*80)
        print("CSV输出格式预览:")
        print("="*80)
        print("ensembl_gene_id,GENE symbol,Approved Name,Locus Type,Chromosome")
        print(f"{gene_id},{gene_info['GENE symbol']},\"{gene_info['Approved Name']}\",{gene_info['Locus Type']},\"{gene_info['Chromosome']}\"")
        
    except Exception as e:
        print(f"获取示例时出错: {e}")

if __name__ == "__main__":
    show_output_example() 