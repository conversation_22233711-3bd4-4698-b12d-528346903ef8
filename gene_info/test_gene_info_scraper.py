#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import logging
import sys
import os
from ensembl_gene_info_scraper import EnsemblGeneInfoScraper

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def create_test_csv():
    """创建测试用的CSV文件"""
    test_data = {
        'ensembl_gene_id': [
            'ENSG00000003056',  # M6PR
            'ENSG00000004059',  # ARF5
            'ENSG00000173153',  # ESRRA
            'ENSG00000004478',  # FKBP4
            'ENSG00000003137',  # CYP26B1
        ]
    }
    
    df = pd.DataFrame(test_data)
    test_file = 'test_gene_ids.csv'
    df.to_csv(test_file, index=False)
    logging.info(f"创建测试文件: {test_file}")
    return test_file

def main():
    """测试主函数"""
    logging.info("开始测试Ensembl基因信息爬取器...")
    
    # 创建测试文件
    test_input = create_test_csv()
    test_output = 'test_gene_info_results.csv'
    
    try:
        # 创建爬取器实例（使用较少的线程和延迟进行测试）
        scraper = EnsemblGeneInfoScraper(
            max_workers=2,
            timeout=15,
            delay=1.0  # 测试时使用较长延迟
        )
        
        # 运行测试
        success = scraper.run(test_input, test_output)
        
        if success and os.path.exists(test_output):
            # 检查结果
            result_df = pd.read_csv(test_output)
            logging.info("\n测试结果:")
            logging.info(f"处理的基因数: {len(result_df)}")
            
            # 显示每个基因的结果
            for _, row in result_df.iterrows():
                gene_id = row['ensembl_gene_id']
                gene_symbol = row['GENE symbol'] if pd.notna(row['GENE symbol']) else ''
                approved_name = row['Approved Name'] if pd.notna(row['Approved Name']) else ''
                locus_type = row['Locus Type'] if pd.notna(row['Locus Type']) else ''
                chromosome = row['Chromosome'] if pd.notna(row['Chromosome']) else ''
                
                if gene_symbol:
                    logging.info(f"✓ {gene_id}:")
                    logging.info(f"    基因符号: {gene_symbol}")
                    if approved_name:
                        logging.info(f"    名称: {approved_name[:60]}{'...' if len(approved_name) > 60 else ''}")
                    if locus_type:
                        logging.info(f"    类型: {locus_type}")
                    if chromosome:
                        logging.info(f"    染色体: {chromosome}")
                else:
                    logging.warning(f"✗ {gene_id}: 未获取到信息")
            
            # 统计
            successful = len(result_df[result_df['GENE symbol'] != ''])
            total = len(result_df)
            logging.info(f"\n测试统计: {successful}/{total} 成功 ({successful/total*100:.1f}%)")
            
            if successful >= 3:  # 至少成功3个
                logging.info("✓ 测试通过! 静态爬虫工作正常")
                return True
            else:
                logging.warning("⚠ 测试部分通过，成功率较低")
                return False
                
        else:
            logging.error("✗ 测试失败!")
            return False
            
    except Exception as e:
        logging.error(f"测试过程中出错: {e}")
        return False
        
    finally:
        # 清理测试文件
        for file in [test_input, test_output]:
            if os.path.exists(file):
                os.remove(file)
                logging.info(f"清理文件: {file}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 