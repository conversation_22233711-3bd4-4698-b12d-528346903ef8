#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import pandas as pd
import json
import time
import logging
import sys
import os
from concurrent.futures import ThreadPoolExecutor
import threading

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_transcript_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

# 线程本地存储用于WebDriver
thread_local = threading.local()

def get_driver():
    """获取线程本地的WebDriver"""
    if not hasattr(thread_local, 'driver'):
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        chrome_options.add_argument('--disable-logging')
        chrome_options.add_argument('--log-level=3')
        
        try:
            thread_local.driver = webdriver.Chrome(options=chrome_options)
        except Exception as e:
            logging.error(f"创建WebDriver失败: {e}")
            return None
    
    return thread_local.driver

def close_driver():
    """关闭线程本地的WebDriver"""
    if hasattr(thread_local, 'driver'):
        try:
            thread_local.driver.quit()
        except:
            pass
        del thread_local.driver

def scrape_single_gene_transcript(gene_id):
    """爬取单个基因的转录本数据"""
    driver = get_driver()
    if not driver:
        return {
            "gene_id": gene_id,
            "transcript_count": 0,
            "transcripts": [],
            "error": "Failed to create driver"
        }
    
    url = f"http://www.ensembl.org/id/{gene_id}"
    
    try:
        # 访问页面
        driver.get(url)
        
        # 设置较短的等待时间
        wait = WebDriverWait(driver, 8)
        
        # 查找并点击"Show transcript table"按钮
        try:
            transcript_button = wait.until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, 'a[rel="transcripts_table"].button.toggle'))
            )
            
            if "closed" in transcript_button.get_attribute("class"):
                driver.execute_script("arguments[0].click();", transcript_button)
                time.sleep(1)  # 减少等待时间
                
        except TimeoutException:
            return {
                "gene_id": gene_id,
                "transcript_count": 0,
                "transcripts": [],
                "error": "Button not found"
            }
        
        # 等待表格出现
        try:
            transcript_table = wait.until(
                EC.presence_of_element_located((By.ID, "transcripts_table"))
            )
        except TimeoutException:
            return {
                "gene_id": gene_id,
                "transcript_count": 0,
                "transcripts": [],
                "error": "Table not found"
            }
        
        # 提取数据
        transcript_data = []
        
        try:
            tbody = transcript_table.find_element(By.TAG_NAME, "tbody")
            rows = tbody.find_elements(By.TAG_NAME, "tr")
            
            for row in rows:
                try:
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if len(cells) >= 2:
                        # Transcript ID
                        transcript_id_cell = cells[0]
                        transcript_link = transcript_id_cell.find_element(By.TAG_NAME, "a")
                        transcript_id = transcript_link.text.strip()
                        
                        # Name
                        name_cell = cells[1]
                        name = name_cell.text.strip()
                        
                        transcript_data.append({
                            "transcript_id": transcript_id,
                            "name": name
                        })
                        
                except Exception:
                    continue
                    
        except NoSuchElementException:
            return {
                "gene_id": gene_id,
                "transcript_count": 0,
                "transcripts": [],
                "error": "Table structure error"
            }
        
        return {
            "gene_id": gene_id,
            "transcript_count": len(transcript_data),
            "transcripts": transcript_data
        }
        
    except Exception as e:
        return {
            "gene_id": gene_id,
            "transcript_count": 0,
            "transcripts": [],
            "error": str(e)
        }

def load_gene_ids_from_csv(csv_file):
    """从CSV文件加载基因ID列表"""
    try:
        df = pd.read_csv(csv_file)
        gene_ids = df['ensembl_gene_id'].tolist()
        logging.info(f"从 {csv_file} 加载了 {len(gene_ids)} 个基因ID")
        return gene_ids
    except Exception as e:
        logging.error(f"加载CSV文件失败: {e}")
        return []

def save_checkpoint(results, checkpoint_file):
    """保存检查点"""
    try:
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.error(f"保存检查点失败: {e}")

def load_checkpoint(checkpoint_file):
    """加载检查点"""
    try:
        if os.path.exists(checkpoint_file):
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        logging.error(f"加载检查点失败: {e}")
    return {}

def batch_process_genes(gene_ids, max_workers=3, batch_size=100):
    """批量处理基因，支持断点续传"""
    checkpoint_file = "transcript_scraping_checkpoint.json"
    results = load_checkpoint(checkpoint_file)
    
    # 过滤已处理的基因
    remaining_genes = [gid for gid in gene_ids if gid not in results]
    
    if remaining_genes:
        logging.info(f"需要处理 {len(remaining_genes)} 个基因 (已完成 {len(results)} 个)")
    else:
        logging.info("所有基因已处理完成")
        return results
    
    # 分批处理
    for i in range(0, len(remaining_genes), batch_size):
        batch = remaining_genes[i:i+batch_size]
        batch_num = i // batch_size + 1
        total_batches = (len(remaining_genes) + batch_size - 1) // batch_size
        
        logging.info(f"开始处理第 {batch_num}/{total_batches} 批，包含 {len(batch)} 个基因")
        
        # 使用线程池处理当前批次
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            try:
                batch_results = list(executor.map(scrape_single_gene_transcript, batch))
                
                # 合并结果
                for result in batch_results:
                    results[result['gene_id']] = result
                
                # 保存检查点
                save_checkpoint(results, checkpoint_file)
                
                # 显示批次统计
                successful_in_batch = sum(1 for r in batch_results if r['transcript_count'] > 0)
                logging.info(f"第 {batch_num} 批完成: {successful_in_batch}/{len(batch)} 成功")
                
            except Exception as e:
                logging.error(f"处理第 {batch_num} 批时出错: {e}")
            finally:
                # 确保清理所有线程的驱动
                for _ in range(max_workers):
                    try:
                        close_driver()
                    except:
                        pass
        
        # 批次间休息
        if i + batch_size < len(remaining_genes):
            time.sleep(2)
    
    return results

def main():
    """主函数"""
    # 配置文件路径
    csv_file = "gene_ids/ensembl_gene_id_unique.csv"
    output_file = "all_transcript_data_results.json"
    
    # 检查输入文件
    if not os.path.exists(csv_file):
        logging.error(f"基因ID文件不存在: {csv_file}")
        return
    
    # 加载基因ID
    gene_ids = load_gene_ids_from_csv(csv_file)
    if not gene_ids:
        logging.error("未能加载基因ID")
        return
    
    # 可选：只处理前N个基因进行测试
    # gene_ids = gene_ids[:50]  # 取消注释以测试前50个基因
    
    logging.info(f"开始批量爬取 {len(gene_ids)} 个基因的转录本数据")
    
    # 批量处理
    results = batch_process_genes(gene_ids, max_workers=3, batch_size=50)
    
    # 保存最终结果
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logging.info(f"最终结果已保存到: {output_file}")
    except Exception as e:
        logging.error(f"保存最终结果失败: {e}")
    
    # 统计信息
    total_genes = len(results)
    successful_genes = len([r for r in results.values() if r.get('transcript_count', 0) > 0])
    total_transcripts = sum(r.get('transcript_count', 0) for r in results.values())
    failed_genes = [gid for gid, data in results.items() if data.get('transcript_count', 0) == 0]
    
    logging.info("\n=== 最终统计 ===")
    logging.info(f"总基因数: {total_genes}")
    logging.info(f"成功爬取的基因数: {successful_genes}")
    logging.info(f"失败的基因数: {len(failed_genes)}")
    logging.info(f"总转录本数: {total_transcripts}")
    logging.info(f"成功率: {(successful_genes/total_genes*100):.1f}%")
    
    # 保存失败的基因列表
    if failed_genes:
        failed_file = "failed_transcript_genes.txt"
        with open(failed_file, 'w') as f:
            for gene_id in failed_genes:
                f.write(f"{gene_id}\n")
        logging.info(f"失败的基因ID已保存到: {failed_file}")

if __name__ == "__main__":
    main() 