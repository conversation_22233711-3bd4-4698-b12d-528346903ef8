#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ensembl_gene_info_scraper import EnsemblGeneInfoScraper
import logging

# 设置简单日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')

def test_chromosome_link():
    """测试染色体链接功能"""
    
    # 测试基因ID列表
    test_genes = [
        'ENSG00000003056',  # M6PR - Chromosome 12
        'ENSG00000173153',  # ESRRA - Chromosome 11  
        'ENSG00000004487',  # KDM1A - Chromosome 1
    ]
    
    print("="*80)
    print("测试染色体链接功能")
    print("="*80)
    print("验证染色体字段是否包含key-value格式的信息")
    print("预期格式: 'Chromosome X: start-end:http://www.ensembl.org/...'")
    print("="*80)
    
    # 创建爬取器实例
    scraper = EnsemblGeneInfoScraper(
        max_workers=1,    # 单线程测试
        timeout=15,       # 较长超时时间
        delay=1.0         # 1秒延迟
    )
    
    # 测试每个基因
    for i, gene_id in enumerate(test_genes, 1):
        print(f"\n[{i}/{len(test_genes)}] 测试基因: {gene_id}")
        
        try:
            gene_id_result, gene_info = scraper.scrape_single_gene(gene_id, len(test_genes))
            
            print(f"基因符号: {gene_info['GENE symbol']}")
            print(f"批准名称: {gene_info['Approved Name'][:50]}...")
            print(f"基因类型: {gene_info['Locus Type']}")
            
            # 重点检查染色体信息
            chromosome_info = gene_info['Chromosome']
            print(f"染色体信息: {chromosome_info}")
            
            # 验证格式
            if ':http' in chromosome_info:
                # 找到最后一个包含http的冒号位置
                split_pos = chromosome_info.rfind(':http')
                key_part = chromosome_info[:split_pos]
                value_part = chromosome_info[split_pos+1:]  # 去掉冒号
                
                print(f"  ✅ 包含key-value分隔符 ':http'")
                print(f"  Key部分 (名称): {key_part}")
                print(f"  Value部分 (链接): {value_part}")
                
                # 检查链接格式
                if value_part.startswith('http://www.ensembl.org/'):
                    print(f"  ✅ 链接格式正确")
                else:
                    print(f"  ❌ 链接格式错误，应以 'http://www.ensembl.org/' 开头")
                
                # 检查key部分是否包含染色体信息
                if 'Chromosome' in key_part and any(char.isdigit() for char in key_part):
                    print(f"  ✅ Key部分格式正确")
                else:
                    print(f"  ❌ Key部分格式错误，应包含 'Chromosome' 和数字")
                    
            else:
                print(f"  ❌ 缺少key-value分隔符 ':http'，可能只获取到染色体号")
            
        except Exception as e:
            print(f"  错误: {e}")
    
    print("\n" + "="*80)
    print("测试完成")
    print("="*80)

if __name__ == "__main__":
    test_chromosome_link() 