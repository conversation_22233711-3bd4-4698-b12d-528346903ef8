#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 导入批量处理脚本的所有功能
from batch_transcript_scraper import *
import pandas as pd

def main():
    """测试主函数 - 只处理前10个基因"""
    # 配置文件路径
    csv_file = "gene_ids/ensembl_gene_id_unique.csv"
    output_file = "test_transcript_data_results.json"
    
    # 检查输入文件
    if not os.path.exists(csv_file):
        logging.error(f"基因ID文件不存在: {csv_file}")
        return
    
    # 加载基因ID
    gene_ids = load_gene_ids_from_csv(csv_file)
    if not gene_ids:
        logging.error("未能加载基因ID")
        return
    
    # 只处理前10个基因进行测试
    test_gene_ids = gene_ids[:10]
    
    logging.info(f"开始测试批量爬取 {len(test_gene_ids)} 个基因的转录本数据")
    logging.info(f"测试基因列表: {test_gene_ids}")
    
    # 批量处理（使用较小的批次大小）
    results = batch_process_genes(test_gene_ids, max_workers=2, batch_size=5)
    
    # 保存测试结果
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logging.info(f"测试结果已保存到: {output_file}")
    except Exception as e:
        logging.error(f"保存测试结果失败: {e}")
    
    # 统计信息
    total_genes = len(results)
    successful_genes = len([r for r in results.values() if r.get('transcript_count', 0) > 0])
    total_transcripts = sum(r.get('transcript_count', 0) for r in results.values())
    failed_genes = [gid for gid, data in results.items() if data.get('transcript_count', 0) == 0]
    
    logging.info("\n=== 测试统计 ===")
    logging.info(f"测试基因数: {total_genes}")
    logging.info(f"成功爬取的基因数: {successful_genes}")
    logging.info(f"失败的基因数: {len(failed_genes)}")
    logging.info(f"总转录本数: {total_transcripts}")
    logging.info(f"成功率: {(successful_genes/total_genes*100):.1f}%")
    
    # 显示详细结果
    logging.info("\n=== 详细结果 ===")
    for gene_id, data in results.items():
        if data.get('transcript_count', 0) > 0:
            logging.info(f"{gene_id}: {data['transcript_count']} 个转录本")
            for transcript in data['transcripts'][:2]:  # 显示前2个
                logging.info(f"  - {transcript['transcript_id']}: {transcript['name']}")
            if len(data['transcripts']) > 2:
                logging.info(f"  ... 还有 {len(data['transcripts'])-2} 个转录本")
        else:
            error_msg = data.get('error', 'Unknown error')
            logging.warning(f"{gene_id}: 失败 ({error_msg})")

if __name__ == "__main__":
    main() 