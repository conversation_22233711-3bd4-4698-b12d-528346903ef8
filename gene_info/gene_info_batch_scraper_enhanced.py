import requests
from bs4 import Beautiful<PERSON>oup
import pandas as pd
import time
import os
import json
import random
from tqdm import tqdm
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import concurrent.futures
import logging
import threading

# Set up logging to track progress and errors
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("gene_scraper.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Thread-local storage for driver instances
thread_local = threading.local()

def get_driver():
    """
    Get or create a thread-local WebDriver instance
    
    Returns:
        A WebDriver instance specific to the current thread
    """
    if not hasattr(thread_local, "driver"):
        thread_local.driver = setup_selenium_driver(headless=True)
    return thread_local.driver

def setup_selenium_driver(headless=True):
    """
    Setup and return a configured Selenium WebDriver
    
    Args:
        headless: Whether to run the browser in headless mode
    
    Returns:
        WebDriver instance
    """
    options = webdriver.ChromeOptions()
    if headless:
        options.add_argument('--headless=new')  # Using newer headless mode
    
    # Basic stability options
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1280,720')
    
    # Additional stability options
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-infobars')
    options.add_argument('--disable-notifications')
    
    # Add user agent to look like a real browser
    options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36")
    
    try:
        driver = webdriver.Chrome(options=options)
        driver.set_page_load_timeout(30)  # Set timeout to avoid hanging
        driver.set_script_timeout(30)
        return driver
    except Exception as e:
        logger.error(f"Failed to create WebDriver: {e}")
        raise

def fetch_gene_info_worker(gene_id, max_retries=3, retry_delay=5):
    """
    Worker function to fetch gene information in a separate thread
    
    Args:
        gene_id: The gene ID to process
        max_retries: Number of retry attempts if errors occur
        retry_delay: Delay between retries in seconds
        
    Returns:
        Dictionary containing the gene information
    """
    # Get the thread-local driver
    try:
        driver = get_driver()
    except Exception as e:
        logger.error(f"Error creating driver for {gene_id}: {e}")
        return {"gene_id": gene_id, "Status": f"Driver creation error: {str(e)}"}
    
    # Now use the driver to fetch gene info
    for attempt in range(max_retries):
        try:
            result = fetch_gene_info_selenium(gene_id, driver, max_retries=1)
            return result
        except Exception as e:
            logger.warning(f"{gene_id}: Error on attempt {attempt+1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                # Sleep with exponential backoff
                sleep_time = retry_delay * (2 ** attempt)
                logger.info(f"{gene_id}: Retrying after {sleep_time} seconds...")
                time.sleep(sleep_time)
            else:
                # Final attempt failed, return error
                return {"gene_id": gene_id, "Status": f"Error after {max_retries} retries: {str(e)}"}

def fetch_gene_info_selenium(gene_id, driver, max_retries=3, retry_delay=5):
    """
    Fetch gene information from NCBI Gene database using Selenium
    
    Args:
        gene_id: The gene ID (e.g., ENSG00000092607)
        driver: Selenium WebDriver instance
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds
        
    Returns:
        Dictionary containing the gene information
    """
    search_url = f"https://www.ncbi.nlm.nih.gov/gene/?term={gene_id}"
    gene_info = {'gene_id': gene_id}
    
    for attempt in range(max_retries):
        try:
            # Navigate to the search page
            logger.info(f"{gene_id}: Navigating to search page")
            driver.get(search_url)
            
            # Wait for page to load
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            time.sleep(random.uniform(1.0, 2.0))  # Add a small random delay
            
            # Check current URL to determine page state
            current_url = driver.current_url
            
            # Case 1: Directly on gene page (URL contains /gene/ followed by numbers)
            if "/gene/" in current_url and current_url.split('/gene/')[1].split('?')[0].isdigit():
                logger.info(f"{gene_id}: Directly navigated to gene page {current_url}")
                # Extract NCBI Gene ID from URL
                try:
                    ncbi_gene_id = current_url.split('/gene/')[1].split('?')[0]
                    if ncbi_gene_id.isdigit():
                        gene_info['ncbi_gene_id'] = ncbi_gene_id
                except IndexError:
                    pass  # Continue with extraction anyway
            
            # Case 2: On search results page
            elif "?term=" in current_url:
                try:
                    # Wait for either results table or "No items found" message
                    WebDriverWait(driver, 15).until(
                        EC.any_of(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "table.gene-tabular-rprt, table#gene_all")),
                            EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'No items found')]"))
                        )
                    )
                    
                    # Check if "No items found" is present
                    no_items = driver.find_elements(By.XPATH, "//*[contains(text(), 'No items found')]")
                    if no_items:
                        logger.info(f"{gene_id}: No items found in NCBI database")
                        return {"gene_id": gene_id, "Status": "Gene ID not found in NCBI database"}
                    
                    # Look for gene links in the search results
                    gene_links = driver.find_elements(By.CSS_SELECTOR, "tr.rprt td.gene-name-id a")
                    
                    if gene_links:
                        # Store NCBI gene ID before clicking
                        link_href = gene_links[0].get_attribute("href")
                        if link_href and "/gene/" in link_href:
                            ncbi_gene_id = link_href.split("/gene/")[-1].split("?")[0]
                            gene_info['ncbi_gene_id'] = ncbi_gene_id
                            logger.info(f"{gene_id}: Found result: {gene_links[0].text} (NCBI ID: {ncbi_gene_id})")
                            
                            # Try to click the link (with JavaScript fallback)
                            try:
                                # Scroll element into view first
                                driver.execute_script("arguments[0].scrollIntoView(true);", gene_links[0])
                                time.sleep(0.5)  # Brief pause after scrolling
                                gene_links[0].click()
                            except Exception as click_error:
                                logger.warning(f"{gene_id}: Direct click failed: {click_error}")
                                # Try JavaScript click instead
                                driver.execute_script("arguments[0].click();", gene_links[0])
                            
                            # Wait for gene page to load
                            WebDriverWait(driver, 15).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, "div.rprt-section.gene-summary"))
                            )
                        else:
                            logger.warning(f"{gene_id}: Found link but couldn't parse NCBI ID")
                            return {"gene_id": gene_id, "Status": "Invalid gene link format"}
                    else:
                        logger.warning(f"{gene_id}: No gene links found in search results")
                        return {"gene_id": gene_id, "Status": "No gene links found in search results"}
                
                except TimeoutException:
                    logger.warning(f"{gene_id}: Timeout waiting for search results")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    return {"gene_id": gene_id, "Status": "Timeout waiting for search results"}
            
            # Extract gene information from the page
            try:
                # Wait for the summary section
                WebDriverWait(driver, 15).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div.rprt-section.gene-summary"))
                )
                
                # Parse the page with BeautifulSoup
                soup = BeautifulSoup(driver.page_source, 'html.parser')
                
                # Extract information from summary section
                summary_div = soup.find('div', class_='rprt-section gene-summary')
                if summary_div:
                    dl = summary_div.find('dl', id='summaryDl')
                    if dl:
                        # Find all dt and dd elements
                        dt_elements = dl.find_all('dt')
                        dd_elements = dl.find_all('dd')
                        
                        # Process each dt-dd pair
                        for i in range(len(dt_elements)):
                            if i < len(dd_elements):
                                # Get the key (normalized)
                                key = ' '.join(dt_elements[i].get_text(strip=True).split())
                                
                                # Skip non-relevant fields
                                if key == 'Expression' or key == 'Orthologs' or 'NEW' in key:
                                    continue
                                
                                # Get the value
                                dd = dd_elements[i]
                                
                                # Handle special cases for fields
                                if "See related" in key:
                                    links = dd.find_all('a')
                                    link_texts = [link.get_text(strip=True) for link in links]
                                    gene_info[key] = "; ".join(link_texts)
                                elif "Primary source" in key:
                                    link = dd.find('a')
                                    if link:
                                        gene_info[key] = link.get_text(strip=True)
                                    else:
                                        gene_info[key] = dd.get_text(strip=True)
                                # Handle gene symbols and names
                                elif any(field in key for field in ["Official Symbol", "Gene symbol"]):
                                    text = dd.get_text(strip=True)
                                    if "provided by" in text:
                                        gene_info[key] = text.split("provided by")[0].strip()
                                    else:
                                        gene_info[key] = text
                                elif any(field in key for field in ["Official Full Name", "Gene description"]):
                                    text = dd.get_text(strip=True)
                                    if "provided by" in text:
                                        gene_info[key] = text.split("provided by")[0].strip()
                                    else:
                                        gene_info[key] = text
                                else:
                                    gene_info[key] = dd.get_text(strip=True)
                                
                                # Stop after processing Summary
                                if "Summary" in key:
                                    break
                
                # Extract chromosome location
                chr_info = soup.find('dl', class_='dl-chr-info')
                if chr_info:
                    location_dt = chr_info.find('dt', string=lambda text: text and "Location" in text)
                    if location_dt:
                        location_dd = location_dt.find_next_sibling('dd')
                        if location_dd:
                            gene_info["Location"] = location_dd.get_text(strip=True)
                
                # Check if we have meaningful information
                if len(gene_info) > 1:
                    return gene_info
                else:
                    logger.warning(f"{gene_id}: No gene information found on the page")
                    return {"gene_id": gene_id, "Status": "No information found on gene page"}
            
            except TimeoutException:
                logger.warning(f"{gene_id}: Timeout waiting for gene details")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                return {"gene_id": gene_id, "Status": "Timeout waiting for gene details"}
        
        except Exception as e:
            logger.error(f"{gene_id}: Exception: {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            return {"gene_id": gene_id, "Status": f"Error: {str(e)}"}
    
    # If we've exhausted all retries
    return {"gene_id": gene_id, "Status": "Failed after retries"}

def process_gene_batch_selenium_threaded(gene_ids, output_file, num_threads=4, batch_size=50, checkpoint_interval=10):
    """
    Process gene IDs in parallel using multiple threads
    
    Args:
        gene_ids: List of gene IDs to process
        output_file: Path to the output CSV/JSON file
        num_threads: Number of concurrent threads to use
        batch_size: Number of genes to process in a logical batch
        checkpoint_interval: Number of genes to process before saving progress
    """
    results = []
    checkpoint_file = f"{output_file}.checkpoint"
    output_format = 'json' if output_file.endswith('.json') else 'csv'
    
    # Check if checkpoint exists and load it
    processed_ids = set()
    if os.path.exists(checkpoint_file):
        if checkpoint_file.endswith('.json.checkpoint'):
            with open(checkpoint_file, 'r') as f:
                checkpoint_data = json.load(f)
                results = checkpoint_data
                processed_ids = set(item['gene_id'] for item in checkpoint_data)
        else:
            checkpoint_df = pd.read_csv(checkpoint_file)
            results = checkpoint_df.to_dict('records')
            processed_ids = set(checkpoint_df['gene_id'].tolist())
        logger.info(f"Loaded {len(processed_ids)} already processed genes from checkpoint")
    
    # Filter out already processed IDs
    remaining_ids = [gene_id for gene_id in gene_ids if gene_id not in processed_ids]
    logger.info(f"Processing {len(remaining_ids)} remaining genes using {num_threads} threads")
    
    if not remaining_ids:
        logger.info("No genes left to process!")
        return
    
    # Process genes in parallel with controlled concurrency
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        # Create a dictionary mapping futures to gene_ids for tracking
        future_to_gene = {executor.submit(fetch_gene_info_worker, gene_id): gene_id 
                         for gene_id in remaining_ids}
        
        # Process results as they complete
        processed_count = 0
        result_lock = threading.Lock()  # Lock for thread-safe access to results list
        
        for future in tqdm(concurrent.futures.as_completed(future_to_gene), 
                          total=len(remaining_ids), 
                          desc="Fetching gene information"):
            gene_id = future_to_gene[future]
            try:
                gene_info = future.result()
                
                # Thread-safe update of results
                with result_lock:
                    results.append(gene_info)
                    processed_count += 1
                    
                    # Save checkpoint at intervals
                    if processed_count % checkpoint_interval == 0:
                        logger.info(f"Processed {processed_count}/{len(remaining_ids)}. Saving checkpoint...")
                        if output_format == 'json':
                            with open(checkpoint_file, 'w') as f:
                                json.dump(results, f, indent=2)
                        else:
                            temp_df = pd.DataFrame(results)
                            temp_df.to_csv(checkpoint_file, index=False)
                        logger.info(f"Checkpoint saved ({len(results)} total genes processed)")
            
            except Exception as e:
                logger.error(f"Error processing gene {gene_id}: {str(e)}")
                with result_lock:
                    results.append({"gene_id": gene_id, "Status": f"Processing error: {str(e)}"})
    
    # Clean up thread-local drivers
    for thread in threading.enumerate():
        if hasattr(thread_local, "driver"):
            try:
                thread_local.driver.quit()
            except:
                pass
    
    # Save the final results
    if results:
        if output_format == 'json':
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2)
        else:
            df = pd.DataFrame(results)
            df.to_csv(output_file, index=False)
        logger.info(f"Data saved to {output_file}")
        
        # Remove checkpoint file if successful
        if os.path.exists(checkpoint_file):
            os.remove(checkpoint_file)
    else:
        logger.warning("No results to save.")

def process_missing_gene_file_selenium(input_file, output_file, id_column='gene_id', num_threads=4, batch_size=50, checkpoint_interval=10):
    """
    Process a CSV file containing missing gene IDs using Selenium with multi-threading
    
    Args:
        input_file: Path to the input CSV file with missing gene IDs
        output_file: Path to the output file (CSV or JSON)
        id_column: Name of the column containing gene IDs
        num_threads: Number of concurrent threads to use
        batch_size: Number of genes to process in a logical batch
        checkpoint_interval: Number of genes to process before saving progress
    """
    try:
        df = pd.read_csv(input_file)
        if id_column not in df.columns:
            logger.error(f"ID column '{id_column}' not found in {input_file}")
            return
        
        gene_ids = df[id_column].dropna().astype(str).tolist()
        gene_ids = [gid for gid in gene_ids if gid.strip()]  # Remove empty strings
        
        if not gene_ids:
            logger.error(f"No valid gene IDs found in column '{id_column}' of {input_file}")
            return
        
        logger.info(f"Loaded {len(gene_ids)} gene IDs from {input_file}")
        process_gene_batch_selenium_threaded(
            gene_ids,
            output_file,
            num_threads=num_threads,
            batch_size=batch_size,
            checkpoint_interval=checkpoint_interval
        )
    
    except FileNotFoundError:
        logger.error(f"Input file not found at {input_file}")
    except Exception as e:
        logger.error(f"Error processing input file: {e}")

def test_fetch_gene_selenium(gene_id):
    """
    Test the fetch_gene_info_selenium function with a single gene ID
    
    Args:
        gene_id: The gene ID to test with
    """
    logger.info(f"Testing with gene ID: {gene_id}")
    driver = None
    try:
        driver = setup_selenium_driver(headless=True)
        gene_info = fetch_gene_info_selenium(gene_id, driver)
        logger.info(f"Test result: {json.dumps(gene_info, indent=2)}")
        return gene_info
    finally:
        if driver:
            driver.quit()

# Main execution block
if __name__ == "__main__":
    # Test with a single gene if needed
    # test_gene_id = "ENSG00000092607"  # TBX15 gene
    # test_fetch_gene_selenium(test_gene_id)
    
    # Configuration for processing the missing genes file
    input_file = "gene_info_results_missing.csv"
    output_file = "gene_info_results_recovered_2.json"
    id_column = 'gene_id'
    num_threads = 5  # Adjust based on your system capabilities
    batch_size = 50
    checkpoint_interval = 10
    
    logger.info(f"\nStarting multi-threaded processing of missing gene IDs")
    logger.info(f"Input file: {input_file}")
    logger.info(f"Output file: {output_file}")
    logger.info(f"Using {num_threads} threads")
    
    start_time = time.time()
    try:
        process_missing_gene_file_selenium(
            input_file, 
            output_file,
            id_column=id_column,
            num_threads=num_threads,
            batch_size=batch_size,
            checkpoint_interval=checkpoint_interval
        )
    except KeyboardInterrupt:
        logger.warning("\nProcess interrupted by user. Progress has been saved in the checkpoint file.")
    except Exception as e:
        logger.error(f"\nError: {str(e)}")
        logger.error("Progress has been saved in the checkpoint file.")
    finally:
        end_time = time.time()
        logger.info(f"Processing finished in {end_time - start_time:.2f} seconds")