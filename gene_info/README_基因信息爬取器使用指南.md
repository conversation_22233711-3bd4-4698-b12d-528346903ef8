# Ensembl基因信息爬取器使用指南

## 功能概述

本爬取器可以从Ensembl数据库自动获取基因的以下4个字段信息：
- **GENE symbol** (基因符号) 
- **Approved Name** (批准名称/描述)
- **Locus Type** (基因类型)
- **Chromosome** (染色体 - key:value格式，包含名称和完整链接)

## 性能表现

### 测试结果
- ✅ **成功率**: 100% (所有4个字段)
- ⚡ **处理速度**: 500-1000基因/小时
- 🔧 **技术特性**: 多线程、错误重试、断点续传

### 最新优化
2025年1月18日修复了HTML解析问题，现在所有4个字段都能稳定获取：
- 修复了面板ID查找问题
- 优化了数据提取逻辑
- 改进了错误处理机制

## 快速开始

### 1. 基本使用
```bash
# 使用默认配置处理文件
python run_gene_info_scraper.py --preset default

# 指定输入输出文件
python run_gene_info_scraper.py --input my_genes.csv --output results.csv
```

### 2. 预设配置选择

| 配置 | 线程数 | 超时 | 延迟 | 适用场景 |
|------|--------|------|------|----------|
| `test` | 2 | 10s | 1.0s | 🧪 小规模测试 |
| `default` | 4 | 12s | 0.8s | 📊 常规使用 |
| `conservative` | 2 | 20s | 1.5s | 🐌 网络不稳定 |
| `aggressive` | 6 | 15s | 0.5s | 🚀 网络良好 |
| `fast` | 8 | 8s | 0.3s | ⚡ 高速网络 |

### 3. 输入文件格式
CSV文件，包含一个名为`ensembl_gene_id`的列：
```csv
ensembl_gene_id
ENSG00000003056
ENSG00000004478
ENSG00000173153
```

### 4. 输出文件格式
包含原有列和新增的4个信息列：
```csv
ensembl_gene_id,GENE symbol,Approved Name,Locus Type,Chromosome
ENSG00000003056,M6PR,"mannose-6-phosphate receptor, cation dependent",Protein coding,"Chromosome 12: 8,940,361-8,949,761:http://www.ensembl.org/Homo_sapiens/Location/View?db=core;g=ENSG00000003056;r=12:8940361-8949761"
ENSG00000173153,ESRRA,"estrogen related receptor alpha",Protein coding,"Chromosome 11: 64,305,497-64,316,743:http://www.ensembl.org/Homo_sapiens/Location/View?db=core;g=ENSG00000173153;r=11:64305497-64316743"
```

**染色体字段格式说明:**
- 格式: `名称:链接`
- 名称部分: 如 `Chromosome 11: 64,305,497-64,316,743`
- 链接部分: 完整的Ensembl位置查看URL

## 核心文件说明

### 主要脚本
- `ensembl_gene_info_scraper.py` - 核心爬取器类
- `run_gene_info_scraper.py` - 启动脚本（推荐使用）

### 测试脚本  
- `test_gene_info_scraper.py` - 基本功能测试
- `test_improved_scraper.py` - 改进版测试
- `final_comprehensive_test.py` - 综合性能测试

### 调试工具
- `debug_detailed_html.py` - HTML结构分析

## 使用示例

### 测试运行
```bash
# 小规模测试（推荐先运行）
python final_comprehensive_test.py

# 快速功能验证
python test_improved_scraper.py
```

### 生产环境
```bash
# 处理完整数据集
python run_gene_info_scraper.py --preset default --input ../ensembl_gene_id_unique.csv

# 网络不稳定环境
python run_gene_info_scraper.py --preset conservative

# 高速网络环境
python run_gene_info_scraper.py --preset fast
```

### 断点续传
```bash
# 从第1000行开始继续处理
python run_gene_info_scraper.py --start-from 1000
```

## 技术特性

### 多线程处理
- 使用ThreadPoolExecutor实现并发爬取
- 可配置线程数量（2-8个线程）
- 线程安全的进度跟踪

### 错误处理
- 自动网络重试机制
- 详细的错误日志记录
- 优雅的异常处理

### 性能优化
- HTTP Session复用
- 可配置请求延迟
- 批量处理和检查点

### 数据质量
- 多种HTML解析策略
- 数据验证和清理
- 完整性检查

## 常见问题

### Q: 如何选择合适的配置？
A: 建议先用`test`配置测试，确认正常后：
- 网络稳定：使用`default`或`fast`
- 网络不稳定：使用`conservative`
- 高性能需求：使用`aggressive`

### Q: 处理大文件时如何避免中断？
A: 
1. 使用`conservative`配置提高稳定性
2. 利用`--start-from`参数实现断点续传
3. 定期检查日志文件

### Q: 成功率不理想怎么办？
A:
1. 检查网络连接
2. 增加延迟时间
3. 减少线程数
4. 使用`conservative`配置

## 更新日志

### v2.1 (2025-01-18)
- ✅ 修改染色体字段格式为key-value形式 (名称:链接)
- ✅ 染色体字段现在包含完整的Ensembl位置查看URL
- ✅ 保持其他字段100%成功率不变

### v2.0 (2025-01-18)
- ✅ 修复HTML解析问题，4字段成功率达到100%
- ✅ 优化了数据提取逻辑
- ✅ 改进了错误处理和日志记录

### v1.0 (初始版本)
- ✅ 基本静态爬取功能
- ✅ 多线程支持
- ✅ 基因符号提取（95%+成功率）

---

🔗 **相关项目**: [转录本信息爬取器](../batch_transcript_scraper_optimized.py) 