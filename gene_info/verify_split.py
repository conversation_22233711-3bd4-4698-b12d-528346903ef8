import pandas as pd

df = pd.read_csv('ensembl_gene_id_with_full_info_split.csv')
print('='*60)
print('Chromosome列拆分结果验证')
print('='*60)
print(f'总行数: {len(df):,}')
print(f'列名: {list(df.columns)}')
print('\n前3行详细信息:')
for i in range(3):
    row = df.iloc[i]
    print(f'\n基因ID: {row["ensembl_gene_id"]}')
    print(f'基因符号: {row["GENE symbol"]}')
    print(f'染色体信息: {row["Chromosome"]}')
    print(f'染色体URL: {row["Chromosome_URL"]}')
    print('-' * 40)

# 统计有URL的行数
has_url = df[df['Chromosome_URL'] != '']
print(f'\n统计信息:')
print(f'有URL的行数: {len(has_url):,}')
print(f'URL完整率: {len(has_url)/len(df)*100:.2f}%') 