#!/usr/bin/env python3
"""
更新翻译指数文件中的基因信息

通过transcript_id在unique_transcript_gene_mapping_with_location_updated.csv中查找匹配的信息，
更新translation_indices_results_grouped_filtered_processed_final.csv文件中的"GENE ID"和"GENE symbol"列。
"""

import csv
import logging
from datetime import datetime
import os

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_translation_indices_gene_info.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def load_transcript_gene_mapping():
    """加载transcript到基因信息的映射"""
    try:
        logging.info("正在加载transcriptinfo/unique_transcript_gene_mapping_with_location_updated.csv...")
        transcript_to_gene = {}
        
        with open('transcriptinfo/unique_transcript_gene_mapping_with_location_updated.csv', 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                transcript_id = row.get('transcript_id', '').strip()
                gene_id = row.get('GENE ID', '').strip()
                gene_symbol = row.get('GENE symbol', '').strip()
                
                if transcript_id and gene_id and gene_symbol:
                    transcript_to_gene[transcript_id] = {
                        'gene_id': gene_id,
                        'gene_symbol': gene_symbol
                    }
        
        logging.info(f"成功加载{len(transcript_to_gene)}个转录本到基因的映射")
        return transcript_to_gene
        
    except Exception as e:
        logging.error(f"加载转录本基因映射时出错: {str(e)}")
        return {}

def update_translation_indices_file(transcript_to_gene):
    """更新翻译指数文件中的基因信息"""
    try:
        input_file = 'translation_indices_results_grouped_filtered_processed_final.csv'
        output_file = 'translation_indices_results_grouped_filtered_processed_final_updated.csv'
        
        logging.info(f"正在处理{input_file}...")
        
        total_rows = 0
        updated_gene_id_count = 0
        updated_gene_symbol_count = 0
        not_found_count = 0
        
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8', newline='') as outfile:
            
            reader = csv.DictReader(infile)
            fieldnames = reader.fieldnames
            writer = csv.DictWriter(outfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for row in reader:
                total_rows += 1
                
                transcript_id = row.get('transcript_id', '').strip()
                current_gene_id = row.get('GENE ID', '').strip()
                current_gene_symbol = row.get('GENE symbol', '').strip()
                
                # 在映射中查找transcript_id
                if transcript_id in transcript_to_gene:
                    mapping_info = transcript_to_gene[transcript_id]
                    new_gene_id = mapping_info['gene_id']
                    new_gene_symbol = mapping_info['gene_symbol']
                    
                    # 检查是否需要更新GENE ID
                    if current_gene_id != new_gene_id:
                        row['GENE ID'] = new_gene_id
                        updated_gene_id_count += 1
                    
                    # 检查是否需要更新GENE symbol
                    if current_gene_symbol != new_gene_symbol:
                        row['GENE symbol'] = new_gene_symbol
                        updated_gene_symbol_count += 1
                        
                else:
                    not_found_count += 1
                    if not_found_count <= 10:  # 只记录前10个未找到的
                        logging.warning(f"未找到transcript_id: {transcript_id}")
                
                writer.writerow(row)
                
                # 每处理10000行显示进度
                if total_rows % 10000 == 0:
                    logging.info(f"已处理 {total_rows} 行...")
        
        # 统计结果
        logging.info("=" * 50)
        logging.info("更新完成！统计结果：")
        logging.info(f"总处理行数: {total_rows}")
        logging.info(f"更新GENE ID的行数: {updated_gene_id_count}")
        logging.info(f"更新GENE symbol的行数: {updated_gene_symbol_count}")
        logging.info(f"未找到匹配的transcript_id数量: {not_found_count}")
        logging.info(f"匹配成功率: {((total_rows - not_found_count) / total_rows * 100):.2f}%")
        logging.info(f"输出文件: {output_file}")
        logging.info("=" * 50)
        
        return True
        
    except Exception as e:
        logging.error(f"更新文件时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logging.info("开始更新翻译指数文件中的基因信息...")
    start_time = datetime.now()
    
    # 加载转录本到基因的映射
    transcript_to_gene = load_transcript_gene_mapping()
    
    if not transcript_to_gene:
        logging.error("无法加载转录本基因映射，程序退出")
        return
    
    # 更新翻译指数文件
    success = update_translation_indices_file(transcript_to_gene)
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    if success:
        logging.info(f"程序执行成功！总耗时: {duration}")
    else:
        logging.error(f"程序执行失败！总耗时: {duration}")

if __name__ == "__main__":
    main() 