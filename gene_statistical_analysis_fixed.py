#!/usr/bin/env python3
"""
基因翻译效率统计分析脚本 - 修复版本
避免numpy递归错误，使用纯Python实现
"""

import csv
import sys
from collections import defaultdict
import math

def load_csv_data(file_path):
    """使用纯Python加载CSV数据"""
    print("正在加载数据...")
    try:
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            print(f"列名: {headers}")
            
            for i, row in enumerate(reader):
                data.append(row)
                if i < 5:  # 显示前5行
                    print(f"行 {i+1}: {row}")
                if i == 0:
                    print("...")
        
        print(f"数据加载成功！共 {len(data)} 行")
        return data, headers
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None, None

def preprocess_data(data, headers):
    """数据预处理"""
    print("\n开始数据预处理...")
    
    # 检查必要的列
    required_cols = ['geneSymbol', 'geneId', 'disease']
    missing_cols = [col for col in required_cols if col not in headers]
    if missing_cols:
        print(f"缺少必要的列: {missing_cols}")
        return None
    
    # 检查分析列
    analysis_cols = ['TE', 'TR', 'EVI']
    available_cols = [col for col in analysis_cols if col in headers]
    print(f"可用于分析的变量列: {available_cols}")
    
    if not available_cols:
        print("未找到TE、TR、EVI列，请检查数据格式")
        return None
    
    # 统计疾病类型
    diseases = set()
    for row in data:
        diseases.add(row['disease'])
    diseases = list(diseases)
    print(f"疾病类型: {diseases}")
    
    if 'Normal' not in diseases:
        print("警告: 未找到'Normal'对照组")
    
    # 按基因分组
    gene_groups = defaultdict(list)
    for row in data:
        key = (row['geneSymbol'], row['geneId'])
        gene_groups[key].append(row)
    
    print(f"共有 {len(gene_groups)} 个基因组合")
    
    return gene_groups, available_cols, diseases

def safe_float_convert(value):
    """安全的浮点数转换"""
    if value is None or value == '' or value == 'nan' or value == 'NaN':
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def calculate_mean(values):
    """计算均值"""
    valid_values = [v for v in values if v is not None]
    if not valid_values:
        return None
    return sum(valid_values) / len(valid_values)

def calculate_variance(values):
    """计算方差"""
    valid_values = [v for v in values if v is not None]
    if len(valid_values) < 2:
        return None
    
    mean = calculate_mean(valid_values)
    if mean is None:
        return None
    
    variance = sum((x - mean) ** 2 for x in valid_values) / (len(valid_values) - 1)
    return variance

def t_test_independent(group1, group2):
    """独立样本t检验"""
    n1, n2 = len(group1), len(group2)
    if n1 < 2 or n2 < 2:
        return None, None
    
    mean1 = calculate_mean(group1)
    mean2 = calculate_mean(group2)
    var1 = calculate_variance(group1)
    var2 = calculate_variance(group2)
    
    if mean1 is None or mean2 is None or var1 is None or var2 is None:
        return None, None
    
    # 合并方差
    pooled_var = ((n1 - 1) * var1 + (n2 - 1) * var2) / (n1 + n2 - 2)
    
    if pooled_var <= 0:
        return None, None
    
    # t统计量
    t_stat = (mean1 - mean2) / math.sqrt(pooled_var * (1/n1 + 1/n2))
    
    # 自由度
    df = n1 + n2 - 2
    
    # 简化的p值估计（双尾检验）
    # 这里使用近似方法，对于大样本比较准确
    abs_t = abs(t_stat)
    if abs_t > 3:
        p_value = 0.001
    elif abs_t > 2.5:
        p_value = 0.01
    elif abs_t > 2:
        p_value = 0.05
    elif abs_t > 1.5:
        p_value = 0.1
    else:
        p_value = 0.2
    
    return t_stat, p_value

def perform_statistical_tests(disease_data, normal_data, gene_id, gene_symbol, disease, variable):
    """执行统计检验"""
    results = {
        'geneId': gene_id,
        'geneSymbol': gene_symbol,
        'disease_category': disease,
        'variable': variable,
        'p_t': None,
        'direction': 'unknown',
        'disease_mean': None,
        'normal_mean': None,
        'disease_n': 0,
        'normal_n': 0
    }
    
    # 转换数据
    disease_values = [safe_float_convert(row[variable]) for row in disease_data]
    normal_values = [safe_float_convert(row[variable]) for row in normal_data]
    
    # 移除None值
    disease_clean = [v for v in disease_values if v is not None]
    normal_clean = [v for v in normal_values if v is not None]
    
    results['disease_n'] = len(disease_clean)
    results['normal_n'] = len(normal_clean)
    
    if len(disease_clean) < 2 or len(normal_clean) < 2:
        return results
    
    # 计算均值
    disease_mean = calculate_mean(disease_clean)
    normal_mean = calculate_mean(normal_clean)
    
    results['disease_mean'] = disease_mean
    results['normal_mean'] = normal_mean
    
    if disease_mean is not None and normal_mean is not None:
        if disease_mean > normal_mean:
            results['direction'] = 'higher'
        else:
            results['direction'] = 'lower'
    
    # 执行t检验
    try:
        t_stat, p_value = t_test_independent(disease_clean, normal_clean)
        if p_value is not None:
            results['p_t'] = p_value
    except Exception as e:
        print(f"统计检验失败 - 基因: {gene_symbol}, 疾病: {disease}, 变量: {variable}, 错误: {e}")
    
    return results

def analyze_gene_data(gene_groups, available_cols, diseases):
    """分析基因数据"""
    print("\n开始统计分析...")
    
    all_results = []
    test_diseases = [d for d in diseases if d != 'Normal']
    
    if 'Normal' not in diseases:
        print("错误: 未找到Normal对照组")
        return None
    
    print(f"对照组: Normal")
    print(f"测试疾病组: {test_diseases}")
    
    total_genes = len(gene_groups)
    
    for i, ((gene_symbol, gene_id), gene_data) in enumerate(gene_groups.items()):
        if i % 100 == 0:
            print(f"处理进度: {i}/{total_genes} 基因")
        
        # 获取Normal组数据
        normal_data = [row for row in gene_data if row['disease'] == 'Normal']
        
        if len(normal_data) == 0:
            continue
        
        # 对每个变量类型进行分析
        for variable in available_cols:
            # 检查Normal组该变量是否有有效值
            normal_values = [safe_float_convert(row[variable]) for row in normal_data]
            normal_valid = [v for v in normal_values if v is not None]
            
            if len(normal_valid) < 2:
                continue
            
            # 对每个疾病组进行比较
            for disease in test_diseases:
                disease_data = [row for row in gene_data if row['disease'] == disease]
                
                if len(disease_data) == 0:
                    continue
                
                # 检查疾病组该变量是否有有效值
                disease_values = [safe_float_convert(row[variable]) for row in disease_data]
                disease_valid = [v for v in disease_values if v is not None]
                
                if len(disease_valid) < 2:
                    continue
                
                # 执行统计检验
                result = perform_statistical_tests(
                    disease_data, normal_data, 
                    gene_id, gene_symbol, disease, variable
                )
                
                all_results.append(result)
    
    print(f"完成分析，共生成 {len(all_results)} 个结果")
    return all_results

def save_results(results, output_file):
    """保存结果"""
    print(f"\n保存结果到: {output_file}")
    
    if not results:
        print("没有结果可保存")
        return
    
    # 定义列顺序
    columns = ['geneId', 'geneSymbol', 'disease_category', 'variable', 
               'p_t', 'direction', 'disease_mean', 'normal_mean', 
               'disease_n', 'normal_n']
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=columns)
            writer.writeheader()
            
            for result in results:
                # 确保所有列都存在
                row = {}
                for col in columns:
                    row[col] = result.get(col, '')
                writer.writerow(row)
        
        print("结果保存完成！")
        print(f"结果文件包含 {len(results)} 行数据")
        
        # 显示结果摘要
        genes = set(r['geneSymbol'] for r in results)
        diseases = set(r['disease_category'] for r in results)
        variables = set(r['variable'] for r in results)
        
        print(f"\n结果摘要:")
        print(f"分析的基因数量: {len(genes)}")
        print(f"分析的疾病类型: {len(diseases)}")
        print(f"分析的变量类型: {len(variables)}")
        
        # 显著性结果统计
        significant = sum(1 for r in results if r.get('p_t') is not None and r['p_t'] < 0.05)
        total = sum(1 for r in results if r.get('p_t') is not None)
        print(f"显著性结果 (p<0.05): {significant}/{total}")
        
    except Exception as e:
        print(f"保存结果失败: {e}")

def main():
    """主函数"""
    input_file = "gene/gene_with_translation_indices.csv"
    output_file = "gene_statistical_analysis_results.csv"
    
    print("=== 基因翻译效率统计分析 (修复版本) ===")
    
    # 1. 加载数据
    data, headers = load_csv_data(input_file)
    if data is None:
        return
    
    # 2. 数据预处理
    preprocessing_result = preprocess_data(data, headers)
    if preprocessing_result is None:
        return
    
    gene_groups, available_cols, diseases = preprocessing_result
    
    # 3. 执行统计分析
    results = analyze_gene_data(gene_groups, available_cols, diseases)
    if results is None or len(results) == 0:
        print("未生成任何分析结果")
        return
    
    # 4. 保存结果
    save_results(results, output_file)
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
