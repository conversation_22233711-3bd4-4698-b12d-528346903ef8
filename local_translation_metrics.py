import os
import csv
import pandas as pd
from collections import defaultdict

def calculate_local_translation_metrics(transcript_id="ENST00000673477", project_id="GSE56924", 
                                        gse_match_file="GSE_match.csv", tpm_dir="demo_TPM_csv"):
    """
    从本地文件计算转录本的翻译指标
    
    Args:
        transcript_id: 要分析的转录本ID
        project_id: 项目ID
        gse_match_file: GSE匹配文件路径
        tpm_dir: TPM文件目录
    
    Returns:
        指标计算结果字典
    """
    # 第1步：从GSE_match.csv获取SRA Accession和Strategy
    strategy_sra_dict = defaultdict(list)
    
    print(f"正在从{gse_match_file}查找项目{project_id}的SRA Accession和Strategy...")
    with open(gse_match_file, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            if row['Project ID'] == project_id:
                strategy = row['Strategy']
                sra = row['SRA Accession']
                strategy_sra_dict[strategy].append(sra)
    
    # 显示找到的策略和SRA数量
    for strategy, sra_list in strategy_sra_dict.items():
        print(f"策略 {strategy}: 找到 {len(sra_list)} 个SRA Accession")
    
    if not strategy_sra_dict:
        print(f"未找到项目 {project_id} 的数据")
        return None
    
    # 第2步：按策略类型计算P、M、R值
    strategy_map = {
        'Ribo': 'P',
        'RNA': 'M',
        'RNC': 'R'
    }
    
    values = {}
    
    # 处理每种策略
    for strategy, sra_list in strategy_sra_dict.items():
        if strategy in strategy_map:
            key = strategy_map[strategy]
            print(f"\n处理策略 {strategy} ({key}):")
            
            valid_tpm_sum = 0
            valid_count = 0
            
            # 遍历每个SRA Accession
            for sra in sra_list:
                tpm_file = os.path.join(tpm_dir, f"{sra}_final_merged_tpm.csv")
                
                if os.path.exists(tpm_file):
                    try:
                        # 读取TPM文件
                        df = pd.read_csv(tpm_file)
                        
                        # 查找特定转录本
                        transcript_rows = df[df['transcript_id'] == transcript_id]
                        
                        if not transcript_rows.empty:
                            tpm_value = transcript_rows.iloc[0]['TPM']
                            
                            # 只有TPM ≥ 1才算有效值
                            if tpm_value >= 1:
                                valid_tpm_sum += tpm_value
                                valid_count += 1
                                print(f"  SRA {sra}: TPM = {tpm_value:.4f} (有效)")
                            else:
                                print(f"  SRA {sra}: TPM = {tpm_value:.4f} (无效，小于1)")
                        else:
                            print(f"  SRA {sra}: 未找到转录本 {transcript_id}")
                    except Exception as e:
                        print(f"  处理文件 {tpm_file} 时出错: {e}")
                else:
                    print(f"  SRA {sra}: TPM文件不存在 ({tpm_file})")
            
            # 计算平均值
            if valid_count > 0:
                avg_value = valid_tpm_sum / valid_count
                values[key] = avg_value
                print(f"  {strategy} ({key}) 平均值: {avg_value:.4f} = {valid_tpm_sum:.4f}/{valid_count}")
            else:
                print(f"  {strategy} ({key}): 没有有效TPM值")
    
    # 第3步：计算翻译指标
    print("\n计算翻译指标:")
    if len(values) < 2:
        print("无法计算指标: 至少需要两种策略类型的有效数据")
        return None
    
    results = {}
    
    # 获取P, M, R值
    p_value = values.get('P')
    m_value = values.get('M')
    r_value = values.get('R')
    
    # 计算TR (Translation Ratio)
    if r_value is not None and m_value is not None and m_value != 0:
        results['TR'] = r_value / m_value
        print(f"Translation Ratio (TR) = R/M = {r_value:.4f}/{m_value:.4f} = {results['TR']:.4f}")
    else:
        print("无法计算 TR: 缺少必要数据")
    
    # 计算EVI (Elongation Velocity Index)
    if r_value is not None and m_value is not None and p_value is not None and m_value != 0 and p_value != 0:
        results['EVI'] = (r_value ** 2) / (m_value * p_value)
        print(f"Elongation Velocity Index (EVI) = R²/(M*P) = {r_value:.4f}²/({m_value:.4f}*{p_value:.4f}) = {results['EVI']:.4f}")
    else:
        print("无法计算 EVI: 缺少必要数据")
    
    # 计算TE (Translational Efficiency)
    if p_value is not None and m_value is not None and m_value != 0:
        results['TE'] = p_value / m_value
        print(f"Translational Efficiency (TE) = P/M = {p_value:.4f}/{m_value:.4f} = {results['TE']:.4f}")
    else:
        print("无法计算 TE: 缺少必要数据")
    
    return results

if __name__ == "__main__":
    results = calculate_local_translation_metrics() 