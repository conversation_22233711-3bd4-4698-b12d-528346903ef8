#!/usr/bin/env python3
"""
脚本功能：为gene.csv文件添加TR、E<PERSON>、TE三列
通过geneId和projectId匹配translation_indices_results_grouped.csv中的数据
"""

import pandas as pd
import numpy as np

def add_translation_indices():
    """主函数：为gene.csv添加翻译指标列"""
    
    print("开始处理gene.csv文件...")
    
    # 读取gene.csv文件
    print("1. 读取gene.csv文件...")
    gene_df = pd.read_csv('gene.csv')
    print(f"   gene.csv文件包含 {len(gene_df)} 行数据")
    print(f"   列名: {list(gene_df.columns)}")
    
    # 读取translation_indices_results_grouped.csv文件
    print("2. 读取translation_indices_results_grouped.csv文件...")
    translation_df = pd.read_csv('translation_indices_results_grouped.csv')
    print(f"   translation_indices_results_grouped.csv文件包含 {len(translation_df)} 行数据")
    print(f"   列名: {list(translation_df.columns)}")
    
    # 检查关键列是否存在
    print("3. 检查关键列...")
    gene_required_cols = ['geneId', 'projectId']
    translation_required_cols = ['ensembl_gene_id', 'project_id', 'TR', 'EVI', 'TE']
    
    for col in gene_required_cols:
        if col not in gene_df.columns:
            print(f"   错误：gene.csv中缺少列 '{col}'")
            return
    
    for col in translation_required_cols:
        if col not in translation_df.columns:
            print(f"   错误：translation_indices_results_grouped.csv中缺少列 '{col}'")
            return
    
    print("   ✅ 所有必需的列都存在")
    
    # 创建匹配键
    print("4. 准备数据匹配...")
    gene_df['match_key'] = gene_df['geneId'].astype(str) + '_' + gene_df['projectId'].astype(str)
    translation_df['match_key'] = translation_df['ensembl_gene_id'].astype(str) + '_' + translation_df['project_id'].astype(str)
    
    print(f"   gene.csv中唯一的匹配键数量: {gene_df['match_key'].nunique()}")
    print(f"   translation_indices_results_grouped.csv中唯一的匹配键数量: {translation_df['match_key'].nunique()}")
    
    # 执行左连接
    print("5. 执行数据匹配...")
    # 选择需要的列进行合并
    translation_subset = translation_df[['match_key', 'TR', 'EVI', 'TE']].copy()
    
    # 执行左连接
    result_df = gene_df.merge(translation_subset, on='match_key', how='left')
    
    # 删除临时的match_key列
    result_df = result_df.drop('match_key', axis=1)
    
    # 统计匹配结果
    print("6. 统计匹配结果...")
    total_rows = len(result_df)
    
    # 统计每列的非空值数量
    tr_matched = result_df['TR'].notna().sum()
    evi_matched = result_df['EVI'].notna().sum()
    te_matched = result_df['TE'].notna().sum()
    
    # 统计至少有一个指标的行数
    any_matched = result_df[['TR', 'EVI', 'TE']].notna().any(axis=1).sum()
    
    print(f"   总行数: {total_rows}")
    print(f"   获取到TR值的行数: {tr_matched} ({tr_matched/total_rows*100:.2f}%)")
    print(f"   获取到EVI值的行数: {evi_matched} ({evi_matched/total_rows*100:.2f}%)")
    print(f"   获取到TE值的行数: {te_matched} ({te_matched/total_rows*100:.2f}%)")
    print(f"   至少获取到一个指标的行数: {any_matched} ({any_matched/total_rows*100:.2f}%)")
    
    # 显示一些示例数据
    print("7. 显示前5行结果...")
    print(result_df[['geneSymbol', 'geneId', 'projectId', 'TR', 'EVI', 'TE']].head())
    
    # 保存结果
    output_file = 'gene_with_translation_indices.csv'
    print(f"8. 保存结果到 {output_file}...")
    result_df.to_csv(output_file, index=False)
    print(f"   ✅ 结果已保存到 {output_file}")
    
    # 显示一些匹配成功的示例
    print("9. 显示一些匹配成功的示例...")
    matched_examples = result_df[result_df[['TR', 'EVI', 'TE']].notna().any(axis=1)].head(3)
    if len(matched_examples) > 0:
        print("   匹配成功的示例:")
        for idx, row in matched_examples.iterrows():
            print(f"   - 基因: {row['geneSymbol']} ({row['geneId']}) 项目: {row['projectId']}")
            print(f"     TR: {row['TR']}, EVI: {row['EVI']}, TE: {row['TE']}")
    else:
        print("   ⚠️ 没有找到匹配成功的记录")
    
    # 显示一些未匹配的示例
    print("10. 显示一些未匹配的示例...")
    unmatched_examples = result_df[result_df[['TR', 'EVI', 'TE']].isna().all(axis=1)].head(3)
    if len(unmatched_examples) > 0:
        print("    未匹配的示例:")
        for idx, row in unmatched_examples.iterrows():
            print(f"    - 基因: {row['geneSymbol']} ({row['geneId']}) 项目: {row['projectId']}")
    
    print("\n✅ 处理完成！")
    return result_df

if __name__ == '__main__':
    add_translation_indices()
