#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查 Gene_count_old 文件夹下所有文件中的 Geneid 对应的 external_gene_name 是否一致
"""

import os
import csv
from collections import defaultdict
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gene_consistency_check.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def check_gene_consistency():
    """检查基因ID和基因名称的一致性"""
    gene_count_dir = "../Gene_count_old"
    
    if not os.path.exists(gene_count_dir):
        logging.error(f"目录 {gene_count_dir} 不存在!")
        return
    
    # 存储每个基因ID对应的所有基因名称
    gene_mappings = defaultdict(set)
    # 存储每个文件的基因数量
    file_gene_counts = {}
    # 存储不一致的基因信息
    inconsistent_genes = defaultdict(dict)
    
    # 获取所有TPM文件
    tpm_files = []
    for filename in os.listdir(gene_count_dir):
        if filename.endswith("_final_merged_gene_tpm.txt"):
            tpm_files.append(filename)
    
    logging.info(f"找到 {len(tpm_files)} 个TPM文件")
    
    # 处理每个文件
    for filename in tpm_files:
        filepath = os.path.join(gene_count_dir, filename)
        logging.info(f"处理文件: {filename}")
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f, delimiter='\t')
                gene_count = 0
                
                for row in reader:
                    gene_id = row['Geneid'].strip()
                    gene_name = row['external_gene_name'].strip()
                    
                    # 记录基因ID和基因名称的映射
                    gene_mappings[gene_id].add(gene_name)
                    
                    # 记录每个文件中的基因名称
                    if gene_id not in inconsistent_genes:
                        inconsistent_genes[gene_id] = {}
                    inconsistent_genes[gene_id][filename] = gene_name
                    
                    gene_count += 1
                
                file_gene_counts[filename] = gene_count
                logging.info(f"  文件 {filename} 包含 {gene_count} 个基因")
                
        except Exception as e:
            logging.error(f"处理文件 {filename} 时出错: {e}")
            continue
    
    # 分析结果
    logging.info("开始分析基因名称一致性...")
    
    total_genes = len(gene_mappings)
    consistent_genes = 0
    inconsistent_count = 0
    
    # 检查不一致的基因
    inconsistent_details = []
    
    for gene_id, gene_names in gene_mappings.items():
        if len(gene_names) == 1:
            consistent_genes += 1
        else:
            inconsistent_count += 1
            # 收集详细信息
            file_mappings = inconsistent_genes[gene_id]
            inconsistent_details.append({
                'gene_id': gene_id,
                'gene_names': list(gene_names),
                'file_mappings': file_mappings
            })
    
    # 输出统计结果
    logging.info("=" * 60)
    logging.info("基因名称一致性检查结果:")
    logging.info(f"总基因数: {total_genes}")
    logging.info(f"名称一致的基因: {consistent_genes}")
    logging.info(f"名称不一致的基因: {inconsistent_count}")
    logging.info(f"一致性比例: {(consistent_genes/total_genes*100):.2f}%")
    
    # 输出文件统计
    logging.info("\n文件基因数量统计:")
    for filename, count in sorted(file_gene_counts.items()):
        logging.info(f"  {filename}: {count} 个基因")
    
    # 输出不一致的基因详情
    if inconsistent_count > 0:
        logging.info(f"\n发现 {inconsistent_count} 个基因名称不一致的基因:")
        
        # 只显示前20个不一致的基因
        display_count = min(20, len(inconsistent_details))
        for i, detail in enumerate(inconsistent_details[:display_count]):
            logging.info(f"\n{i+1}. 基因ID: {detail['gene_id']}")
            logging.info(f"   不同的基因名称: {detail['gene_names']}")
            logging.info("   各文件中的名称:")
            for file, name in detail['file_mappings'].items():
                logging.info(f"     {file}: {name}")
        
        if len(inconsistent_details) > display_count:
            logging.info(f"\n... 还有 {len(inconsistent_details) - display_count} 个不一致的基因")
        
        # 保存完整的不一致基因列表到文件
        with open('inconsistent_genes.txt', 'w', encoding='utf-8') as f:
            f.write("基因名称不一致的基因列表\n")
            f.write("=" * 50 + "\n\n")
            
            for i, detail in enumerate(inconsistent_details):
                f.write(f"{i+1}. 基因ID: {detail['gene_id']}\n")
                f.write(f"   不同的基因名称: {', '.join(detail['gene_names'])}\n")
                f.write("   各文件中的名称:\n")
                for file, name in detail['file_mappings'].items():
                    f.write(f"     {file}: {name}\n")
                f.write("\n")
        
        logging.info(f"\n完整的不一致基因列表已保存到: inconsistent_genes.txt")
    
    else:
        logging.info("\n✅ 所有基因的名称在不同文件中都是一致的!")
    
    # 检查是否所有文件包含相同的基因
    logging.info("\n检查文件间基因覆盖情况...")
    all_gene_counts = list(file_gene_counts.values())
    if len(set(all_gene_counts)) == 1:
        logging.info("✅ 所有文件包含相同数量的基因")
    else:
        logging.info("⚠️  不同文件包含的基因数量不同:")
        for filename, count in sorted(file_gene_counts.items()):
            logging.info(f"  {filename}: {count} 个基因")

def main():
    """主函数"""
    logging.info("开始检查基因名称一致性...")
    check_gene_consistency()
    logging.info("检查完成!")

if __name__ == '__main__':
    main()
