#!/usr/bin/env python3

# 简单测试脚本
import pandas as pd

def test_files():
    print("=== 测试文件读取 ===")
    
    # 测试 unique_gene_mapping.csv
    try:
        df_genes = pd.read_csv('unique_gene_mapping.csv')
        print(f"✅ unique_gene_mapping.csv: {len(df_genes)} 行")
        print(f"   列名: {list(df_genes.columns)}")
        print(f"   前3个基因: {df_genes['ensembl_gene_id'].head(3).tolist()}")
    except Exception as e:
        print(f"❌ unique_gene_mapping.csv 错误: {e}")
    
    # 测试 GSE_match_new.csv
    try:
        df_gse = pd.read_csv('GSE_match_new.csv')
        print(f"✅ GSE_match_new.csv: {len(df_gse)} 行")
        print(f"   列名: {list(df_gse.columns)}")
        
        # 检查项目和策略
        projects = df_gse['Project ID'].unique()
        print(f"   项目数: {len(projects)}")
        print(f"   前5个项目: {projects[:5].tolist()}")
        
        strategies = df_gse['Strategy'].unique()
        print(f"   策略类型: {strategies.tolist()}")
        
        # 检查TEDD00001项目
        tedd1 = df_gse[df_gse['Project ID'] == 'TEDD00001']
        print(f"   TEDD00001项目: {len(tedd1)} 条记录")
        print(f"   TEDD00001策略: {tedd1['Strategy'].unique().tolist()}")
        print(f"   TEDD00001的SRA: {tedd1['SRA Accession'].head(3).tolist()}")
        
    except Exception as e:
        print(f"❌ GSE_match_new.csv 错误: {e}")

if __name__ == '__main__':
    test_files()
