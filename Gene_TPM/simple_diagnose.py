#!/usr/bin/env python3

import mysql.connector

DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def diagnose_database():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("=== 检查 gseMatch 表 ===")
        cursor.execute("SELECT COUNT(*) FROM gseMatch")
        count = cursor.fetchone()[0]
        print(f"gseMatch 表总记录数: {count}")
        
        print("\n=== 检查 Gene_TPM 表 ===")
        cursor.execute("SELECT COUNT(*) FROM Gene_TPM")
        count = cursor.fetchone()[0]
        print(f"Gene_TPM 表总记录数: {count}")
        
        print("\n=== 检查连接查询 ===")
        cursor.execute("SELECT COUNT(*) FROM gseMatch g JOIN Gene_TPM t ON g.sraAccession = t.sraAccession")
        count = cursor.fetchone()[0]
        print(f"连接查询结果数: {count}")
        
        if count == 0:
            print("\n=== 检查SRA编号匹配情况 ===")
            cursor.execute("SELECT DISTINCT sraAccession FROM gseMatch LIMIT 5")
            gse_sra = cursor.fetchall()
            print("gseMatch 中的 SRA 样本:")
            for sra, in gse_sra:
                print(f"  '{sra}' (长度: {len(sra)})")
            
            cursor.execute("SELECT DISTINCT sraAccession FROM Gene_TPM LIMIT 5")
            tpm_sra = cursor.fetchall()
            print("Gene_TPM 中的 SRA 样本:")
            for sra, in tpm_sra:
                print(f"  '{sra}' (长度: {len(sra)})")
                
            # 检查项目TEDD00005的SRA
            cursor.execute("SELECT DISTINCT sraAccession FROM gseMatch WHERE projectId = 'TEDD00005' LIMIT 3")
            tedd5_sra = cursor.fetchall()
            print("\n项目TEDD00005的SRA样本:")
            for sra, in tedd5_sra:
                print(f"  '{sra}'")
                cursor.execute("SELECT COUNT(*) FROM Gene_TPM WHERE sraAccession = %s", (sra,))
                tpm_count = cursor.fetchone()[0]
                print(f"    在Gene_TPM中的记录数: {tpm_count}")
        else:
            print("✅ 有匹配的SRA编号")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    diagnose_database()
