#!/usr/bin/env python3

import mysql.connector

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def check_sra_data():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 测试SRA编号
        test_sra_list = [
            'SRR1257233', 'SRR1257234', 'SRR1257177', 'SRR1257178',  # TEDD00001的Ribo和RNA
            'SRR1551155', 'SRR1551154',  # TEDD00003的Ribo和RNA
            'SRR1551165', 'SRR1551164'   # TEDD00002的Ribo和RNA
        ]
        
        print("=== 检查 gseMatch 表中的SRA编号 ===")
        for sra in test_sra_list:
            cursor.execute("SELECT projectId, strategy FROM gseMatch WHERE sraAccession = %s", (sra,))
            result = cursor.fetchone()
            if result:
                print(f"✅ {sra}: {result[0]} - {result[1]}")
            else:
                print(f"❌ {sra}: 未找到")
        
        print("\n=== 检查 Gene_TPM 表中的SRA编号 ===")
        for sra in test_sra_list:
            cursor.execute("SELECT COUNT(*) FROM Gene_TPM WHERE sraAccession = %s", (sra,))
            count = cursor.fetchone()[0]
            if count > 0:
                print(f"✅ {sra}: {count} 条TPM记录")
            else:
                print(f"❌ {sra}: 无TPM数据")
        
        print("\n=== 检查连接查询结果 ===")
        test_project = 'TEDD00001'
        test_strategy = 'Ribo'
        
        cursor.execute("""
        SELECT COUNT(*) 
        FROM gseMatch g 
        JOIN Gene_TPM t ON g.sraAccession = t.sraAccession 
        WHERE g.projectId = %s AND g.strategy = %s
        """, (test_project, test_strategy))
        
        count = cursor.fetchone()[0]
        print(f"项目 {test_project} 策略 {test_strategy} 的连接结果: {count} 条记录")
        
        if count > 0:
            cursor.execute("""
            SELECT t.geneId, g.sraAccession, t.tpm 
            FROM gseMatch g 
            JOIN Gene_TPM t ON g.sraAccession = t.sraAccession 
            WHERE g.projectId = %s AND g.strategy = %s 
            LIMIT 5
            """, (test_project, test_strategy))
            
            results = cursor.fetchall()
            print("前5条数据样本:")
            for gene_id, sra, tpm in results:
                print(f"  {gene_id} - {sra}: {tpm}")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    check_sra_data()
