#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试翻译指标计算问题
"""

import mysql.connector
import pandas as pd

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def test_database_connection():
    """测试数据库连接"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def check_tables(conn):
    """检查相关表的数据"""
    cursor = conn.cursor()
    
    # 检查 gseMatch 表
    cursor.execute("SELECT COUNT(*) FROM gseMatch")
    gse_count = cursor.fetchone()[0]
    print(f"gseMatch 表记录数: {gse_count}")
    
    # 检查 Gene_TPM 表
    cursor.execute("SELECT COUNT(*) FROM Gene_TPM")
    tpm_count = cursor.fetchone()[0]
    print(f"Gene_TPM 表记录数: {tpm_count}")
    
    # 检查项目和策略
    cursor.execute("SELECT DISTINCT projectId, strategy FROM gseMatch LIMIT 10")
    projects = cursor.fetchall()
    print(f"前10个项目和策略:")
    for project_id, strategy in projects:
        print(f"  {project_id}: {strategy}")
    
    # 检查基因ID样本
    cursor.execute("SELECT DISTINCT geneId FROM Gene_TPM LIMIT 10")
    genes = cursor.fetchall()
    print(f"前10个基因ID:")
    for gene_id, in genes:
        print(f"  {gene_id}")

def test_join_query(conn):
    """测试连接查询"""
    cursor = conn.cursor()
    
    # 测试连接查询
    query = """
    SELECT COUNT(*) 
    FROM gseMatch g
    JOIN Gene_TPM t ON g.sraAccession = t.sraAccession
    """
    cursor.execute(query)
    join_count = cursor.fetchone()[0]
    print(f"gseMatch 和 Gene_TPM 连接后的记录数: {join_count}")
    
    # 测试特定项目的连接
    query = """
    SELECT g.projectId, g.strategy, COUNT(*) as count
    FROM gseMatch g
    JOIN Gene_TPM t ON g.sraAccession = t.sraAccession
    WHERE g.projectId IN ('TEDD00001', 'TEDD00002', 'TEDD00003')
    GROUP BY g.projectId, g.strategy
    """
    cursor.execute(query)
    results = cursor.fetchall()
    print(f"测试项目的连接结果:")
    for project_id, strategy, count in results:
        print(f"  {project_id} - {strategy}: {count} 条记录")

def test_specific_query(conn):
    """测试具体的查询"""
    cursor = conn.cursor()
    
    # 测试具体的基因和项目查询
    query = """
    SELECT t.geneId, g.sraAccession, t.tpm 
    FROM gseMatch g
    JOIN Gene_TPM t ON g.sraAccession = t.sraAccession
    WHERE g.projectId = 'TEDD00001'
    AND g.strategy = 'Ribo'
    LIMIT 10
    """
    cursor.execute(query)
    results = cursor.fetchall()
    print(f"TEDD00001 项目 Ribo 策略的前10条数据:")
    for gene_id, sra, tpm in results:
        print(f"  {gene_id} - {sra}: {tpm}")

def check_file_exists():
    """检查输入文件是否存在"""
    import os
    
    files_to_check = [
        'unique_gene_mapping.csv',
        'GSE_match_new.csv'
    ]
    
    for filename in files_to_check:
        if os.path.exists(filename):
            print(f"✅ 文件存在: {filename}")
            # 读取前几行
            try:
                df = pd.read_csv(filename, nrows=5)
                print(f"  列名: {list(df.columns)}")
                print(f"  行数: {len(df)}")
            except Exception as e:
                print(f"  读取错误: {e}")
        else:
            print(f"❌ 文件不存在: {filename}")

def main():
    print("开始调试翻译指标计算问题...")
    
    # 检查文件
    print("\n1. 检查输入文件:")
    check_file_exists()
    
    # 测试数据库
    print("\n2. 测试数据库连接:")
    conn = test_database_connection()
    if not conn:
        return
    
    print("\n3. 检查数据库表:")
    check_tables(conn)
    
    print("\n4. 测试表连接:")
    test_join_query(conn)
    
    print("\n5. 测试具体查询:")
    test_specific_query(conn)
    
    conn.close()
    print("\n调试完成!")

if __name__ == '__main__':
    main()
