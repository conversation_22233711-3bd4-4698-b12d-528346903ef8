#!/usr/bin/env python3

import os

def basic_check():
    gene_count_dir = "../Gene_count_old"
    
    # 测试文件列表
    test_files = [
        "ERR3367723_final_merged_gene_tpm.txt",
        "SRR5409600_final_merged_gene_tpm.txt",
        "SRR5345617_final_merged_gene_tpm.txt"
    ]
    
    print("基因名称一致性检查")
    print("=" * 40)
    
    # 存储基因映射
    gene_mappings = {}
    
    for filename in test_files:
        filepath = os.path.join(gene_count_dir, filename)
        if not os.path.exists(filepath):
            print(f"文件不存在: {filename}")
            continue
            
        print(f"\n处理文件: {filename}")
        
        try:
            with open(filepath, 'r') as f:
                lines = f.readlines()
                
            # 跳过标题行，处理前20行数据
            for i, line in enumerate(lines[1:21]):  # 前20行数据
                parts = line.strip().split('\t')
                if len(parts) >= 3:
                    gene_id = parts[0]
                    gene_name = parts[2]
                    
                    if gene_id not in gene_mappings:
                        gene_mappings[gene_id] = set()
                    gene_mappings[gene_id].add(gene_name)
            
            print(f"  处理了前20个基因")
            
        except Exception as e:
            print(f"  错误: {e}")
    
    # 分析结果
    print(f"\n分析结果:")
    print(f"总基因数: {len(gene_mappings)}")
    
    consistent = 0
    inconsistent = 0
    
    print("\n基因映射检查:")
    for gene_id, names in gene_mappings.items():
        if len(names) == 1:
            consistent += 1
            print(f"✅ {gene_id} → {list(names)[0]}")
        else:
            inconsistent += 1
            print(f"❌ {gene_id} → {list(names)} (不一致!)")
    
    print(f"\n统计:")
    print(f"一致的基因: {consistent}")
    print(f"不一致的基因: {inconsistent}")
    
    if inconsistent == 0:
        print("✅ 所有检查的基因名称都是一致的!")
    else:
        print(f"⚠️  发现 {inconsistent} 个基因名称不一致")

if __name__ == '__main__':
    basic_check()
