#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import bulk_translation_indices

def debug_function_modification():
    """调试函数修改"""
    print("=== 调试函数修改 ===")
    
    try:
        # 初始化连接池
        if bulk_translation_indices.setup_connection_pool(pool_size=5):
            print("✅ 数据库连接池初始化成功")
        else:
            print("❌ 数据库连接池初始化失败")
            return
        
        # 测试calculate_strategy_value_batch函数
        print("\n测试 calculate_strategy_value_batch 函数...")
        
        # 获取一些测试数据
        gene_ids = ['ENSG00000000003', 'ENSG00000000005']  # 测试基因
        project_id = 'TEDD00005'  # 测试项目
        strategy = 'Ribo'  # 测试策略
        
        print(f"测试参数: 项目={project_id}, 策略={strategy}, 基因={gene_ids}")
        
        # 调用函数
        result = bulk_translation_indices.calculate_strategy_value_batch(
            project_id, strategy, gene_ids
        )
        
        print(f"函数返回类型: {type(result)}")
        
        if isinstance(result, tuple) and len(result) == 2:
            avg_results, raw_results = result
            print("✅ 函数返回两个值（平均值和原始值）")
            print(f"平均值结果: {avg_results}")
            print(f"原始值结果: {raw_results}")
            
            # 检查原始值格式
            for gene_id, tpm_values in raw_results.items():
                print(f"基因 {gene_id} 的原始TPM值: {tpm_values}")
                tpm_str = ';'.join(map(str, tpm_values))
                print(f"转换为字符串: '{tpm_str}'")
                
        else:
            print("❌ 函数返回格式不正确")
            print(f"返回值: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        bulk_translation_indices.cleanup_resources()

def test_csv_creation():
    """测试CSV创建"""
    print("\n=== 测试CSV创建 ===")
    
    import csv
    
    output_file = 'debug_test.csv'
    
    # 创建CSV文件
    with open(output_file, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['ensembl_gene_id', 'project_id', 'bioproject_id',
                        'TR', 'EVI', 'TE', 'RNA_TPM', 'RNC_TPM', 'Ribo_TPM'])
        
        # 写入测试数据
        writer.writerow(['ENSG00000000003', 'TEDD00005', 'PRJNA256316',
                        1.5, 2.0, 3.0, '1.1;2.2;3.3', '4.4;5.5', '6.6;7.7;8.8'])
    
    # 读取并验证
    with open(output_file, 'r') as csvfile:
        reader = csv.reader(csvfile)
        header = next(reader)
        data_row = next(reader)
        
        print(f"CSV头部: {header}")
        print(f"数据行: {data_row}")
        
        if len(header) == 9 and len(data_row) == 9:
            print("✅ CSV格式正确")
        else:
            print("❌ CSV格式不正确")
    
    # 清理
    os.remove(output_file)

if __name__ == '__main__':
    debug_function_modification()
    test_csv_creation()
    print("\n=== 调试完成 ===")
