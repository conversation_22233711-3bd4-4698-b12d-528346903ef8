#!/bin/bash

echo "检查基因名称一致性..."

# 选择几个测试文件
files=(
    "../Gene_count_old/ERR3367723_final_merged_gene_tpm.txt"
    "../Gene_count_old/SRR5409600_final_merged_gene_tpm.txt"
    "../Gene_count_old/SRR5345617_final_merged_gene_tpm.txt"
)

# 提取第一个文件的基因ID和基因名称作为参考
echo "提取参考基因映射..."
awk -F'\t' 'NR>1 {print $1 "\t" $3}' "${files[0]}" | head -20 > reference_genes.txt

echo "参考文件中的前20个基因:"
cat reference_genes.txt

echo -e "\n检查其他文件中相同基因的名称..."

# 检查其他文件
for file in "${files[@]:1}"; do
    echo -e "\n检查文件: $(basename "$file")"
    
    # 提取前20个基因的映射
    awk -F'\t' 'NR>1 {print $1 "\t" $3}' "$file" | head -20 > current_genes.txt
    
    # 比较差异
    echo "与参考文件的差异:"
    diff reference_genes.txt current_genes.txt || echo "文件内容一致"
done

# 清理临时文件
rm -f reference_genes.txt current_genes.txt

echo -e "\n检查完成!"
