#!/usr/bin/env python3
"""
基因翻译效率统计分析脚本
对gene_with_translation_indices.csv文件进行统计分析
"""

import pandas as pd
import numpy as np
from scipy import stats
from statsmodels.stats.multitest import multipletests
import warnings
warnings.filterwarnings('ignore')

def load_and_explore_data(file_path):
    """加载数据并进行初步探索"""
    print("正在加载数据...")
    try:
        # 先尝试读取少量数据来检查格式，使用object类型避免numpy递归错误
        print("读取样本数据...")
        df_sample = pd.read_csv(file_path, nrows=5, dtype=str)
        print(f"样本数据列名: {list(df_sample.columns)}")
        print("样本数据:")
        print(df_sample)

        # 读取完整数据，使用object类型避免自动类型推断
        print("读取完整数据...")
        df = pd.read_csv(file_path, dtype=str, low_memory=False)

        print(f"数据加载成功！数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print("\n前5行数据:")
        print(df.head())
        print("\n原始数据类型:")
        print(df.dtypes)
        print("\n缺失值统计:")
        print(df.isnull().sum())
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def preprocess_data(df):
    """数据预处理"""
    print("\n开始数据预处理...")

    # 检查必要的列是否存在
    required_cols = ['geneSymbol', 'geneId', 'disease']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"缺少必要的列: {missing_cols}")
        return None

    # 确保字符串列的数据类型正确
    try:
        df['geneSymbol'] = df['geneSymbol'].astype(str)
        df['geneId'] = df['geneId'].astype(str)
        df['disease'] = df['disease'].astype(str)
        print("字符串列数据类型转换完成")
    except Exception as e:
        print(f"字符串列数据类型转换失败: {e}")
        return None

    # 按照 geneSymbol, geneId 分组
    print("按照 geneSymbol, geneId 进行分组...")
    grouped = df.groupby(['geneSymbol', 'geneId'])
    print(f"共有 {len(grouped)} 个基因组合")

    # 检查TE、TR、EVI列
    analysis_cols = ['TE', 'TR', 'EVI']
    available_cols = [col for col in analysis_cols if col in df.columns]
    print(f"可用于分析的变量列: {available_cols}")

    if not available_cols:
        print("未找到TE、TR、EVI列，请检查数据格式")
        return None

    # 确保数值列的数据类型正确，并处理异常值
    for col in available_cols:
        try:
            # 转换为数值类型，无法转换的设为NaN
            df[col] = pd.to_numeric(df[col], errors='coerce')
            print(f"列 {col} 数据类型转换完成，缺失值数量: {df[col].isnull().sum()}")
        except Exception as e:
            print(f"列 {col} 数据类型转换失败: {e}")
            return None

    # 检查disease列的唯一值
    diseases = df['disease'].unique()
    print(f"疾病类型: {diseases}")

    if 'Normal' not in diseases:
        print("警告: 未找到'Normal'对照组")

    return df, available_cols, diseases

def perform_statistical_tests(disease_data, normal_data, gene_id, gene_symbol, disease, variable):
    """执行统计检验"""
    # 移除缺失值并确保数据类型正确
    try:
        disease_clean = pd.to_numeric(disease_data, errors='coerce').dropna()
        normal_clean = pd.to_numeric(normal_data, errors='coerce').dropna()
    except Exception as e:
        print(f"数据类型转换失败 - 基因: {gene_symbol}, 疾病: {disease}, 变量: {variable}, 错误: {e}")
        return None

    # 数据不足时直接返回None，不保存结果
    if len(disease_clean) < 2 or len(normal_clean) < 2:
        return None

    # 检查数据是否包含无穷大或极大值
    if not (np.isfinite(disease_clean).all() and np.isfinite(normal_clean).all()):
        print(f"数据包含无穷大值 - 基因: {gene_symbol}, 疾病: {disease}, 变量: {variable}")
        return None

    # 初始化结果字典
    results = {
        'geneId': gene_id,
        'geneSymbol': gene_symbol,
        'disease_category': disease,
        'variable': variable,
        'p_t': np.nan,
        'p_wilcox': np.nan,
        'p_ks': np.nan,
        'direction': 'unknown'
    }

    try:
        # T检验
        t_stat, p_t = stats.ttest_ind(disease_clean, normal_clean)
        if np.isfinite(p_t):
            results['p_t'] = float(p_t)

        # Wilcoxon秩和检验 (Mann-Whitney U检验)
        u_stat, p_wilcox = stats.mannwhitneyu(disease_clean, normal_clean, alternative='two-sided')
        if np.isfinite(p_wilcox):
            results['p_wilcox'] = float(p_wilcox)

        # Kolmogorov-Smirnov检验
        ks_stat, p_ks = stats.ks_2samp(disease_clean, normal_clean)
        if np.isfinite(p_ks):
            results['p_ks'] = float(p_ks)

        # 确定表达变化方向
        disease_median = float(disease_clean.median())
        normal_median = float(normal_clean.median())

        if disease_median > normal_median:
            results['direction'] = 'higher'
        else:
            results['direction'] = 'lower'

    except Exception as e:
        print(f"统计检验失败 - 基因: {gene_symbol}, 疾病: {disease}, 变量: {variable}, 错误: {e}")
        return None

    return results

def analyze_gene_data(df, available_cols, diseases):
    """分析基因数据"""
    print("\n开始统计分析...")
    
    all_results = []
    normal_diseases = [d for d in diseases if d == 'Normal']
    test_diseases = [d for d in diseases if d != 'Normal']
    
    if not normal_diseases:
        print("错误: 未找到Normal对照组")
        return None
    
    print(f"对照组: {normal_diseases}")
    print(f"测试疾病组: {test_diseases}")
    
    # 按基因分组
    gene_groups = df.groupby(['geneSymbol', 'geneId'])
    total_genes = len(gene_groups)
    
    for i, ((gene_symbol, gene_id), gene_data) in enumerate(gene_groups):
        if i % 100 == 0:
            print(f"处理进度: {i}/{total_genes} 基因")
        
        # 获取Normal组数据
        normal_data = gene_data[gene_data['disease'] == 'Normal']
        
        if len(normal_data) == 0:
            continue
        
        # 对每个变量类型进行分析
        for variable in available_cols:
            normal_values = normal_data[variable]
            
            # 检查该变量是否有非空值
            if normal_values.dropna().empty:
                continue
            
            # 对每个疾病组进行比较
            for disease in test_diseases:
                disease_data = gene_data[gene_data['disease'] == disease]
                
                if len(disease_data) == 0:
                    continue
                
                disease_values = disease_data[variable]
                
                # 检查疾病组该变量是否有非空值
                if disease_values.dropna().empty:
                    continue
                
                # 执行统计检验
                result = perform_statistical_tests(
                    disease_values, normal_values,
                    gene_id, gene_symbol, disease, variable
                )

                # 只有当结果不为None时才添加到结果列表
                if result is not None:
                    all_results.append(result)
    
    print(f"完成分析，共生成 {len(all_results)} 个结果")
    return all_results

def apply_fdr_correction(results_df):
    """应用FDR多重检验校正"""
    print("\n应用FDR多重检验校正...")
    
    # 为每种检验方法应用FDR校正
    test_types = ['p_t', 'p_wilcox', 'p_ks']
    
    for test_type in test_types:
        p_values = results_df[test_type].dropna()
        
        if len(p_values) > 0:
            # 应用Benjamini-Hochberg FDR校正
            rejected, p_corrected, _, _ = multipletests(p_values, method='fdr_bh')
            
            # 创建校正后的p值列
            fdr_col = f'fdr_{test_type[2:]}'  # 去掉'p_'前缀
            results_df[fdr_col] = np.nan
            results_df.loc[p_values.index, fdr_col] = p_corrected
    
    return results_df

def save_results(results_df, output_file):
    """保存结果"""
    print(f"\n保存结果到: {output_file}")
    
    # 重新排列列的顺序
    column_order = [
        'geneId', 'geneSymbol', 'disease_category', 
        'p_t', 'fdr_t', 'p_wilcox', 'fdr_wilcox', 'p_ks', 'fdr_ks',
        'direction', 'variable'
    ]
    
    # 确保所有列都存在
    for col in column_order:
        if col not in results_df.columns:
            results_df[col] = np.nan
    
    results_df = results_df[column_order]
    
    # 保存为CSV
    results_df.to_csv(output_file, index=False)
    
    print("结果保存完成！")
    print(f"结果文件包含 {len(results_df)} 行数据")
    
    # 显示结果摘要
    print("\n结果摘要:")
    print(f"分析的基因数量: {results_df['geneSymbol'].nunique()}")
    print(f"分析的疾病类型: {results_df['disease_category'].nunique()}")
    print(f"分析的变量类型: {results_df['variable'].nunique()}")
    
    # 显示显著性结果统计
    for test_type in ['fdr_t', 'fdr_wilcox', 'fdr_ks']:
        if test_type in results_df.columns:
            significant = (results_df[test_type] < 0.05).sum()
            total = results_df[test_type].notna().sum()
            print(f"{test_type} 显著性结果 (p<0.05): {significant}/{total}")

def main():
    """主函数"""
    input_file = "gene_with_translation_indices.csv"
    output_file = "gene_statistical_analysis_results_median.csv"
    
    print("=== 基因翻译效率统计分析 ===")
    
    # 1. 加载和探索数据
    df = load_and_explore_data(input_file)
    if df is None:
        return
    
    # 2. 数据预处理
    preprocessing_result = preprocess_data(df)
    if preprocessing_result is None:
        return
    
    df, available_cols, diseases = preprocessing_result
    
    # 3. 执行统计分析
    results = analyze_gene_data(df, available_cols, diseases)
    if results is None or len(results) == 0:
        print("未生成任何分析结果")
        return
    
    # 4. 转换为DataFrame
    results_df = pd.DataFrame(results)
    
    # 5. 应用FDR校正
    results_df = apply_fdr_correction(results_df)
    
    # 6. 保存结果
    save_results(results_df, output_file)
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
