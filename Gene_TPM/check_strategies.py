#!/usr/bin/env python3

import pandas as pd

def check_strategies():
    print("=== 检查策略分布 ===")
    
    try:
        # 读取GSE匹配文件
        df = pd.read_csv('GSE_match_new.csv')
        print(f"总记录数: {len(df)}")
        
        # 检查所有策略类型
        strategies = df['Strategy'].value_counts()
        print(f"\n所有策略类型分布:")
        for strategy, count in strategies.items():
            print(f"  {strategy}: {count} 条记录")
        
        # 检查项目的策略分布
        print(f"\n前10个项目的策略分布:")
        projects = df['Project ID'].unique()[:10]
        
        for project_id in projects:
            project_data = df[df['Project ID'] == project_id]
            project_strategies = project_data['Strategy'].unique()
            print(f"  {project_id}: {project_strategies.tolist()} ({len(project_data)} 条记录)")
        
        # 特别检查测试项目
        test_projects = ['TEDD00001', 'TEDD00002', 'TEDD00003', 'TEDD00004', 'TEDD00005']
        print(f"\n测试项目的策略分布:")
        
        for project_id in test_projects:
            project_data = df[df['Project ID'] == project_id]
            if len(project_data) > 0:
                project_strategies = project_data['Strategy'].unique()
                sra_list = project_data['SRA Accession'].tolist()
                print(f"  {project_id}:")
                print(f"    策略: {project_strategies.tolist()}")
                print(f"    SRA数量: {len(sra_list)}")
                print(f"    前3个SRA: {sra_list[:3]}")
            else:
                print(f"  {project_id}: 未找到数据")
        
        # 检查多策略项目
        print(f"\n有多种策略的项目:")
        multi_strategy_projects = []
        for project_id in df['Project ID'].unique():
            project_strategies = df[df['Project ID'] == project_id]['Strategy'].unique()
            if len(project_strategies) > 1:
                multi_strategy_projects.append((project_id, project_strategies.tolist()))
        
        print(f"多策略项目数量: {len(multi_strategy_projects)}")
        for project_id, strategies in multi_strategy_projects[:10]:
            print(f"  {project_id}: {strategies}")
            
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    check_strategies()
