#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面检查 Gene_count_old 文件夹下所有文件中的基因ID与基因名称一致性
"""

import os
import csv
from collections import defaultdict, Counter
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_gene_check.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def check_all_files():
    """检查所有文件的基因名称一致性"""
    gene_count_dir = "../Gene_count_old"
    
    if not os.path.exists(gene_count_dir):
        logging.error(f"目录 {gene_count_dir} 不存在!")
        return
    
    # 存储每个基因ID对应的所有基因名称
    gene_mappings = defaultdict(set)
    # 存储每个文件的统计信息
    file_stats = {}
    # 存储不一致的基因详情
    inconsistent_genes = defaultdict(dict)
    
    # 获取所有TPM文件
    tpm_files = []
    for filename in os.listdir(gene_count_dir):
        if filename.endswith("_final_merged_gene_tpm.txt"):
            tpm_files.append(filename)
    
    logging.info(f"找到 {len(tpm_files)} 个TPM文件")
    
    # 处理每个文件
    processed_files = 0
    for filename in tpm_files:
        filepath = os.path.join(gene_count_dir, filename)
        logging.info(f"处理文件 {processed_files + 1}/{len(tpm_files)}: {filename}")
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f, delimiter='\t')
                gene_count = 0
                
                for row in reader:
                    gene_id = row['Geneid'].strip()
                    gene_name = row['external_gene_name'].strip()
                    
                    # 记录基因ID和基因名称的映射
                    gene_mappings[gene_id].add(gene_name)
                    
                    # 记录每个文件中的基因名称
                    if gene_id not in inconsistent_genes:
                        inconsistent_genes[gene_id] = {}
                    inconsistent_genes[gene_id][filename] = gene_name
                    
                    gene_count += 1
                
                file_stats[filename] = {
                    'gene_count': gene_count,
                    'status': 'success'
                }
                
                processed_files += 1
                
                # 每处理10个文件显示进度
                if processed_files % 10 == 0:
                    logging.info(f"已处理 {processed_files}/{len(tpm_files)} 个文件")
                
        except Exception as e:
            logging.error(f"处理文件 {filename} 时出错: {e}")
            file_stats[filename] = {
                'gene_count': 0,
                'status': 'error',
                'error': str(e)
            }
            continue
    
    # 分析结果
    logging.info("开始分析基因名称一致性...")
    
    total_genes = len(gene_mappings)
    consistent_genes = 0
    inconsistent_count = 0
    
    # 检查不一致的基因
    inconsistent_details = []
    
    for gene_id, gene_names in gene_mappings.items():
        if len(gene_names) == 1:
            consistent_genes += 1
        else:
            inconsistent_count += 1
            # 收集详细信息
            file_mappings = inconsistent_genes[gene_id]
            inconsistent_details.append({
                'gene_id': gene_id,
                'gene_names': list(gene_names),
                'file_count': len(file_mappings),
                'file_mappings': file_mappings
            })
    
    # 输出统计结果
    logging.info("=" * 80)
    logging.info("基因名称一致性检查结果:")
    logging.info(f"处理的文件数: {processed_files}")
    logging.info(f"总基因数: {total_genes}")
    logging.info(f"名称一致的基因: {consistent_genes}")
    logging.info(f"名称不一致的基因: {inconsistent_count}")
    
    if total_genes > 0:
        consistency_rate = (consistent_genes / total_genes) * 100
        logging.info(f"一致性比例: {consistency_rate:.2f}%")
    
    # 文件统计
    logging.info("\n文件处理统计:")
    success_count = sum(1 for stats in file_stats.values() if stats['status'] == 'success')
    error_count = len(file_stats) - success_count
    logging.info(f"成功处理: {success_count} 个文件")
    logging.info(f"处理失败: {error_count} 个文件")
    
    # 基因数量统计
    gene_counts = [stats['gene_count'] for stats in file_stats.values() if stats['status'] == 'success']
    if gene_counts:
        logging.info(f"每个文件的基因数量: 最小={min(gene_counts)}, 最大={max(gene_counts)}, 平均={sum(gene_counts)/len(gene_counts):.0f}")
        
        # 检查基因数量是否一致
        unique_counts = set(gene_counts)
        if len(unique_counts) == 1:
            logging.info("✅ 所有文件包含相同数量的基因")
        else:
            logging.info(f"⚠️  文件包含不同数量的基因: {sorted(unique_counts)}")
    
    # 输出不一致的基因详情
    if inconsistent_count > 0:
        logging.info(f"\n发现 {inconsistent_count} 个基因名称不一致的基因:")
        
        # 按不一致程度排序
        inconsistent_details.sort(key=lambda x: len(x['gene_names']), reverse=True)
        
        # 显示前10个最严重的不一致
        display_count = min(10, len(inconsistent_details))
        for i, detail in enumerate(inconsistent_details[:display_count]):
            logging.info(f"\n{i+1}. 基因ID: {detail['gene_id']}")
            logging.info(f"   不同的基因名称: {detail['gene_names']}")
            logging.info(f"   涉及文件数: {detail['file_count']}")
            
            # 显示前5个文件的映射
            sample_files = list(detail['file_mappings'].items())[:5]
            logging.info("   示例文件映射:")
            for file, name in sample_files:
                logging.info(f"     {file}: {name}")
        
        if len(inconsistent_details) > display_count:
            logging.info(f"\n... 还有 {len(inconsistent_details) - display_count} 个不一致的基因")
        
        # 保存完整的不一致基因列表
        output_file = 'inconsistent_genes_full_report.txt'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("基因名称不一致的完整报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"总基因数: {total_genes}\n")
            f.write(f"不一致基因数: {inconsistent_count}\n")
            f.write(f"一致性比例: {consistency_rate:.2f}%\n\n")
            
            for i, detail in enumerate(inconsistent_details):
                f.write(f"{i+1}. 基因ID: {detail['gene_id']}\n")
                f.write(f"   不同的基因名称: {', '.join(detail['gene_names'])}\n")
                f.write(f"   涉及文件数: {detail['file_count']}\n")
                f.write("   所有文件映射:\n")
                for file, name in detail['file_mappings'].items():
                    f.write(f"     {file}: {name}\n")
                f.write("\n")
        
        logging.info(f"\n完整的不一致基因报告已保存到: {output_file}")
    
    else:
        logging.info("\n✅ 所有基因的名称在不同文件中都是一致的!")
    
    # 保存处理统计
    stats_file = 'file_processing_stats.txt'
    with open(stats_file, 'w', encoding='utf-8') as f:
        f.write("文件处理统计报告\n")
        f.write("=" * 50 + "\n\n")
        
        for filename, stats in sorted(file_stats.items()):
            f.write(f"文件: {filename}\n")
            f.write(f"  状态: {stats['status']}\n")
            f.write(f"  基因数量: {stats['gene_count']}\n")
            if stats['status'] == 'error':
                f.write(f"  错误信息: {stats.get('error', 'Unknown error')}\n")
            f.write("\n")
    
    logging.info(f"文件处理统计已保存到: {stats_file}")

def main():
    """主函数"""
    logging.info("开始全面检查基因名称一致性...")
    check_all_files()
    logging.info("检查完成!")

if __name__ == '__main__':
    main()
