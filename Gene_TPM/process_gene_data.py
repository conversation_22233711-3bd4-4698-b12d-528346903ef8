#!/usr/bin/env python3

import pandas as pd
import sys

def main():
    print("开始处理gene.csv文件...")
    
    # 读取文件
    print("读取文件...")
    gene_df = pd.read_csv('gene.csv')
    translation_df = pd.read_csv('translation_indices_results_grouped.csv')
    
    print(f"gene.csv文件包含 {len(gene_df)} 行数据")
    print(f"translation_indices_results_grouped.csv文件包含 {len(translation_df)} 行数据")
    
    # 创建匹配键
    print("创建匹配键...")
    gene_df['match_key'] = gene_df['geneId'].astype(str) + '_' + gene_df['projectId'].astype(str)
    translation_df['match_key'] = translation_df['ensembl_gene_id'].astype(str) + '_' + translation_df['project_id'].astype(str)
    
    print(f"gene.csv中唯一的匹配键数量: {gene_df['match_key'].nunique()}")
    print(f"translation_indices_results_grouped.csv中唯一的匹配键数量: {translation_df['match_key'].nunique()}")
    
    # 执行匹配
    print("执行数据匹配...")
    translation_subset = translation_df[['match_key', 'TR', 'EVI', 'TE']].copy()
    result_df = gene_df.merge(translation_subset, on='match_key', how='left')
    result_df = result_df.drop('match_key', axis=1)
    
    # 统计结果
    print("统计匹配结果...")
    total_rows = len(result_df)
    tr_matched = result_df['TR'].notna().sum()
    evi_matched = result_df['EVI'].notna().sum()
    te_matched = result_df['TE'].notna().sum()
    any_matched = result_df[['TR', 'EVI', 'TE']].notna().any(axis=1).sum()
    
    print(f"总行数: {total_rows}")
    print(f"获取到TR值的行数: {tr_matched} ({tr_matched/total_rows*100:.2f}%)")
    print(f"获取到EVI值的行数: {evi_matched} ({evi_matched/total_rows*100:.2f}%)")
    print(f"获取到TE值的行数: {te_matched} ({te_matched/total_rows*100:.2f}%)")
    print(f"至少获取到一个指标的行数: {any_matched} ({any_matched/total_rows*100:.2f}%)")
    
    # 显示一些匹配成功的示例
    print("\n匹配成功的示例:")
    matched_examples = result_df[result_df[['TR', 'EVI', 'TE']].notna().any(axis=1)].head(5)
    for idx, row in matched_examples.iterrows():
        print(f"  基因: {row['geneSymbol']} ({row['geneId']}) 项目: {row['projectId']}")
        print(f"    TR: {row['TR']}, EVI: {row['EVI']}, TE: {row['TE']}")
    
    # 保存结果
    print("\n保存结果...")
    result_df.to_csv('gene_with_translation_indices.csv', index=False)
    print("结果已保存到 gene_with_translation_indices.csv")
    
    # 验证保存的文件
    print("\n验证保存的文件...")
    saved_df = pd.read_csv('gene_with_translation_indices.csv')
    print(f"保存的文件包含 {len(saved_df)} 行数据")
    print(f"列名: {list(saved_df.columns)}")
    
    print("\n✅ 处理完成！")

if __name__ == '__main__':
    main()
