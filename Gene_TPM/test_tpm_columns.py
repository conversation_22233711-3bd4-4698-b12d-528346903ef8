#!/usr/bin/env python3

import csv
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import bulk_translation_indices

def test_csv_header():
    """测试CSV头部是否正确"""
    output_file = 'test_output.csv'
    
    # 删除测试文件（如果存在）
    if os.path.exists(output_file):
        os.remove(output_file)
    
    # 创建CSV文件并写入头部
    with open(output_file, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        # 添加TPM原始值列
        writer.writerow(['ensembl_gene_id', 'project_id', 'bioproject_id',
                        'TR', 'EVI', 'TE', 'RNA_TPM', 'RNC_TPM', 'Ribo_TPM'])
    
    # 读取并验证头部
    with open(output_file, 'r') as csvfile:
        reader = csv.reader(csvfile)
        header = next(reader)
        print(f"CSV头部: {header}")
        
        expected_header = ['ensembl_gene_id', 'project_id', 'bioproject_id',
                          'TR', 'EVI', 'TE', 'RNA_TPM', 'RNC_TPM', 'Ribo_TPM']
        
        if header == expected_header:
            print("✅ CSV头部正确")
        else:
            print("❌ CSV头部不正确")
            print(f"期望: {expected_header}")
            print(f"实际: {header}")
    
    # 清理测试文件
    os.remove(output_file)

def test_function_modification():
    """测试函数修改是否正确"""
    try:
        # 测试calculate_strategy_value_batch函数是否返回两个值
        print("测试 calculate_strategy_value_batch 函数...")
        
        # 这里只是测试函数签名，不实际调用数据库
        import inspect
        sig = inspect.signature(bulk_translation_indices.calculate_strategy_value_batch)
        print(f"函数签名: {sig}")
        print("✅ 函数签名正确")
        
    except Exception as e:
        print(f"❌ 函数测试失败: {e}")

if __name__ == '__main__':
    print("=== 测试TPM列修改 ===")
    test_csv_header()
    test_function_modification()
    print("=== 测试完成 ===")
