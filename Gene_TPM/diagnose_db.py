#!/usr/bin/env python3

import mysql.connector

DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def diagnose_database():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("=== 检查 gseMatch 表 ===")
        cursor.execute("SELECT COUNT(*) FROM gseMatch")
        count = cursor.fetchone()[0]
        print(f"gseMatch 表总记录数: {count}")
        
        if count > 0:
            cursor.execute("SELECT * FROM gseMatch LIMIT 3")
            rows = cursor.fetchall()
            print("gseMatch 表前3行数据:")
            for row in rows:
                print(f"  {row}")
                
            # 检查特定项目
            cursor.execute("SELECT COUNT(*) FROM gseMatch WHERE projectId = 'TEDD00005'")
            tedd5_count = cursor.fetchone()[0]
            print(f"项目 TEDD00005 的记录数: {tedd5_count}")
            
            if tedd5_count > 0:
                cursor.execute("SELECT DISTINCT strategy FROM gseMatch WHERE projectId = 'TEDD00005'")
                strategies = cursor.fetchall()
                print(f"项目 TEDD00005 的策略: {[s[0] for s in strategies]}")
                
                cursor.execute("SELECT DISTINCT sraAccession FROM gseMatch WHERE projectId = 'TEDD00005' LIMIT 5")
                sras = cursor.fetchall()
                print(f"项目 TEDD00005 的前5个SRA: {[s[0] for s in sras]}")
        
        print("\n=== 检查 Gene_TPM 表 ===")
        cursor.execute("SELECT COUNT(*) FROM Gene_TPM")
        count = cursor.fetchone()[0]
        print(f"Gene_TPM 表总记录数: {count}")
        
        if count > 0:
            cursor.execute("SELECT * FROM Gene_TPM LIMIT 3")
            rows = cursor.fetchall()
            print("Gene_TPM 表前3行数据:")
            for row in rows:
                print(f"  {row}")
                
            cursor.execute("SELECT DISTINCT sraAccession FROM Gene_TPM LIMIT 5")
            tpm_sras = cursor.fetchall()
            print(f"Gene_TPM 表前5个SRA: {[s[0] for s in tpm_sras]}")
        
        print("\n=== 检查连接查询 ===")
        cursor.execute("""
        SELECT COUNT(*) 
        FROM gseMatch g 
        JOIN Gene_TPM t ON g.sraAccession = t.sraAccession
        """)
        count = cursor.fetchone()[0]
        print(f"连接查询结果数: {count}")
        
        if count == 0:
            print("\n=== 检查SRA编号匹配情况 ===")
            cursor.execute("SELECT DISTINCT sraAccession FROM gseMatch LIMIT 5")
            gse_sra = cursor.fetchall()
            print("gseMatch 中的 SRA 样本:")
            for sra, in gse_sra:
                print(f"  '{sra}' (长度: {len(sra)})")
            
            cursor.execute("SELECT DISTINCT sraAccession FROM Gene_TPM LIMIT 5")
            tpm_sra = cursor.fetchall()
            print("Gene_TPM 中的 SRA 样本:")
            for sra, in tpm_sra:
                print(f"  '{sra}' (长度: {len(sra)})")
                
            # 检查是否有任何匹配
            if gse_sra and tpm_sra:
                test_sra = gse_sra[0][0]
                cursor.execute("SELECT COUNT(*) FROM Gene_TPM WHERE sraAccession = %s", (test_sra,))
                match_count = cursor.fetchone()[0]
                print(f"测试SRA '{test_sra}' 在Gene_TPM中的记录数: {match_count}")
        else:
            print("✅ 有匹配的SRA编号")
            
            # 测试特定项目的连接查询
            cursor.execute("""
            SELECT COUNT(*) 
            FROM gseMatch g 
            JOIN Gene_TPM t ON g.sraAccession = t.sraAccession
            WHERE g.projectId = 'TEDD00005' AND g.strategy = 'Ribo'
            """)
            test_count = cursor.fetchone()[0]
            print(f"项目 TEDD00005 策略 Ribo 的连接结果: {test_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    diagnose_database()
