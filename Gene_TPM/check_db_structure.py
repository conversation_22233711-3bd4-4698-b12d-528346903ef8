#!/usr/bin/env python3

import mysql.connector

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def check_database():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("=== 检查 gseMatch 表结构 ===")
        cursor.execute("DESCRIBE gseMatch")
        columns = cursor.fetchall()
        for column in columns:
            print(f"列名: {column[0]}, 类型: {column[1]}")
        
        print("\n=== 检查 Gene_TPM 表结构 ===")
        cursor.execute("DESCRIBE Gene_TPM")
        columns = cursor.fetchall()
        for column in columns:
            print(f"列名: {column[0]}, 类型: {column[1]}")
        
        print("\n=== 检查 gseMatch 表数据样本 ===")
        cursor.execute("SELECT * FROM gseMatch LIMIT 3")
        rows = cursor.fetchall()
        for row in rows:
            print(row)
        
        print("\n=== 检查 Gene_TPM 表数据样本 ===")
        cursor.execute("SELECT * FROM Gene_TPM LIMIT 3")
        rows = cursor.fetchall()
        for row in rows:
            print(row)
        
        print("\n=== 检查连接查询 ===")
        cursor.execute("""
        SELECT COUNT(*) 
        FROM gseMatch g 
        JOIN Gene_TPM t ON g.sraAccession = t.sraAccession
        """)
        count = cursor.fetchone()[0]
        print(f"连接查询结果数: {count}")
        
        if count == 0:
            print("\n=== 检查SRA编号匹配情况 ===")
            cursor.execute("SELECT DISTINCT sraAccession FROM gseMatch LIMIT 5")
            gse_sra = cursor.fetchall()
            print("gseMatch 中的 SRA 样本:")
            for sra, in gse_sra:
                print(f"  {sra}")
            
            cursor.execute("SELECT DISTINCT sraAccession FROM Gene_TPM LIMIT 5")
            tpm_sra = cursor.fetchall()
            print("Gene_TPM 中的 SRA 样本:")
            for sra, in tpm_sra:
                print(f"  {sra}")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    check_database()
