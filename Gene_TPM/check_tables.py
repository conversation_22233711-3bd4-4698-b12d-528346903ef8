#!/usr/bin/env python3

import mysql.connector

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def check_tables():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("=== 检查 Gene_TPM 表 ===")
        cursor.execute("SELECT COUNT(*) FROM Gene_TPM")
        count = cursor.fetchone()[0]
        print(f"Gene_TPM 表总记录数: {count}")
        
        if count > 0:
            cursor.execute("SELECT DISTINCT sraAccession FROM Gene_TPM LIMIT 10")
            sra_list = cursor.fetchall()
            print("Gene_TPM 表中的前10个SRA编号:")
            for sra, in sra_list:
                print(f"  {sra}")
        else:
            print("❌ Gene_TPM 表是空的！")
        
        print("\n=== 检查 gseMatch 表 ===")
        cursor.execute("SELECT COUNT(*) FROM gseMatch")
        count = cursor.fetchone()[0]
        print(f"gseMatch 表总记录数: {count}")
        
        if count > 0:
            cursor.execute("SELECT DISTINCT sraAccession FROM gseMatch WHERE projectId = 'TEDD00001' LIMIT 10")
            sra_list = cursor.fetchall()
            print("gseMatch 表中 TEDD00001 项目的前10个SRA编号:")
            for sra, in sra_list:
                print(f"  {sra}")
        else:
            print("❌ gseMatch 表是空的！")
        
        print("\n=== 检查 SRA 编号匹配情况 ===")
        cursor.execute("""
        SELECT 
            (SELECT COUNT(DISTINCT sraAccession) FROM gseMatch) as gse_count,
            (SELECT COUNT(DISTINCT sraAccession) FROM Gene_TPM) as tpm_count,
            (SELECT COUNT(DISTINCT g.sraAccession) 
             FROM gseMatch g 
             INNER JOIN Gene_TPM t ON g.sraAccession = t.sraAccession) as match_count
        """)
        
        result = cursor.fetchone()
        gse_count, tpm_count, match_count = result
        
        print(f"gseMatch 表中唯一SRA数: {gse_count}")
        print(f"Gene_TPM 表中唯一SRA数: {tpm_count}")
        print(f"匹配的SRA数: {match_count}")
        
        if match_count == 0:
            print("❌ 没有匹配的SRA编号！")
            
            # 检查具体的SRA编号格式
            print("\n=== 检查SRA编号格式差异 ===")
            cursor.execute("SELECT sraAccession FROM gseMatch WHERE projectId = 'TEDD00001' LIMIT 3")
            gse_sra = cursor.fetchall()
            print("gseMatch 中的SRA样本:")
            for sra, in gse_sra:
                print(f"  '{sra}' (长度: {len(sra)})")
            
            cursor.execute("SELECT sraAccession FROM Gene_TPM LIMIT 3")
            tpm_sra = cursor.fetchall()
            print("Gene_TPM 中的SRA样本:")
            for sra, in tpm_sra:
                print(f"  '{sra}' (长度: {len(sra)})")
        else:
            print("✅ 有匹配的SRA编号")
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_tables()
