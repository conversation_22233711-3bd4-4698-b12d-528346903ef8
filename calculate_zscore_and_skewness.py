#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计算翻译效率指标的Z-score、分位数等级和偏度
基于R代码逻辑的Python实现
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import os
import warnings
warnings.filterwarnings('ignore')

class TranslationIndicesAnalyzer:
    """翻译效率指标分析器"""
    
    def __init__(self, input_file, output_dir=None):
        """
        初始化分析器
        
        Args:
            input_file: 输入CSV文件路径
            output_dir: 输出目录，如果为None则使用输入文件名创建目录
        """
        self.input_file = input_file
        self.df = None
        self.results = {}
        
        # 设置输出目录
        if output_dir is None:
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            self.output_dir = f"analysis_output_{base_name}"
        else:
            self.output_dir = output_dir
            
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 设置中文字体（如果需要）
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
    def load_data(self):
        """加载数据"""
        print(f"正在加载数据: {self.input_file}")
        self.df = pd.read_csv(self.input_file)
        print(f"原始数据行数: {len(self.df)}")
        return self.df
    
    def calculate_metrics_for_column(self, column_name):
        """
        为指定列计算指标
        
        Args:
            column_name: 列名 ('TE', 'TR', 'EVI')
            
        Returns:
            dict: 包含处理结果的字典
        """
        print(f"\n处理 {column_name} 数据...")
        
        # 获取对应的列名
        zscore_col = f"{column_name}_ZSCORE"
        level_col = f"{column_name}_LEVEL"
        
        # 创建工作副本
        work_df = self.df.copy()
        
        # 过滤有效数据：不为NA且大于0
        valid_mask = (work_df[column_name].notna()) & (work_df[column_name] > 0)
        valid_data = work_df[valid_mask].copy()
        
        if len(valid_data) == 0:
            print(f"{column_name} 没有有效数据")
            return None
            
        print(f"{column_name} 有效数据行数: {len(valid_data)}")
        
        # 计算log2变换
        valid_data[f'log2_{column_name}'] = np.log2(valid_data[column_name])
        
        # 计算z-score
        log_values = valid_data[f'log2_{column_name}']
        z_scores = stats.zscore(log_values, nan_policy='omit')
        valid_data['zscore'] = z_scores
        
        # 移除z-score中的NA值
        valid_data = valid_data.dropna(subset=['zscore'])
        print(f"{column_name} 计算z-score后数据行数: {len(valid_data)}")
        
        # 计算分位数
        quartiles = np.percentile(valid_data['zscore'], [25, 50, 75])
        
        # 分组
        def assign_quantile(zscore):
            if zscore <= quartiles[0]:
                return 'Q1'
            elif zscore <= quartiles[1]:
                return 'Q2'
            elif zscore <= quartiles[2]:
                return 'Q3'
            else:
                return 'Q4'
        
        valid_data['quantile_group'] = valid_data['zscore'].apply(assign_quantile)
        
        # 计算偏度
        skewness = stats.skew(valid_data['zscore'])
        
        # 更新原始数据框中的ZSCORE和LEVEL列
        # 首先清空这些列
        work_df[zscore_col] = np.nan
        work_df[level_col] = ''
        
        # 更新有效数据的值
        for idx, row in valid_data.iterrows():
            work_df.loc[idx, zscore_col] = row['zscore']
            work_df.loc[idx, level_col] = row['quantile_group']
        
        # 统计各组数量
        group_counts = valid_data['quantile_group'].value_counts().sort_index()
        print(f"{column_name} 各分组数量:")
        for group, count in group_counts.items():
            print(f"  {group}: {count}")
        
        # 保存结果
        result = {
            'column_name': column_name,
            'valid_data': valid_data,
            'total_count': len(valid_data),
            'skewness': skewness,
            'quartiles': quartiles,
            'group_counts': group_counts,
            'updated_df': work_df
        }
        
        print(f"{column_name} 偏度 (skewness): {skewness:.4f}")
        
        return result
    
    def create_histogram(self, result):
        """创建直方图"""
        column_name = result['column_name']
        valid_data = result['valid_data']
        skewness = result['skewness']
        total_count = result['total_count']
        
        # 颜色映射
        color_map = {'Q1': '#D73027', 'Q2': '#FC8D59', 'Q3': '#91BFDB', 'Q4': '#4575B4'}
        
        # 创建图形
        plt.figure(figsize=(10, 6))
        
        # 绘制直方图
        binwidth = 0.2
        bins = np.arange(valid_data['zscore'].min(), valid_data['zscore'].max() + binwidth, binwidth)
        
        for group in ['Q1', 'Q2', 'Q3', 'Q4']:
            group_data = valid_data[valid_data['quantile_group'] == group]['zscore']
            if len(group_data) > 0:
                plt.hist(group_data, bins=bins, alpha=0.7, label=group, 
                        color=color_map[group], edgecolor='black', linewidth=0.5)
        
        # 添加0σ竖线
        plt.axvline(x=0, linestyle='dashed', color='black', linewidth=1)
        
        # 设置标签和标题
        plt.xlabel(f'log₂ {column_name} (z-score)', fontsize=14)
        plt.ylabel('Frequency', fontsize=14)
        plt.title(f'{column_name} Z-score Distribution', fontsize=16)
        plt.legend(title='Quantile')
        
        # 添加偏度和样本量信息
        y_max = plt.ylim()[1]
        x_min = plt.xlim()[0]
        plt.text(x_min + 0.5, y_max * 0.9, f'skewness = {skewness:.3f}', 
                fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        plt.text(x_min + 0.5, y_max * 0.8, f'n = {total_count:,}', 
                fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        # 设置x轴刻度
        x_ticks = np.arange(-6, 7, 2)
        plt.xticks(x_ticks, [f'{x}σ' for x in x_ticks])
        
        plt.tight_layout()
        
        # 保存图像
        output_path = os.path.join(self.output_dir, f'{column_name}_zscore_histogram.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"已保存 {column_name} 直方图: {output_path}")
    
    def save_quantile_data(self, result):
        """保存各分位数组的数据"""
        column_name = result['column_name']
        valid_data = result['valid_data']
        
        # 为每个分组保存transcript_id和zscore
        for group in ['Q1', 'Q2', 'Q3', 'Q4']:
            group_data = valid_data[valid_data['quantile_group'] == group]
            if len(group_data) > 0:
                output_data = group_data[['TRANSCRIPT_ID', 'zscore']].copy()
                output_path = os.path.join(self.output_dir, f'{column_name}_{group}_transcripts_zscore.txt')
                output_data.to_csv(output_path, sep='\t', index=False)
                print(f"已保存 {column_name} {group} 数据: {output_path}")
    
    def process_all_metrics(self):
        """处理所有指标"""
        # 加载数据
        self.load_data()
        
        # 处理三个指标
        metrics = ['TE', 'TR', 'EVI']
        updated_df = self.df.copy()
        
        for metric in metrics:
            if metric in self.df.columns:
                result = self.calculate_metrics_for_column(metric)
                if result is not None:
                    self.results[metric] = result
                    # 更新数据框
                    updated_df = result['updated_df']
                    # 创建图表
                    self.create_histogram(result)
                    # 保存分组数据
                    self.save_quantile_data(result)
            else:
                print(f"警告: 列 {metric} 不存在于数据中")
        
        # 保存更新后的完整数据
        output_path = os.path.join(self.output_dir, 'updated_data_with_zscore_level.csv')
        updated_df.to_csv(output_path, index=False)
        print(f"\n已保存更新后的完整数据: {output_path}")
        
        # 生成总结报告
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """生成总结报告"""
        report_path = os.path.join(self.output_dir, 'analysis_summary.txt')
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("翻译效率指标分析总结报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"输入文件: {self.input_file}\n")
            f.write(f"原始数据总行数: {len(self.df)}\n\n")
            
            for metric, result in self.results.items():
                f.write(f"{metric} 指标分析结果:\n")
                f.write("-" * 30 + "\n")
                f.write(f"有效数据行数: {result['total_count']}\n")
                f.write(f"偏度 (skewness): {result['skewness']:.4f}\n")
                f.write(f"分位数 (25%, 50%, 75%): {result['quartiles']}\n")
                f.write("各分组数量:\n")
                for group, count in result['group_counts'].items():
                    f.write(f"  {group}: {count}\n")
                f.write("\n")
        
        print(f"已生成分析报告: {report_path}")

def main():
    """主函数"""
    # 示例使用
    input_file = "skewness_test/TEDD00001_transcripts.csv"
    
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        return
    
    # 创建分析器并处理数据
    analyzer = TranslationIndicesAnalyzer(input_file)
    analyzer.process_all_metrics()
    
    print("\n分析完成！请查看输出目录中的结果文件。")

if __name__ == "__main__":
    main() 