#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量处理 transcriptLength/transcript_locations_final_1.csv 文件
通过访问 source_url 获取 location_text 和 location_url
分批处理，避免对服务器造成过大压力
"""

import csv
import requests
import re
import time
import logging
from datetime import datetime
import random
import sys
from urllib.parse import urljoin

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extract_transcript_locations_batch.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

INPUT_FILE = 'transcript_locations_final_1.csv'
OUTPUT_FILE = 'transcript_locations_final_1_updated.csv'
BATCH_SIZE = 50  # 每批处理50个
DELAY_BETWEEN_REQUESTS = (2, 4)  # 请求间隔2-4秒
DELAY_BETWEEN_BATCHES = (10, 20)  # 批次间隔10-20秒

# 请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

def extract_location_from_html(html_content, base_url):
    """从HTML内容中提取位置信息和URL"""
    try:
        # 使用正则表达式查找Location行的内容
        # 匹配模式：<div class="lhs">Location</div><div class="rhs"><p><a href="...">Chromosome X: 103,776,874-103,786,583</a>
        location_pattern = r'<div class="lhs">Location</div>\s*<div class="rhs">\s*<p>\s*<a[^>]+href="([^"]+)"[^>]*>([^<]+)</a>'

        match = re.search(location_pattern, html_content, re.IGNORECASE | re.DOTALL)
        if match:
            location_url = match.group(1).strip()
            location_text = match.group(2).strip()

            # 移除" forward strand."或" reverse strand."后缀
            location_text = re.sub(r'\s+(forward|reverse)\s+strand\.?$', '', location_text)

            # 如果URL是相对路径，转换为绝对路径
            if location_url.startswith('/'):
                location_url = urljoin(base_url, location_url)

            return location_text, location_url

        # 备用模式：查找包含染色体信息的文本
        chromosome_pattern = r'Chromosome\s+[^:]+:\s*[\d,]+-[\d,]+'
        match = re.search(chromosome_pattern, html_content)
        if match:
            location_text = match.group(0)
            return location_text, ""

        return None, None

    except Exception as e:
        logging.error(f"解析HTML时出错: {e}")
        return None, None

def get_location_from_url(url, transcript_id, max_retries=3):
    """从URL获取位置信息和URL"""
    for attempt in range(max_retries):
        try:
            # 添加随机延迟，避免请求过于频繁
            delay = random.uniform(*DELAY_BETWEEN_REQUESTS)
            time.sleep(delay)
            
            logging.info(f"正在请求 {transcript_id} (尝试 {attempt + 1}/{max_retries})")
            response = requests.get(url, headers=HEADERS, timeout=30)
            response.raise_for_status()
            
            location_text, location_url = extract_location_from_html(response.text, url)
            
            if location_text:
                logging.info(f"成功获取 {transcript_id} 的位置信息: {location_text}")
                return location_text, location_url, "success"
            else:
                logging.warning(f"未能从 {transcript_id} 的页面中提取位置信息")
                return None, None, "no_location_found"
                
        except requests.exceptions.RequestException as e:
            logging.error(f"请求 {transcript_id} 时出错 (尝试 {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                # 重试前等待更长时间
                retry_delay = random.uniform(5, 10)
                time.sleep(retry_delay)
            else:
                return None, None, "request_failed"
        except Exception as e:
            logging.error(f"处理 {transcript_id} 时出错: {e}")
            return None, None, "processing_error"
    
    return None, None, "max_retries_exceeded"

def save_progress(rows, filename):
    """保存当前进度"""
    try:
        with open(filename, 'w', encoding='utf-8', newline='') as f:
            fieldnames = ['transcript_id', 'location_text', 'location_url', 'status', 'source_url', 'processed_at', 'attempts']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)
        logging.info(f"进度已保存到 {filename}")
    except Exception as e:
        logging.error(f"保存进度时出错: {e}")

def main():
    """主函数 - 批量处理"""
    try:
        logging.info(f'开始批量处理文件: {INPUT_FILE}')
        
        # 读取CSV文件
        rows = []
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        
        total_rows = len(rows)
        logging.info(f'读取了 {total_rows} 条记录')
        
        # 统计信息
        processed = 0
        success_count = 0
        failed_count = 0
        skipped_count = 0
        
        # 分批处理
        for batch_start in range(0, total_rows, BATCH_SIZE):
            batch_end = min(batch_start + BATCH_SIZE, total_rows)
            batch_rows = rows[batch_start:batch_end]
            
            logging.info(f"处理批次 {batch_start//BATCH_SIZE + 1}: 记录 {batch_start + 1}-{batch_end}")
            
            # 处理当前批次
            for i, row in enumerate(batch_rows):
                transcript_id = row['transcript_id']
                source_url = row['source_url']
                current_status = row.get('status', '')
                
                # 如果已经成功处理过，跳过
                if current_status == 'success' and row.get('location_text'):
                    logging.info(f"跳过已处理的 {transcript_id}")
                    skipped_count += 1
                    continue
                
                # 获取位置信息
                location_text, location_url, status = get_location_from_url(source_url, transcript_id)
                
                # 更新记录
                if location_text:
                    row['location_text'] = location_text
                    row['location_url'] = location_url or ""
                    row['status'] = status
                    success_count += 1
                else:
                    row['location_text'] = ""
                    row['location_url'] = ""
                    row['status'] = status
                    failed_count += 1
                
                row['processed_at'] = datetime.now().isoformat()
                row['attempts'] = str(int(row.get('attempts', 0)) + 1)
                
                processed += 1
                
                # 每处理10个记录显示进度
                if processed % 10 == 0:
                    logging.info(f"总进度: {processed}/{total_rows} (成功: {success_count}, 失败: {failed_count}, 跳过: {skipped_count})")
            
            # 每批次后保存进度
            save_progress(rows, OUTPUT_FILE)
            
            # 批次间延迟
            if batch_end < total_rows:
                batch_delay = random.uniform(*DELAY_BETWEEN_BATCHES)
                logging.info(f"批次完成，等待 {batch_delay:.1f} 秒后继续...")
                time.sleep(batch_delay)
        
        # 最终统计
        logging.info('处理完成！')
        logging.info(f'总记录数: {total_rows}')
        logging.info(f'成功处理: {success_count}')
        logging.info(f'处理失败: {failed_count}')
        logging.info(f'跳过记录: {skipped_count}')
        logging.info(f'成功率: {(success_count/(total_rows-skipped_count)*100):.2f}%')
        
        # 显示成功示例
        success_examples = [row for row in rows if row.get('status') == 'success' and row.get('location_text')][:5]
        if success_examples:
            logging.info('成功处理的示例:')
            for example in success_examples:
                logging.info(f"  {example['transcript_id']}: {example['location_text']}")
        
    except KeyboardInterrupt:
        logging.info("用户中断处理，保存当前进度...")
        save_progress(rows, OUTPUT_FILE)
        sys.exit(1)
    except Exception as e:
        logging.error(f'处理过程中发生错误: {str(e)}')
        save_progress(rows, OUTPUT_FILE)

if __name__ == '__main__':
    main()
