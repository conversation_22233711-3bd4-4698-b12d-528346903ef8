#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试提取转录本位置信息 - 只处理前5条记录
"""

import csv
import requests
import re
import time
import logging
from datetime import datetime
import random
from urllib.parse import urljoin

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extract_transcript_locations_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

INPUT_FILE = 'transcript_locations_final_1.csv'
OUTPUT_FILE = 'transcript_locations_final_1_test.csv'

# 请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

def extract_location_from_html(html_content, base_url):
    """从HTML内容中提取位置信息和URL"""
    try:
        # 使用正则表达式查找Location行的内容
        # 匹配模式：<div class="lhs">Location</div><div class="rhs"><p><a href="...">Chromosome X: 103,776,874-103,786,583</a>
        location_pattern = r'<div class="lhs">Location</div>\s*<div class="rhs">\s*<p>\s*<a[^>]+href="([^"]+)"[^>]*>([^<]+)</a>'

        match = re.search(location_pattern, html_content, re.IGNORECASE | re.DOTALL)
        if match:
            location_url = match.group(1).strip()
            location_text = match.group(2).strip()

            # 移除" forward strand."或" reverse strand."后缀
            location_text = re.sub(r'\s+(forward|reverse)\s+strand\.?$', '', location_text)

            # 如果URL是相对路径，转换为绝对路径
            if location_url.startswith('/'):
                location_url = urljoin(base_url, location_url)

            return location_text, location_url

        # 备用模式：查找包含染色体信息的文本
        chromosome_pattern = r'Chromosome\s+[^:]+:\s*[\d,]+-[\d,]+'
        match = re.search(chromosome_pattern, html_content)
        if match:
            location_text = match.group(0)
            return location_text, ""

        return None, None

    except Exception as e:
        logging.error(f"解析HTML时出错: {e}")
        return None, None

def get_location_from_url(url, transcript_id):
    """从URL获取位置信息和URL"""
    try:
        # 添加随机延迟，避免请求过于频繁
        time.sleep(random.uniform(1, 2))
        
        logging.info(f"正在请求: {url}")
        response = requests.get(url, headers=HEADERS, timeout=30)
        response.raise_for_status()
        
        location_text, location_url = extract_location_from_html(response.text, url)
        
        if location_text:
            logging.info(f"成功获取 {transcript_id} 的位置信息: {location_text}")
            if location_url:
                logging.info(f"位置URL: {location_url}")
            return location_text, location_url, "success"
        else:
            logging.warning(f"未能从 {transcript_id} 的页面中提取位置信息")
            return None, None, "no_location_found"
            
    except requests.exceptions.RequestException as e:
        logging.error(f"请求 {transcript_id} 时出错: {e}")
        return None, None, "request_failed"
    except Exception as e:
        logging.error(f"处理 {transcript_id} 时出错: {e}")
        return None, None, "processing_error"

def main():
    """主函数 - 只处理前5条记录"""
    try:
        logging.info(f'开始测试处理文件: {INPUT_FILE}')
        
        # 读取CSV文件
        rows = []
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)[:5]  # 只取前5条记录
        
        logging.info(f'读取了前 {len(rows)} 条记录进行测试')
        
        # 处理每条记录
        for i, row in enumerate(rows):
            transcript_id = row['transcript_id']
            source_url = row['source_url']
            
            logging.info(f"处理第 {i+1}/{len(rows)} 条记录: {transcript_id}")
            
            # 获取位置信息
            location_text, location_url, status = get_location_from_url(source_url, transcript_id)
            
            # 更新记录
            if location_text:
                row['location_text'] = location_text
                row['location_url'] = location_url or ""
                row['status'] = status
            else:
                row['location_text'] = ""
                row['location_url'] = ""
                row['status'] = status
            
            row['processed_at'] = datetime.now().isoformat()
            row['attempts'] = str(int(row.get('attempts', 0)) + 1)
        
        # 保存结果
        logging.info(f'保存测试结果到: {OUTPUT_FILE}')
        with open(OUTPUT_FILE, 'w', encoding='utf-8', newline='') as f:
            fieldnames = ['transcript_id', 'location_text', 'location_url', 'status', 'source_url', 'processed_at', 'attempts']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)
        
        # 显示结果
        logging.info('测试完成！结果:')
        for row in rows:
            logging.info(f"  {row['transcript_id']}: {row['status']} - {row['location_text']}")
        
    except Exception as e:
        logging.error(f'测试过程中发生错误: {str(e)}')

if __name__ == '__main__':
    main()
