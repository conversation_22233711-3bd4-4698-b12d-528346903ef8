# 转录本位置信息提取工具

本目录包含用于从 Ensembl 网站提取转录本位置信息的脚本。

## 文件说明

### 数据文件
- `transcript_locations_final_1.csv` - 输入文件，包含转录本ID和source_url
- `transcript_locations_final_1_test.csv` - 测试输出文件（前5条记录）
- `transcript_locations_final_1_updated.csv` - 完整处理后的输出文件

### 脚本文件
- `extract_transcript_locations_test.py` - 测试脚本（处理前5条记录）
- `extract_transcript_locations_batch.py` - 批量处理脚本（处理完整文件）

### 日志文件
- `extract_transcript_locations_test.log` - 测试脚本日志
- `extract_transcript_locations_batch.log` - 批量处理脚本日志

## 使用方法

### 1. 测试运行（推荐先运行）
```bash
cd transcriptLength
python extract_transcript_locations_test.py
```

### 2. 批量处理
```bash
cd transcriptLength
python extract_transcript_locations_batch.py
```

## 功能特性

### 提取信息
- **location_text**: 染色体位置信息（如 "Chromosome X: 103,776,874-103,786,583"）
- **location_url**: Ensembl位置查看链接（完整URL）

### 安全机制
- 🕐 请求间隔：2-4秒随机延迟
- 🕐 批次间隔：10-20秒随机延迟（每50个记录一批）
- 🔄 自动重试：最多3次重试
- 💾 进度保存：每批次后自动保存
- ⏸️ 中断恢复：支持Ctrl+C中断并保存进度

### 处理逻辑
- 跳过已成功处理的记录
- 智能HTML解析
- 错误处理和状态记录
- 详细的日志记录

## 输出格式

处理后的CSV文件包含以下列：
- `transcript_id`: 转录本ID
- `location_text`: 位置文本
- `location_url`: 位置查看链接
- `status`: 处理状态（success/failed等）
- `source_url`: 原始Ensembl链接
- `processed_at`: 处理时间戳
- `attempts`: 尝试次数

## 示例输出

```csv
transcript_id,location_text,location_url,status,source_url,processed_at,attempts
ENST00000464776,"Chromosome X: 103,776,874-103,786,583",https://www.ensembl.org/Homo_sapiens/Location/View?db=core;g=ENSG00000123560;r=X:103776874-103786583;t=ENST00000464776,success,https://www.ensembl.org/Homo_sapiens/Transcript/Summary?t=ENST00000464776,2025-07-11T11:26:40.820297,1
```

## 注意事项

1. **网络稳定性**: 确保网络连接稳定
2. **处理时间**: 完整文件可能需要较长时间
3. **服务器友好**: 脚本已设置合理延迟，避免对Ensembl服务器造成压力
4. **中断恢复**: 可以随时中断并从上次位置继续

## 依赖包

确保安装以下Python包：
```bash
pip install requests beautifulsoup4
```
