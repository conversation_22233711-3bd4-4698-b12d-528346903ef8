#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新 transcript_locations_final.csv 文件中的位置信息
如果 location_text 为空，从 transcript_locations_final_1_updated.csv 中查找并更新对应的数据
"""

import csv
import logging
from datetime import datetime
import pandas as pd

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_location_data.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# 文件路径
MAIN_FILE = 'transcript_locations_final.csv'
UPDATE_FILE = 'transcript_locations_final_1_updated.csv'
OUTPUT_FILE = 'transcript_locations_final_updated.csv'

# 需要更新的列
UPDATE_COLUMNS = ['location_text', 'location_url', 'status', 'source_url', 'processed_at', 'attempts']

def load_update_data(update_file):
    """加载更新数据文件，创建transcript_id到数据的映射"""
    try:
        logging.info(f'加载更新数据文件: {update_file}')
        
        update_data = {}
        with open(update_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                transcript_id = row['transcript_id']
                # 只保存有location_text的记录
                if row.get('location_text') and row.get('location_text').strip():
                    update_data[transcript_id] = row
        
        logging.info(f'加载了 {len(update_data)} 条有效更新记录')
        return update_data
        
    except Exception as e:
        logging.error(f'加载更新数据文件时出错: {e}')
        return {}

def update_location_data():
    """更新位置数据"""
    try:
        logging.info('开始更新位置数据...')
        
        # 加载更新数据
        update_data = load_update_data(UPDATE_FILE)
        if not update_data:
            logging.error('没有可用的更新数据')
            return False
        
        # 读取主文件
        logging.info(f'读取主文件: {MAIN_FILE}')
        main_rows = []
        with open(MAIN_FILE, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            fieldnames = reader.fieldnames
            main_rows = list(reader)
        
        logging.info(f'主文件包含 {len(main_rows)} 条记录')
        
        # 确保所有需要的列都存在
        for col in UPDATE_COLUMNS:
            if col not in fieldnames:
                fieldnames.append(col)
        
        # 统计信息
        updated_count = 0
        empty_count = 0
        already_filled_count = 0
        not_found_count = 0
        
        # 处理每一行
        for row in main_rows:
            transcript_id = row.get('transcript_id', '').strip()
            current_location_text = row.get('location_text', '').strip()
            
            # 检查location_text是否为空
            if not current_location_text:
                empty_count += 1
                
                # 在更新数据中查找
                if transcript_id in update_data:
                    update_row = update_data[transcript_id]
                    
                    # 更新指定的列
                    for col in UPDATE_COLUMNS:
                        if col in update_row:
                            row[col] = update_row[col]
                    
                    updated_count += 1
                    logging.info(f'更新 {transcript_id}: {update_row.get("location_text", "N/A")}')
                else:
                    not_found_count += 1
                    logging.warning(f'未找到 {transcript_id} 的更新数据')
            else:
                already_filled_count += 1
        
        # 保存更新后的文件
        logging.info(f'保存更新后的文件到: {OUTPUT_FILE}')
        with open(OUTPUT_FILE, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(main_rows)
        
        # 显示统计信息
        logging.info('更新完成！统计信息:')
        logging.info(f'总记录数: {len(main_rows)}')
        logging.info(f'location_text为空的记录: {empty_count}')
        logging.info(f'成功更新的记录: {updated_count}')
        logging.info(f'已有数据的记录: {already_filled_count}')
        logging.info(f'未找到更新数据的记录: {not_found_count}')
        logging.info(f'更新成功率: {(updated_count/empty_count*100):.2f}%' if empty_count > 0 else '无需更新')
        
        # 显示一些更新示例
        if updated_count > 0:
            logging.info('更新示例:')
            example_count = 0
            for row in main_rows:
                if row.get('location_text') and row.get('status') == 'success':
                    logging.info(f'  {row["transcript_id"]}: {row["location_text"]}')
                    example_count += 1
                    if example_count >= 5:
                        break
        
        return True
        
    except Exception as e:
        logging.error(f'更新过程中发生错误: {e}')
        return False

def validate_files():
    """验证文件是否存在"""
    import os
    
    if not os.path.exists(MAIN_FILE):
        logging.error(f'主文件不存在: {MAIN_FILE}')
        return False
    
    if not os.path.exists(UPDATE_FILE):
        logging.error(f'更新文件不存在: {UPDATE_FILE}')
        return False
    
    return True

def main():
    """主函数"""
    logging.info('开始位置数据更新任务...')
    
    # 验证文件
    if not validate_files():
        logging.error('文件验证失败，退出程序')
        return
    
    # 执行更新
    success = update_location_data()
    
    if success:
        logging.info('位置数据更新任务完成！')
        logging.info(f'输出文件: {OUTPUT_FILE}')
    else:
        logging.error('位置数据更新任务失败！')

if __name__ == '__main__':
    main()
