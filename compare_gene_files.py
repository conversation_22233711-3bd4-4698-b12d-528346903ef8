#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比gene/gene.csv和gene/gene_count_by_project_results_with_chromosome.csv文件
找出第二个文件比第一个文件多的geneId和projectId组合
"""

import pandas as pd
import sys

def compare_gene_files():
    """
    对比两个基因文件，找出差异的组合
    """
    print("开始读取文件...")
    
    # 读取第一个文件 (gene.csv)
    try:
        df1 = pd.read_csv('gene/gene.csv')
        print(f"成功读取 gene/gene.csv，共 {len(df1)} 行")
    except Exception as e:
        print(f"读取 gene/gene.csv 失败: {e}")
        return
    
    # 读取第二个文件 (gene_count_by_project_results_with_chromosome.csv)
    try:
        df2 = pd.read_csv('gene/gene_count_by_project_results_with_chromosome.csv')
        print(f"成功读取 gene/gene_count_by_project_results_with_chromosome.csv，共 {len(df2)} 行")
    except Exception as e:
        print(f"读取 gene/gene_count_by_project_results_with_chromosome.csv 失败: {e}")
        return
    
    # 检查列名
    print("\n第一个文件的列名:")
    print(df1.columns.tolist())
    print("\n第二个文件的列名:")
    print(df2.columns.tolist())

    # 统计两个文件中 geneId 的唯一值数量
    print("\n=== geneId 唯一值统计 ===")
    unique_genes_df1 = df1['geneId'].nunique()
    unique_genes_df2 = df2['geneId'].nunique()

    print(f"第一个文件 (gene.csv) 中唯一的 geneId 数量: {unique_genes_df1}")
    print(f"第二个文件 (gene_count_by_project_results_with_chromosome.csv) 中唯一的 geneId 数量: {unique_genes_df2}")
    print(f"两个文件 geneId 数量差异: {unique_genes_df2 - unique_genes_df1}")

    # 分析 geneId 的交集和差集
    genes_set1 = set(df1['geneId'].unique())
    genes_set2 = set(df2['geneId'].unique())

    common_genes = genes_set1 & genes_set2
    only_in_df1 = genes_set1 - genes_set2
    only_in_df2 = genes_set2 - genes_set1

    print(f"两个文件共同的 geneId 数量: {len(common_genes)}")
    print(f"仅在第一个文件中的 geneId 数量: {len(only_in_df1)}")
    print(f"仅在第二个文件中的 geneId 数量: {len(only_in_df2)}")

    if only_in_df1:
        print(f"仅在第一个文件中的 geneId 示例 (前10个): {list(only_in_df1)[:10]}")
    if only_in_df2:
        print(f"仅在第二个文件中的 geneId 示例 (前10个): {list(only_in_df2)[:10]}")

    # 创建组合键 (geneId + projectId)
    print("\n=== geneId-projectId 组合统计 ===")
    df1_combinations = df1[['geneId', 'projectId']].drop_duplicates()
    df2_combinations = df2[['geneId', 'projectId']].drop_duplicates()

    print(f"第一个文件中唯一的 geneId-projectId 组合数: {len(df1_combinations)}")
    print(f"第二个文件中唯一的 geneId-projectId 组合数: {len(df2_combinations)}")
    
    # 创建组合字符串用于比较
    df1_combinations['combination'] = df1_combinations['geneId'].astype(str) + '_' + df1_combinations['projectId'].astype(str)
    df2_combinations['combination'] = df2_combinations['geneId'].astype(str) + '_' + df2_combinations['projectId'].astype(str)
    
    # 找出第二个文件比第一个文件多的组合
    set1 = set(df1_combinations['combination'])
    set2 = set(df2_combinations['combination'])
    
    # 第二个文件比第一个文件多的组合
    extra_in_df2 = set2 - set1
    # 第一个文件比第二个文件多的组合
    extra_in_df1 = set1 - set2
    
    print(f"\n第二个文件比第一个文件多的组合数: {len(extra_in_df2)}")
    print(f"第一个文件比第二个文件多的组合数: {len(extra_in_df1)}")
    
    # 处理第二个文件多出的组合
    if extra_in_df2:
        print(f"\n正在处理第二个文件多出的 {len(extra_in_df2)} 个组合...")
        
        # 从组合字符串中分离出geneId和projectId
        extra_combinations_df2 = []
        for combo in extra_in_df2:
            gene_id, project_id = combo.split('_', 1)
            extra_combinations_df2.append({'geneId': gene_id, 'projectId': project_id})
        
        extra_df2 = pd.DataFrame(extra_combinations_df2)
        
        # 从第二个文件中获取这些组合的完整信息
        result_df = pd.merge(extra_df2, df2, on=['geneId', 'projectId'], how='left')
        
        # 保存结果
        output_file = 'gene_combinations_extra_in_second_file.csv'
        result_df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"第二个文件多出的组合已保存到: {output_file}")
        
        # 显示前几行作为预览
        print(f"\n预览前5行:")
        print(result_df.head())
        
        # 统计信息
        print(f"\n统计信息:")
        print(f"总共多出的组合数: {len(result_df)}")
        if 'geneSymbol' in result_df.columns:
            print(f"涉及的唯一基因数: {result_df['geneSymbol'].nunique()}")
        print(f"涉及的唯一项目数: {result_df['projectId'].nunique()}")
    
    # 处理第一个文件多出的组合（如果有的话）
    if extra_in_df1:
        print(f"\n正在处理第一个文件多出的 {len(extra_in_df1)} 个组合...")
        
        extra_combinations_df1 = []
        for combo in extra_in_df1:
            gene_id, project_id = combo.split('_', 1)
            extra_combinations_df1.append({'geneId': gene_id, 'projectId': project_id})
        
        extra_df1 = pd.DataFrame(extra_combinations_df1)
        
        # 从第一个文件中获取这些组合的完整信息
        result_df1 = pd.merge(extra_df1, df1, on=['geneId', 'projectId'], how='left')
        
        # 保存结果
        output_file1 = 'gene_combinations_extra_in_first_file.csv'
        result_df1.to_csv(output_file1, index=False, encoding='utf-8')
        print(f"第一个文件多出的组合已保存到: {output_file1}")

if __name__ == "__main__":
    compare_gene_files()
