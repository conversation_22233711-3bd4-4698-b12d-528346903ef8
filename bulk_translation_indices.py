import os
import csv
import time
import pandas as pd
import mysql.connector
from mysql.connector import Error
import concurrent.futures
from tqdm import tqdm
import numpy as np
from mysql.connector.pooling import MySQLConnectionPool
from functools import lru_cache
import pickle
import threading
import gc
import queue

# 数据库配置信息 - 移除重复的连接池参数
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306,
    'buffered': True  # 增加缓冲查询选项
    # 移除 'pool_size' 和 'pool_name', 这些将作为单独参数传递
}

# 全局变量
connection_pool = None     # 连接池
thread_local = threading.local()  # 线程本地存储，每个线程使用独立的连接
connection_queue = queue.Queue()  # 用于手动管理连接的队列
connection_lock = threading.Lock()  # 连接获取锁，防止并发问题

# 全局缓存
PROJECT_STRATEGY_CACHE = {}
PROJECT_INFO_CACHE = {}
PROJECT_GROUP_CACHE = {}  # 项目分组信息缓存

# 配置参数
MAX_WORKERS = 10          # 降低工作线程数，避免连接池耗尽
BATCH_SIZE = 10000        # 每批处理的转录本数
MAX_PROJECTS_PER_ROUND = 5 # 每轮处理的项目数，减少以降低资源消耗
BUFFER_SIZE = 10000       # 结果缓冲区大小
GSE_MATCH_FILE = 'GSE_match_new.csv'  # GSE匹配文件路径
POOL_SIZE = 30            # 连接池大小，略大于工作线程数
CONNECTION_TIMEOUT = 30   # 连接获取超时时间（秒）

def setup_connection_pool(pool_size=POOL_SIZE):
    """设置数据库连接池"""
    global connection_pool
    try:
        connection_pool = MySQLConnectionPool(
            pool_name="mypool",
            pool_size=pool_size,
            pool_reset_session=True,  # 重置会话状态
            **DB_CONFIG
        )
        
        # 预先创建一些连接放入队列
        for _ in range(pool_size):
            try:
                conn = connection_pool.get_connection()
                connection_queue.put(conn)
            except Exception as e:
                print(f"预创建连接失败: {e}")
                break
        
        return True
    except Error as e:
        print(f"创建连接池失败: {e}")
        return False

def get_connection():
    """获取数据库连接，优先从队列获取，失败则从池获取"""
    # 先检查线程是否已有连接
    if hasattr(thread_local, "connection") and thread_local.connection:
        try:
            if thread_local.connection.is_connected():
                return thread_local.connection
        except:
            pass
    
    # 尝试从队列获取连接
    try:
        with connection_lock:  # 加锁防止并发问题
            conn = connection_queue.get(timeout=CONNECTION_TIMEOUT)
            if not conn.is_connected():
                try:
                    conn.reconnect()
                except:
                    # 如果重连失败，尝试创建新连接
                    conn = mysql.connector.connect(**DB_CONFIG)
            
            thread_local.connection = conn
            return conn
    except queue.Empty:
        # 队列为空，尝试直接从连接池获取
        try:
            thread_local.connection = connection_pool.get_connection()
            return thread_local.connection
        except Exception as e:
            # 连接池也耗尽，尝试直接创建连接
            print(f"获取连接池连接失败: {e}，尝试直接创建连接")
            try:
                thread_local.connection = mysql.connector.connect(**DB_CONFIG)
                return thread_local.connection
            except Exception as e2:
                print(f"直接创建连接也失败: {e2}，返回None")
                return None

def release_connection(conn=None):
    """释放连接回队列"""
    connection_to_release = conn
    
    # 如果没有指定连接，使用线程本地连接
    if connection_to_release is None and hasattr(thread_local, "connection"):
        connection_to_release = thread_local.connection
        delattr(thread_local, "connection")
    
    # 确保连接有效后放回队列
    if connection_to_release:
        try:
            if connection_to_release.is_connected():
                connection_queue.put(connection_to_release)
            else:
                # 连接已断开，尝试重连后放回队列
                try:
                    connection_to_release.reconnect()
                    connection_queue.put(connection_to_release)
                except:
                    # 无法重连，丢弃
                    pass
        except:
            # 出现异常，忽略
            pass

def close_connection():
    """关闭当前线程的数据库连接"""
    if hasattr(thread_local, "connection"):
        try:
            release_connection(thread_local.connection)
        except:
            pass
        delattr(thread_local, "connection")

def get_unique_transcript_ids(file_path='Transcript_id.csv'):
    """获取唯一的转录本ID列表，使用内存高效的方式"""
    try:
        # 尝试读取缓存
        cache_file = file_path + '.cache'
        if os.path.exists(cache_file):
            with open(cache_file, 'rb') as f:
                unique_ids = pickle.load(f)
                print(f"从缓存加载 {len(unique_ids)} 个唯一转录本ID")
                return unique_ids
    except:
        pass
    
    # 使用更高效的读取方式
    chunk_size = 100000  # 每次读取的行数
    unique_ids = set()
    
    for chunk in pd.read_csv(file_path, chunksize=chunk_size):
        unique_ids.update(chunk['Transcript'].unique())
    
    unique_ids = list(unique_ids)
    print(f"发现 {len(unique_ids)} 个唯一转录本ID")
    
    # 保存缓存
    try:
        with open(cache_file, 'wb') as f:
            pickle.dump(unique_ids, f)
    except:
        pass
        
    return unique_ids

def get_unique_project_ids(file_path=GSE_MATCH_FILE):
    """获取唯一的项目ID列表，使用缓存"""
    try:
        # 尝试读取缓存
        cache_file = file_path + '.project_ids.cache'
        if os.path.exists(cache_file):
            with open(cache_file, 'rb') as f:
                unique_ids = pickle.load(f)
                print(f"从缓存加载 {len(unique_ids)} 个唯一项目ID")
                return unique_ids
    except:
        pass
    
    df = pd.read_csv(file_path)
    unique_ids = df['Project ID'].unique().tolist()
    print(f"发现 {len(unique_ids)} 个唯一项目ID")
    
    # 保存缓存
    try:
        with open(cache_file, 'wb') as f:
            pickle.dump(unique_ids, f)
    except:
        pass
        
    return unique_ids

# 新增函数：获取项目的分组信息
def get_project_groups(project_id, file_path=GSE_MATCH_FILE):
    """
    获取特定项目的分组信息
    返回 {(tissue, cell_type, cell_line, health_condition): [sra_accessions]}
    """
    # 检查缓存
    if project_id in PROJECT_GROUP_CACHE:
        return PROJECT_GROUP_CACHE[project_id]
    
    # 读取GSE匹配文件获取项目的分组信息
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        # 过滤出特定项目的行
        filtered_df = df[df['Project ID'] == project_id]
        
        # 将空字符串和'NA'替换为NaN
        filtered_df = filtered_df.replace(['NA', ''], np.nan)
        
        # 创建分组DataFrame
        df_for_grouping = filtered_df.copy()
        sentinel = "__NA__"  # 特殊标记
        
        # 替换NaN值
        df_for_grouping['Tissue'] = df_for_grouping['Tissue'].fillna(sentinel)
        df_for_grouping['Cell Type'] = df_for_grouping['Cell Type'].fillna(sentinel)
        df_for_grouping['CelI line'] = df_for_grouping['CelI line'].fillna(sentinel)
        df_for_grouping['Healthy Condition'] = df_for_grouping['Healthy Condition'].fillna(sentinel)
        
        # 分组并收集SRA编号
        groups = {}
        for group_key, group_data in df_for_grouping.groupby(['Tissue', 'Cell Type', 'CelI line', 'Healthy Condition']):
            # 将特殊标记替换回None
            tissue, cell_type, cell_line, health_condition = group_key
            tissue = None if tissue == sentinel else tissue
            cell_type = None if cell_type == sentinel else cell_type
            cell_line = None if cell_line == sentinel else cell_line
            health_condition = None if health_condition == sentinel else health_condition
            
            # 获取该分组的SRA编号和策略类型
            group_info = {
                'sra_accessions': group_data['SRA Accession'].tolist(),
                'strategies': group_data['Strategy'].unique().tolist()
            }
            
            groups[(tissue, cell_type, cell_line, health_condition)] = group_info
        
        # 更新缓存
        PROJECT_GROUP_CACHE[project_id] = groups
        return groups
    
    except Exception as e:
        print(f"获取项目 {project_id} 的分组信息时出错: {e}")
        return {}

def execute_query_with_retry(query, params=None, max_retries=3, retry_delay=1):
    """执行查询，带有重试机制"""
    conn = None
    cursor = None
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            conn = get_connection()
            if not conn:
                print(f"无法获取数据库连接，重试 {retry_count + 1}/{max_retries}")
                retry_count += 1
                time.sleep(retry_delay)
                continue
                
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
                
            result = cursor.fetchall()
            return result
            
        except Exception as e:
            print(f"查询执行失败: {e}，重试 {retry_count + 1}/{max_retries}")
            retry_count += 1
            time.sleep(retry_delay)
            
        finally:
            if cursor:
                cursor.close()
            # 不在这里关闭连接，而是把它放回队列
    
    # 所有重试都失败
    print("所有查询重试都失败")
    return []

@lru_cache(maxsize=1024)
def get_strategy_types(project_id):
    """获取特定项目的所有策略类型，使用缓存"""
    # 先检查全局缓存
    if project_id in PROJECT_STRATEGY_CACHE:
        return PROJECT_STRATEGY_CACHE[project_id]
    
    query = """
    SELECT DISTINCT strategy 
    FROM gseMatch 
    WHERE projectId = %s
    """
    
    results = execute_query_with_retry(query, (project_id,))
    strategies = [result[0] for result in results]
    
    # 更新全局缓存
    PROJECT_STRATEGY_CACHE[project_id] = strategies
    
    return strategies

@lru_cache(maxsize=1024)
def get_project_sra_bioproject(project_id):
    """获取项目对应的SRA Accession和BioProject ID，使用缓存"""
    # 先检查全局缓存
    if project_id in PROJECT_INFO_CACHE:
        return PROJECT_INFO_CACHE[project_id]
    
    query = """
    SELECT DISTINCT sraAccession, bioProjectId
    FROM gseMatch
    WHERE projectId = %s
    LIMIT 1
    """
    
    results = execute_query_with_retry(query, (project_id,))
    
    if results and len(results) > 0:
        result = results[0]
        # 更新全局缓存
        PROJECT_INFO_CACHE[project_id] = result
        return result[0], result[1]
    
    return None, None

def get_tpm_batch(project_id, strategy, transcript_id_list, sra_acc_list=None):
    """批量获取一组转录本的TPM数据，可选按SRA列表过滤"""
    if not transcript_id_list:
        return {}
    
    # 为避免SQL参数过多，限制每批SRA和转录本的数量
    max_batch_size = 5000  # 最大批处理大小
    
    # 组织结果字典
    result_dict = {}
    
    # 处理转录本ID批次
    for i in range(0, len(transcript_id_list), max_batch_size):
        batch_transcript_ids = transcript_id_list[i:i + max_batch_size]
        
        if sra_acc_list and len(sra_acc_list) > 0:
            # 处理SRA批次
            for j in range(0, len(sra_acc_list), max_batch_size):
                batch_sra_acc = sra_acc_list[j:j + max_batch_size]
                
                # 将转录本ID和SRA编号列表格式化为SQL IN语句
                transcript_placeholders = ', '.join(['%s'] * len(batch_transcript_ids))
                sra_placeholders = ', '.join(['%s'] * len(batch_sra_acc))
                
                query = f"""
                SELECT t.transcriptId, g.sraAccession, t.tpm 
                FROM gseMatch g
                JOIN tpmData t ON g.sraAccession = t.sraAccession
                WHERE g.projectId = %s
                AND g.strategy = %s
                AND t.transcriptId IN ({transcript_placeholders})
                AND g.sraAccession IN ({sra_placeholders})
                """
                
                params = [project_id, strategy] + batch_transcript_ids + batch_sra_acc
                results = execute_query_with_retry(query, params)
                
                # 合并结果
                for row in results:
                    trans_id, sra, tpm = row
                    if trans_id not in result_dict:
                        result_dict[trans_id] = []
                    result_dict[trans_id].append((sra, tpm))
        else:
            # 将转录本ID列表格式化为SQL IN语句
            placeholders = ', '.join(['%s'] * len(batch_transcript_ids))
            
            query = f"""
            SELECT t.transcriptId, g.sraAccession, t.tpm 
            FROM gseMatch g
            JOIN tpmData t ON g.sraAccession = t.sraAccession
            WHERE g.projectId = %s
            AND g.strategy = %s
            AND t.transcriptId IN ({placeholders})
            """
            
            params = [project_id, strategy] + batch_transcript_ids
            results = execute_query_with_retry(query, params)
            
            # 合并结果
            for row in results:
                trans_id, sra, tpm = row
                if trans_id not in result_dict:
                    result_dict[trans_id] = []
                result_dict[trans_id].append((sra, tpm))
    
    return result_dict

def calculate_strategy_value_batch(project_id, strategy, transcript_batch, sra_acc_list=None):
    """批量计算一组转录本的策略值，可选按特定SRA编号列表过滤"""
    # 获取批量TPM数据
    tpm_data = get_tpm_batch(project_id, strategy, transcript_batch, sra_acc_list)
    
    # 计算每个转录本的平均值
    results = {}
    for trans_id in transcript_batch:
        if trans_id in tpm_data and tpm_data[trans_id]:
            # 提取TPM值和计算SRA数量
            tpm_values = [item[1] for item in tpm_data[trans_id]]
            sra_count = len(set([item[0] for item in tpm_data[trans_id]]))
            
            if tpm_values and sra_count > 0:
                results[trans_id] = sum(tpm_values) / sra_count
    
    return results

def calculate_indices(p_value, m_value, r_value):
    """计算翻译指标"""
    indices = {}
    
    # 检查哪些指标可以计算
    can_calc_tr = r_value is not None and m_value is not None and m_value != 0
    can_calc_evi = r_value is not None and m_value is not None and p_value is not None and m_value != 0 and p_value != 0
    can_calc_te = p_value is not None and m_value is not None and m_value != 0
    
    # 计算指标
    if can_calc_tr:
        indices['TR'] = r_value / m_value
    else:
        indices['TR'] = None
    
    if can_calc_evi:
        indices['EVI'] = (r_value ** 2) / (m_value * p_value)
    else:
        indices['EVI'] = None
    
    if can_calc_te:
        indices['TE'] = p_value / m_value
    else:
        indices['TE'] = None
    
    return indices

def process_project_group_batch(project_id, transcript_batch, group_key):
    """处理一个项目下特定分组的一批转录本"""
    try:
        # 获取项目的SRA Accession和BioProject ID
        _, bioproject_id = get_project_sra_bioproject(project_id)
        
        # 解析分组键
        tissue, cell_type, cell_line, health_condition = group_key
        
        # 获取该分组的SRA编号和策略类型
        project_groups = get_project_groups(project_id)
        if group_key not in project_groups:
            return []
        
        group_info = project_groups[group_key]
        sra_acc_list = group_info['sra_accessions']
        strategies = group_info['strategies']
        
        if not strategies:
            # 移除sra_accession列
            return [(t, project_id, bioproject_id, tissue, cell_type, cell_line, health_condition, None, None, None) for t in transcript_batch]
        
        # 限制SRA列表大小，避免SQL过长
        if len(sra_acc_list) > 1000:
            print(f"项目 {project_id} 分组 {group_key} 的SRA列表过大 ({len(sra_acc_list)}个)，将随机采样1000个")
            # 随机采样1000个SRA
            import random
            random.seed(42)  # 使用固定种子以确保可重复性
            sra_acc_list = random.sample(sra_acc_list, 1000)
        
        # 策略映射
        strategy_map = {
            'Ribo': 'P',
            'RNA': 'M',
            'RNC': 'R'
        }
        
        # 获取每种策略的批量值
        strategy_values = {}
        for strategy in strategies:
            if strategy in strategy_map:
                key = strategy_map[strategy]
                strategy_values[key] = calculate_strategy_value_batch(project_id, strategy, transcript_batch, sra_acc_list)
        
        # 计算每个转录本的指标
        results = []
        for transcript_id in transcript_batch:
            # 获取每种策略的值
            values = {}
            for key, value_dict in strategy_values.items():
                if transcript_id in value_dict:
                    values[key] = value_dict[transcript_id]
            
            # 如果至少有两种策略，计算指标
            if len(values) >= 2:
                p_value = values.get('P')
                m_value = values.get('M')
                r_value = values.get('R')
                
                indices = calculate_indices(p_value, m_value, r_value)
                tr = indices['TR']
                evi = indices['EVI']
                te = indices['TE']
            else:
                tr, evi, te = None, None, None
            
            # 移除sra_accession列
            results.append((transcript_id, project_id, bioproject_id, 
                          tissue, cell_type, cell_line, health_condition, 
                          tr, evi, te))
        
        return results
    
    except Exception as e:
        print(f"处理项目 {project_id} 分组 {group_key} 批次时出错: {e}")
        # 移除sra_accession列，保持列数一致
        return [(t, project_id, None, None, None, None, None, None, None, None) for t in transcript_batch]

def process_worker(args):
    """工作线程函数，处理一个项目特定分组的一批转录本"""
    project_id, transcript_batch, group_key = args
    try:
        results = process_project_group_batch(project_id, transcript_batch, group_key)
        # 确保释放连接而不是关闭
        close_connection()
        return results
    except Exception as e:
        print(f"工作线程处理错误: {e}")
        close_connection()
        return []

def process_with_optimized_batches(transcript_ids, project_ids, 
                                  batch_size=BATCH_SIZE, max_workers=MAX_WORKERS, 
                                  output_file='translation_indices_results_grouped.csv'):
    """使用优化的批处理方法处理数据，按项目分组处理"""
    # 初始化结果CSV文件
    with open(output_file, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        # 移除sra_accession列
        writer.writerow(['transcript_id', 'project_id', 'bioproject_id', 
                        'Tissue', 'Cell Type', 'Cell line', 'Healthy Condition', 
                        'TR', 'EVI', 'TE'])
    
    # 计算总项目数
    total_projects = len(project_ids)
    
    # 创建进度条
    with tqdm(total=total_projects, desc="项目处理进度") as pbar:
        # 按项目分批处理
        for i in range(0, len(project_ids), MAX_PROJECTS_PER_ROUND):
            batch_project_ids = project_ids[i:i + MAX_PROJECTS_PER_ROUND]
            
            for project_id in batch_project_ids:
                # 获取项目的分组信息
                project_groups = get_project_groups(project_id)
                if not project_groups:
                    print(f"项目 {project_id} 没有找到分组信息，跳过")
                    pbar.update(1)
                    continue
                
                print(f"项目 {project_id} 有 {len(project_groups)} 个分组")
                
                # 为每个分组创建任务
                all_tasks = []
                for group_key in project_groups:
                    # 将转录本ID列表分成更小的批次
                    transcript_batch_size = batch_size  # 较小的批次
                    
                    # 处理所有转录本，删除限制5000的代码
                    for j in range(0, len(transcript_ids), transcript_batch_size):
                        transcript_batch = transcript_ids[j:j + transcript_batch_size]
                        all_tasks.append((project_id, transcript_batch, group_key))
                
                # 使用线程池处理所有任务
                completed_tasks = 0
                total_tasks = len(all_tasks)
                
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 使用map提交任务，这样可以控制并发度
                    for result_batch in executor.map(process_worker, all_tasks):
                        if result_batch:
                            # 将结果写入CSV
                            with open(output_file, 'a', newline='') as csvfile:
                                writer = csv.writer(csvfile)
                                for result in result_batch:
                                    if result:
                                        writer.writerow(result)
                        
                        # 更新任务完成进度
                        completed_tasks += 1
                        if completed_tasks % 10 == 0 or completed_tasks == total_tasks:
                            print(f"项目 {project_id} 完成 {completed_tasks}/{total_tasks} 个任务")
                
                # 完成一个项目后更新进度条
                pbar.update(1)
                
                # 在处理完一个项目后清理缓存
                gc.collect()  # 强制垃圾回收
            
            # 每处理一批项目后，重置连接池
            reset_connection_pool()

def reset_connection_pool():
    """重置连接池和队列"""
    global connection_pool, connection_queue
    
    # 清空连接队列
    try:
        while not connection_queue.empty():
            conn = connection_queue.get_nowait()
            try:
                if conn and conn.is_connected():
                    conn.close()
            except:
                pass
    except:
        pass
    
    # 创建新的连接池
    try:
        if connection_pool:
            # 尝试关闭连接池（不是所有连接池实现都支持）
            try:
                connection_pool.close()
            except:
                pass
        
        # 重新设置连接池
        setup_connection_pool(pool_size=POOL_SIZE)
    except Exception as e:
        print(f"重置连接池失败: {e}")

def cleanup_resources():
    """清理所有资源"""
    # 关闭所有连接
    try:
        while not connection_queue.empty():
            conn = connection_queue.get_nowait()
            try:
                if conn and conn.is_connected():
                    conn.close()
            except:
                pass
    except:
        pass
    
    # 清理缓存
    PROJECT_STRATEGY_CACHE.clear()
    PROJECT_INFO_CACHE.clear()
    PROJECT_GROUP_CACHE.clear()
    
    # 强制垃圾回收
    gc.collect()

def main():
    # 设置开始时间
    start_time = time.time()
    
    try:
        # 初始化连接池
        if setup_connection_pool(pool_size=POOL_SIZE):
            print(f"数据库连接池初始化成功，共 {POOL_SIZE} 个连接")
        else:
            print("数据库连接池初始化失败")
            return
        
        # 获取唯一的转录本ID和项目ID
        transcript_ids = get_unique_transcript_ids()
        project_ids = get_unique_project_ids()
        
        # 添加估算处理规模代码 - 使用所有项目而不只是前20个
        print("计算所有项目的分组数量，这可能需要一些时间...")
        total_groups = 0
        for project_id in tqdm(project_ids):  # 使用所有项目
            groups = get_project_groups(project_id)
            total_groups += len(groups)
        
        # 直接使用实际的总分组数，不需要再估算
        estimated_total_groups = total_groups
        # 计算预期的总结果数
        estimated_total_results = len(transcript_ids) * estimated_total_groups
        
        print(f"实际总分组数: {estimated_total_groups:,}")
        print(f"预期总结果行数: 约 {estimated_total_results:,.0f} 行")
        print(f"(基于 {len(transcript_ids):,} 个转录本和 {estimated_total_groups:,} 个分组)")
        
        # 预热缓存 - 预热所有项目信息缓存，因为我们已经加载过了
        print("预热所有项目信息缓存...")
        for project_id in tqdm(project_ids):
            get_strategy_types(project_id)
            get_project_sra_bioproject(project_id)
        
        # 可选：限制处理数量（用于测试）
        # 取消下面两行注释可以限制处理数量
        # transcript_ids = transcript_ids[:1000]
        # project_ids = project_ids[:5]
        
        # 处理所有组合，使用优化的批处理方法
        process_with_optimized_batches(transcript_ids, project_ids)
        
        # 打印总运行时间
        elapsed_time = time.time() - start_time
        hours, remainder = divmod(elapsed_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        print(f"总运行时间: {int(hours)}小时 {int(minutes)}分钟 {seconds:.2f}秒")
        
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        cleanup_resources()
        print("资源已清理完毕")

if __name__ == "__main__":
    main() 