#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并基因数据完整性分析结果脚本
合并 gene_data_completeness_analysis_results 和 utr_translation_indices_completeness_analysis_results 文件
"""

import pandas as pd
import sys
from datetime import datetime

def merge_gene_completeness_analysis():
    """
    合并两个基因数据完整性分析结果文件
    """
    print("=" * 80)
    print("基因数据完整性分析结果合并")
    print("=" * 80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 定义输入文件路径
    gene_file = 'gene_data_completeness_analysis_results_20250807_182615.csv'
    utr_file = 'utr_translation_indices_completeness_analysis_results_20250807_183634.csv'
    
    print(f"\n输入文件:")
    print(f"1. 基因数据集: {gene_file}")
    print(f"2. UTR数据集: {utr_file}")
    
    # 读取第一个文件（基因数据集）
    print(f"\n正在读取基因数据集文件...")
    try:
        df_gene = pd.read_csv(gene_file)
        print(f"成功读取基因数据集，共 {len(df_gene)} 行")
        print(f"列名: {df_gene.columns.tolist()}")
        
        # 检查必要的列
        if 'geneId' not in df_gene.columns:
            print("错误: 基因数据集文件中缺少 'geneId' 列")
            return
        if 'total_count' not in df_gene.columns or 'complete_data_percentage' not in df_gene.columns:
            print("错误: 基因数据集文件中缺少必要的数据列")
            return
            
    except Exception as e:
        print(f"读取基因数据集文件失败: {e}")
        return
    
    # 读取第二个文件（UTR数据集）
    print(f"\n正在读取UTR数据集文件...")
    try:
        df_utr = pd.read_csv(utr_file)
        print(f"成功读取UTR数据集，共 {len(df_utr)} 行")
        print(f"列名: {df_utr.columns.tolist()}")
        
        # 检查必要的列
        if 'geneId' not in df_utr.columns:
            print("错误: UTR数据集文件中缺少 'geneId' 列")
            return
        if 'total_count' not in df_utr.columns or 'complete_data_percentage' not in df_utr.columns:
            print("错误: UTR数据集文件中缺少必要的数据列")
            return
            
    except Exception as e:
        print(f"读取UTR数据集文件失败: {e}")
        return
    
    # 显示基本统计信息
    print(f"\n" + "=" * 60)
    print("文件统计信息")
    print("=" * 60)
    print(f"基因数据集包含的基因数: {len(df_gene):,}")
    print(f"UTR数据集包含的基因数: {len(df_utr):,}")
    
    # 检查重复的geneId
    gene_duplicates = df_gene['geneId'].duplicated().sum()
    utr_duplicates = df_utr['geneId'].duplicated().sum()
    
    if gene_duplicates > 0:
        print(f"警告: 基因数据集中发现 {gene_duplicates} 个重复的geneId")
    if utr_duplicates > 0:
        print(f"警告: UTR数据集中发现 {utr_duplicates} 个重复的geneId")
    
    # 分析两个数据集的交集
    gene_ids_gene = set(df_gene['geneId'])
    gene_ids_utr = set(df_utr['geneId'])
    
    common_genes = gene_ids_gene & gene_ids_utr
    only_in_gene = gene_ids_gene - gene_ids_utr
    only_in_utr = gene_ids_utr - gene_ids_gene
    
    print(f"\n基因ID交集分析:")
    print(f"两个数据集共同的基因数: {len(common_genes):,}")
    print(f"仅在基因数据集中的基因数: {len(only_in_gene):,}")
    print(f"仅在UTR数据集中的基因数: {len(only_in_utr):,}")
    print(f"基因数据集覆盖率: {len(common_genes)/len(gene_ids_gene)*100:.2f}%")
    print(f"UTR数据集覆盖率: {len(common_genes)/len(gene_ids_utr)*100:.2f}%")
    
    # 重命名列以避免冲突
    print(f"\n正在重命名列以避免冲突...")
    df_gene_renamed = df_gene.rename(columns={
        'total_count': 'total_count_gene',
        'complete_data_percentage': 'complete_data_percentage_gene'
    })
    
    df_utr_renamed = df_utr.rename(columns={
        'total_count': 'total_count_utr',
        'complete_data_percentage': 'complete_data_percentage_utr'
    })
    
    print(f"基因数据集重命名后的列: {df_gene_renamed.columns.tolist()}")
    print(f"UTR数据集重命名后的列: {df_utr_renamed.columns.tolist()}")
    
    # 执行内连接合并
    print(f"\n正在执行内连接合并...")
    merged_df = pd.merge(df_gene_renamed, df_utr_renamed, on='geneId', how='inner')
    
    print(f"合并完成！")
    print(f"合并后的数据包含 {len(merged_df):,} 个基因")
    print(f"合并后的列: {merged_df.columns.tolist()}")
    
    # 按geneId字母顺序排序
    print(f"\n正在按geneId字母顺序排序...")
    merged_df = merged_df.sort_values('geneId').reset_index(drop=True)
    
    # 显示合并结果的统计信息
    print(f"\n" + "=" * 60)
    print("合并结果统计")
    print("=" * 60)
    
    print(f"合并后数据形状: {merged_df.shape}")
    print(f"保留的基因数: {len(merged_df):,}")
    print(f"数据保留率: {len(merged_df)/min(len(df_gene), len(df_utr))*100:.2f}%")
    
    # 显示数据范围统计
    print(f"\n基因数据集统计:")
    print(f"  总记录数范围: {merged_df['total_count_gene'].min():,} - {merged_df['total_count_gene'].max():,}")
    print(f"  完整性范围: {merged_df['complete_data_percentage_gene'].min():.2f}% - {merged_df['complete_data_percentage_gene'].max():.2f}%")
    print(f"  平均完整性: {merged_df['complete_data_percentage_gene'].mean():.2f}%")
    
    print(f"\nUTR数据集统计:")
    print(f"  总记录数范围: {merged_df['total_count_utr'].min():,} - {merged_df['total_count_utr'].max():,}")
    print(f"  完整性范围: {merged_df['complete_data_percentage_utr'].min():.2f}% - {merged_df['complete_data_percentage_utr'].max():.2f}%")
    print(f"  平均完整性: {merged_df['complete_data_percentage_utr'].mean():.2f}%")
    
    # 显示前10行作为预览
    print(f"\n前10行合并结果预览:")
    print("-" * 120)
    print(merged_df.head(10).to_string(index=False))
    
    # 分析完整性对比
    print(f"\n" + "=" * 60)
    print("完整性对比分析")
    print("=" * 60)
    
    # 计算完整性差异
    merged_df['completeness_diff'] = merged_df['complete_data_percentage_gene'] - merged_df['complete_data_percentage_utr']
    
    better_in_gene = len(merged_df[merged_df['completeness_diff'] > 0])
    better_in_utr = len(merged_df[merged_df['completeness_diff'] < 0])
    equal_completeness = len(merged_df[merged_df['completeness_diff'] == 0])
    
    print(f"基因数据集完整性更高的基因数: {better_in_gene:,} ({better_in_gene/len(merged_df)*100:.2f}%)")
    print(f"UTR数据集完整性更高的基因数: {better_in_utr:,} ({better_in_utr/len(merged_df)*100:.2f}%)")
    print(f"两个数据集完整性相等的基因数: {equal_completeness:,} ({equal_completeness/len(merged_df)*100:.2f}%)")
    
    print(f"\n完整性差异统计:")
    print(f"平均差异: {merged_df['completeness_diff'].mean():.2f}%")
    print(f"最大正差异: {merged_df['completeness_diff'].max():.2f}%")
    print(f"最大负差异: {merged_df['completeness_diff'].min():.2f}%")
    
    # 移除临时列
    merged_df = merged_df.drop('completeness_diff', axis=1)
    
    # 保存合并结果
    output_file = f"merged_gene_completeness_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    print(f"\n正在保存合并结果到: {output_file}")
    
    try:
        merged_df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"合并结果已成功保存到: {output_file}")
        print(f"输出文件包含 {len(merged_df):,} 行数据，{len(merged_df.columns)} 列")
        
        # 验证保存的文件
        print(f"\n验证保存的文件...")
        verification_df = pd.read_csv(output_file, nrows=3)
        print(f"验证成功！文件前3行:")
        print(verification_df.to_string(index=False))
        
    except Exception as e:
        print(f"保存文件失败: {e}")
        return
    
    print(f"\n合并完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

if __name__ == "__main__":
    merge_gene_completeness_analysis()
