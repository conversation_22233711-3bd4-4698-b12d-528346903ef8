#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理 transcriptinfo/merged_transcriptID_utr_table_data_3.csv 文件
根据 Region 列的值修改 UTR_Type 列的值：
- 如果 Region 为 "3' UTR"，则 UTR_Type 设为 "3UTR"
- 如果 Region 为 "5' UTR"，则 UTR_Type 设为 "5UTR"
"""

import pandas as pd
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('process_utr_type.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

INPUT_FILE = 'transcriptinfo/merged_transcriptID_utr_table_data_3.csv'
OUTPUT_FILE = 'transcriptinfo/merged_transcriptID_utr_table_data_3_updated.csv'

def process_utr_type():
    """处理UTR_Type列的值"""
    try:
        logging.info(f'开始读取文件: {INPUT_FILE}')
        
        # 读取CSV文件
        df = pd.read_csv(INPUT_FILE)
        logging.info(f'文件读取完成，共 {len(df)} 行')
        
        # 显示原始数据统计
        logging.info('原始数据统计:')
        logging.info(f'UTR_Type 列的唯一值: {df["UTR_Type"].value_counts().to_dict()}')
        logging.info(f'Region 列的唯一值: {df["Region"].value_counts().to_dict()}')
        
        # 记录修改前的状态
        original_utr_type = df['UTR_Type'].copy()
        
        # 根据Region列的值修改UTR_Type列
        def update_utr_type(row):
            region = row['Region']
            if region == "3' UTR":
                return "3UTR"
            elif region == "5' UTR":
                return "5UTR"
            else:
                # 如果Region值不是预期的，保持原值
                logging.warning(f'发现未预期的Region值: {region}')
                return row['UTR_Type']
        
        # 应用修改
        df['UTR_Type'] = df.apply(update_utr_type, axis=1)
        
        # 统计修改情况
        changes = (original_utr_type != df['UTR_Type']).sum()
        logging.info(f'共修改了 {changes} 行数据')
        
        # 显示修改后的数据统计
        logging.info('修改后数据统计:')
        logging.info(f'UTR_Type 列的唯一值: {df["UTR_Type"].value_counts().to_dict()}')
        
        # 验证修改结果
        logging.info('验证修改结果:')
        three_utr_check = df[df['Region'] == "3' UTR"]['UTR_Type'].unique()
        five_utr_check = df[df['Region'] == "5' UTR"]['UTR_Type'].unique()
        
        logging.info(f'Region为"3\' UTR"的行，UTR_Type值: {three_utr_check}')
        logging.info(f'Region为"5\' UTR"的行，UTR_Type值: {five_utr_check}')
        
        # 保存修改后的文件
        logging.info(f'保存修改后的文件到: {OUTPUT_FILE}')
        df.to_csv(OUTPUT_FILE, index=False)
        
        logging.info('处理完成！')
        
        # 显示前几行示例
        logging.info('修改后的前5行示例:')
        for i in range(min(5, len(df))):
            row = df.iloc[i]
            logging.info(f'  TranscriptID: {row["TranscriptID"]}, Region: {row["Region"]}, UTR_Type: {row["UTR_Type"]}')
        
        return True
        
    except Exception as e:
        logging.error(f'处理过程中发生错误: {str(e)}')
        return False

def main():
    """主函数"""
    logging.info('开始处理UTR_Type列...')
    success = process_utr_type()
    
    if success:
        logging.info('UTR_Type列处理任务完成！')
    else:
        logging.error('UTR_Type列处理任务失败！')

if __name__ == '__main__':
    main()
