#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理 Transcript_id_with_UTR_info_and_genes.csv 文件中
"3'UTR component characteristics" 和 "5'UTR component characteristics" 两列，
仅保留 miRNAs、PolyA Sites、Repeats、Rfam motifs、uORFs 这五个键。

生成输出文件： Transcript_id_with_UTR_info_and_genes_cleaned.csv
"""

import pandas as pd
import json
import ast
import logging
from typing import Dict

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('clean_utr_characteristics.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

INPUT_FILE = 'Transcript_id_with_UTR_info_and_genes.csv'
OUTPUT_FILE = 'Transcript_id_with_UTR_info_and_genes_cleaned.csv'

KEEP_KEYS = [
    'miRNAs',
    'PolyA Sites',
    'Repeats',
    'Rfam motifs',
    'uORFs'
]

def parse_component(component_str: str) -> Dict[str, str]:
    """
    解析组件字符串，返回字典，仅保留指定键。
    如果解析失败或为空，返回包含五个键且值为 'NA' 的字典。
    """
    if pd.isna(component_str) or component_str.strip() == '':
        return {k: 'NA' for k in KEEP_KEYS}

    # 尝试 JSON 解析
    parsed = None
    try:
        parsed = json.loads(component_str)
    except json.JSONDecodeError:
        try:
            # 有些字符串格式为单引号或缺少引号，使用 ast.literal_eval 作为兜底
            parsed = ast.literal_eval(component_str)
        except Exception:
            logging.warning('无法解析组件字符串，返回 NA')
            return {k: 'NA' for k in KEEP_KEYS}

    # 构造保留字典
    cleaned = {}
    for k in KEEP_KEYS:
        cleaned[k] = str(parsed.get(k, 'NA'))
    return cleaned

def clean_column(df: pd.DataFrame, column_name: str) -> pd.Series:
    """对指定列进行清理，返回新的列 Series"""
    logging.info(f'清理列: {column_name}')
    def _clean_cell(x):
        # 如果为空，直接返回原值（保持 NaN / 空字符串），不做处理
        if pd.isna(x) or str(x).strip() == '':
            return x
        # 否则进行解析与保留指定键
        return json.dumps(parse_component(x), ensure_ascii=False)

    cleaned_series = df[column_name].apply(_clean_cell)
    return cleaned_series


def main():
    logging.info('读取输入文件...')
    df = pd.read_csv(INPUT_FILE)
    logging.info(f'共读取 {len(df)} 行')

    # 清理两个目标列
    df["3'UTR component characteristics"] = clean_column(df, "3'UTR component characteristics")
    df["5'UTR component characteristics"] = clean_column(df, "5'UTR component characteristics")

    logging.info('保存清理后的文件...')
    df.to_csv(OUTPUT_FILE, index=False)
    logging.info(f'已保存到 {OUTPUT_FILE}')

if __name__ == '__main__':
    print('开始清理 UTR 组件信息...')
    main()
    print('清理完成！') 