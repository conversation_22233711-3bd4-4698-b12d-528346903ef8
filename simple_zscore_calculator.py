#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版本的翻译效率指标Z-score和偏度计算脚本
避免复杂依赖问题
"""

import pandas as pd
import math
import statistics
import os

def calculate_skewness(data):
    """计算偏度"""
    n = len(data)
    if n < 3:
        return 0
    
    mean = statistics.mean(data)
    std = statistics.stdev(data)
    
    if std == 0:
        return 0
    
    # 计算偏度
    skew_sum = sum(((x - mean) / std) ** 3 for x in data)
    skewness = skew_sum / n
    
    return skewness

def calculate_zscore(values):
    """计算z-score"""
    if len(values) == 0:
        return []
    
    mean = statistics.mean(values)
    std = statistics.stdev(values) if len(values) > 1 else 0
    
    if std == 0:
        return [0] * len(values)
    
    return [(x - mean) / std for x in values]

def assign_quantile_level(zscore, quartiles):
    """根据z-score分配分位数等级"""
    if zscore <= quartiles[0]:
        return 'Q1'
    elif zscore <= quartiles[1]:
        return 'Q2'
    elif zscore <= quartiles[2]:
        return 'Q3'
    else:
        return 'Q4'

def process_metric(df, metric_name):
    """处理单个指标"""
    print(f"\n处理 {metric_name} 数据...")
    
    # 获取对应列名
    zscore_col = f"{metric_name}_ZSCORE"
    level_col = f"{metric_name}_LEVEL"
    
    # 过滤有效数据：不为空且大于0
    valid_data = []
    valid_indices = []
    
    for idx, value in enumerate(df[metric_name]):
        if pd.notna(value) and value > 0:
            valid_data.append(value)
            valid_indices.append(idx)
    
    if len(valid_data) == 0:
        print(f"{metric_name} 没有有效数据")
        return df, None
    
    print(f"{metric_name} 有效数据行数: {len(valid_data)}")
    
    # 计算log2变换
    log2_values = [math.log2(x) for x in valid_data]
    
    # 计算z-score
    z_scores = calculate_zscore(log2_values)
    
    # 计算分位数
    sorted_zscores = sorted(z_scores)
    n = len(sorted_zscores)
    q1_idx = int(n * 0.25)
    q2_idx = int(n * 0.5) 
    q3_idx = int(n * 0.75)
    
    quartiles = [
        sorted_zscores[q1_idx],
        sorted_zscores[q2_idx], 
        sorted_zscores[q3_idx]
    ]
    
    # 分配等级
    levels = [assign_quantile_level(z, quartiles) for z in z_scores]
    
    # 计算偏度
    skewness = calculate_skewness(z_scores)
    
    # 更新数据框
    df[zscore_col] = None
    df[level_col] = ''
    
    for i, idx in enumerate(valid_indices):
        df.loc[idx, zscore_col] = z_scores[i]
        df.loc[idx, level_col] = levels[i]
    
    # 统计各组数量
    level_counts = {}
    for level in levels:
        level_counts[level] = level_counts.get(level, 0) + 1
    
    print(f"{metric_name} 各分组数量:")
    for level in ['Q1', 'Q2', 'Q3', 'Q4']:
        count = level_counts.get(level, 0)
        print(f"  {level}: {count}")
    
    print(f"{metric_name} 偏度 (skewness): {skewness:.4f}")
    
    # 保存分组数据
    result = {
        'metric_name': metric_name,
        'valid_count': len(valid_data),
        'skewness': skewness,
        'quartiles': quartiles,
        'level_counts': level_counts,
        'z_scores': z_scores,
        'levels': levels,
        'valid_indices': valid_indices
    }
    
    return df, result


def main():
    """主函数"""
    input_file = "skewness_test/TEDD00014_transcripts.csv"
    
    print(f"正在加载数据: {input_file}")
    global df
    df = pd.read_csv(input_file)
    print(f"原始数据行数: {len(df)}")
    
    # 处理三个指标
    metrics = ['TE', 'TR', 'EVI']
    all_results = {}
    
    for metric in metrics:
        if metric in df.columns:
            df, result = process_metric(df, metric)
            if result is not None:
                all_results[metric] = result
        else:
            print(f"警告: 列 {metric} 不存在于数据中")
    
    # 直接保存更新后的数据到原文件
    df.to_csv(input_file, index=False)
    print(f"\n已直接更新原文件: {input_file}")
    
    # 生成简要报告到控制台
    print("\n" + "="*50)
    print("翻译效率指标分析总结报告")
    print("="*50)
    print(f"文件: {input_file}")
    print(f"总行数: {len(df)}")
    print()
    
    for metric, result in all_results.items():
        print(f"{metric} 指标分析结果:")
        print("-" * 30)
        print(f"有效数据行数: {result['valid_count']}")
        print(f"偏度 (skewness): {result['skewness']:.4f}")
        print(f"分位数 (25%, 50%, 75%): {[f'{q:.4f}' for q in result['quartiles']]}")
        print("各分组数量:")
        for level in ['Q1', 'Q2', 'Q3', 'Q4']:
            count = result['level_counts'].get(level, 0)
            print(f"  {level}: {count}")
        print()
    
    print("分析完成！已直接更新CSV文件中的ZSCORE和LEVEL列。")

if __name__ == "__main__":
    main() 