#!/usr/bin/env python3
import pandas as pd
import sys

try:
    print("正在读取数据文件...")
    df = pd.read_csv("Gene_TPM/gene_with_translation_indices.csv")
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print("\n前5行:")
    print(df.head())
    print("\n数据类型:")
    print(df.dtypes)
    print("\n缺失值:")
    print(df.isnull().sum())
    
    if 'disease' in df.columns:
        print(f"\n疾病类型: {df['disease'].unique()}")
    
except Exception as e:
    print(f"错误: {e}")
    sys.exit(1)
