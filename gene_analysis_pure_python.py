#!/usr/bin/env python3
"""
基因翻译效率统计分析脚本 - 纯Python版本
完全避免numpy/pandas依赖，解决递归错误问题
"""

import csv
import math
from collections import defaultdict

def load_csv_data(file_path):
    """使用纯Python加载CSV数据"""
    print("正在加载数据...")
    try:
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            print(f"列名: {headers}")
            
            for i, row in enumerate(reader):
                data.append(row)
                if i < 3:  # 显示前3行
                    print(f"行 {i+1}: {dict(list(row.items())[:5])}...")  # 只显示前5列
        
        print(f"数据加载成功！共 {len(data)} 行")
        return data, headers
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None, None

def safe_float(value):
    """安全的浮点数转换"""
    if value is None or value == '' or str(value).lower() in ['nan', 'null', 'none']:
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def calculate_stats(values):
    """计算基本统计量"""
    valid_values = [v for v in values if v is not None]
    if not valid_values:
        return None, None, 0
    
    n = len(valid_values)
    mean = sum(valid_values) / n
    
    if n < 2:
        return mean, None, n
    
    variance = sum((x - mean) ** 2 for x in valid_values) / (n - 1)
    return mean, variance, n

def t_test_simple(group1, group2):
    """简化的t检验"""
    mean1, var1, n1 = calculate_stats(group1)
    mean2, var2, n2 = calculate_stats(group2)
    
    if mean1 is None or mean2 is None or var1 is None or var2 is None:
        return None, None
    
    if n1 < 2 or n2 < 2:
        return None, None
    
    # 合并方差
    pooled_var = ((n1 - 1) * var1 + (n2 - 1) * var2) / (n1 + n2 - 2)
    
    if pooled_var <= 0:
        return None, None
    
    # t统计量
    t_stat = (mean1 - mean2) / math.sqrt(pooled_var * (1/n1 + 1/n2))
    
    # 简化的p值估计
    abs_t = abs(t_stat)
    if abs_t > 3.0:
        p_value = 0.001
    elif abs_t > 2.5:
        p_value = 0.01
    elif abs_t > 2.0:
        p_value = 0.05
    elif abs_t > 1.5:
        p_value = 0.1
    else:
        p_value = 0.2
    
    return t_stat, p_value

def analyze_data(data, headers):
    """分析数据"""
    print("\n开始数据分析...")
    
    # 检查必要的列
    required_cols = ['geneSymbol', 'geneId', 'disease']
    missing_cols = [col for col in required_cols if col not in headers]
    if missing_cols:
        print(f"缺少必要的列: {missing_cols}")
        return None
    
    # 检查分析列
    analysis_cols = ['TE', 'TR', 'EVI']
    available_cols = [col for col in analysis_cols if col in headers]
    print(f"可用于分析的变量列: {available_cols}")
    
    if not available_cols:
        print("未找到TE、TR、EVI列")
        return None
    
    # 统计疾病类型
    diseases = set(row['disease'] for row in data)
    print(f"疾病类型: {list(diseases)}")
    
    if 'Normal' not in diseases:
        print("警告: 未找到'Normal'对照组")
        return None
    
    # 按基因分组
    gene_groups = defaultdict(list)
    for row in data:
        key = (row['geneSymbol'], row['geneId'])
        gene_groups[key].append(row)
    
    print(f"共有 {len(gene_groups)} 个基因组合")
    
    # 执行分析
    results = []
    test_diseases = [d for d in diseases if d != 'Normal']
    total_genes = len(gene_groups)
    
    for i, ((gene_symbol, gene_id), gene_data) in enumerate(gene_groups.items()):
        if i % 500 == 0:
            print(f"处理进度: {i}/{total_genes} 基因")
        
        # 获取Normal组数据
        normal_data = [row for row in gene_data if row['disease'] == 'Normal']
        if len(normal_data) == 0:
            continue
        
        # 对每个变量进行分析
        for variable in available_cols:
            # 获取Normal组的值
            normal_values = [safe_float(row[variable]) for row in normal_data]
            normal_clean = [v for v in normal_values if v is not None]
            
            if len(normal_clean) < 2:
                continue
            
            # 对每个疾病组进行比较
            for disease in test_diseases:
                disease_data = [row for row in gene_data if row['disease'] == disease]
                if len(disease_data) == 0:
                    continue
                
                disease_values = [safe_float(row[variable]) for row in disease_data]
                disease_clean = [v for v in disease_values if v is not None]
                
                if len(disease_clean) < 2:
                    continue
                
                # 执行t检验
                t_stat, p_value = t_test_simple(disease_clean, normal_clean)
                
                if p_value is not None:
                    disease_mean, _, _ = calculate_stats(disease_clean)
                    normal_mean, _, _ = calculate_stats(normal_clean)
                    
                    direction = 'higher' if disease_mean > normal_mean else 'lower'
                    
                    results.append({
                        'geneId': gene_id,
                        'geneSymbol': gene_symbol,
                        'disease_category': disease,
                        'variable': variable,
                        'p_t': p_value,
                        'direction': direction,
                        'disease_mean': disease_mean,
                        'normal_mean': normal_mean,
                        'disease_n': len(disease_clean),
                        'normal_n': len(normal_clean)
                    })
    
    print(f"完成分析，共生成 {len(results)} 个结果")
    return results

def save_results(results, output_file):
    """保存结果"""
    print(f"\n保存结果到: {output_file}")
    
    if not results:
        print("没有结果可保存")
        return
    
    columns = ['geneId', 'geneSymbol', 'disease_category', 'variable', 
               'p_t', 'direction', 'disease_mean', 'normal_mean', 
               'disease_n', 'normal_n']
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=columns)
            writer.writeheader()
            
            for result in results:
                row = {}
                for col in columns:
                    value = result.get(col, '')
                    # 格式化浮点数
                    if isinstance(value, float):
                        row[col] = f"{value:.6f}"
                    else:
                        row[col] = value
                writer.writerow(row)
        
        print("结果保存完成！")
        print(f"结果文件包含 {len(results)} 行数据")
        
        # 显示结果摘要
        genes = set(r['geneSymbol'] for r in results)
        diseases = set(r['disease_category'] for r in results)
        variables = set(r['variable'] for r in results)
        
        print(f"\n结果摘要:")
        print(f"分析的基因数量: {len(genes)}")
        print(f"分析的疾病类型: {len(diseases)}")
        print(f"分析的变量类型: {len(variables)}")
        
        # 显著性结果统计
        significant = sum(1 for r in results if r.get('p_t') is not None and r['p_t'] < 0.05)
        total = len(results)
        print(f"显著性结果 (p<0.05): {significant}/{total}")
        
    except Exception as e:
        print(f"保存结果失败: {e}")

def main():
    """主函数"""
    input_file = "gene/gene_with_translation_indices.csv"
    output_file = "gene_statistical_analysis_results.csv"
    
    print("=== 基因翻译效率统计分析 (纯Python版本) ===")
    
    # 1. 加载数据
    data, headers = load_csv_data(input_file)
    if data is None:
        return
    
    # 2. 分析数据
    results = analyze_data(data, headers)
    if results is None or len(results) == 0:
        print("未生成任何分析结果")
        return
    
    # 3. 保存结果
    save_results(results, output_file)
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
