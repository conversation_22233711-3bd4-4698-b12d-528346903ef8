import os
import csv

def convert_txt_to_csv(input_dir='demo_TPM', output_dir="demo_TPM_csv"):
    """
    将指定目录中的所有TXT文件转换为CSV文件
    
    Args:
        input_dir: 输入目录，包含TXT文件
        output_dir: 输出目录，如果为None，则使用与输入目录相同的目录
    """
    # 如果未指定输出目录，则使用输入目录
    if output_dir is None:
        output_dir = input_dir
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取输入目录中的所有文件
    files = [f for f in os.listdir(input_dir) if f.endswith('.txt')]
    
    total_files = len(files)
    converted_files = 0
    
    print(f"找到 {total_files} 个TXT文件，开始转换...")
    
    for txt_file in files:
        # 构建输入和输出文件路径
        txt_path = os.path.join(input_dir, txt_file)
        csv_file = txt_file.replace('.txt', '.csv')
        csv_path = os.path.join(output_dir, csv_file)
        
        try:
            # 读取TXT文件
            with open(txt_path, 'r') as txt_f:
                content = txt_f.readlines()
            
            # 写入CSV文件
            with open(csv_path, 'w', newline='') as csv_f:
                csv_writer = csv.writer(csv_f)
                
                # 处理每一行
                for line in content:
                    # 使用制表符分割每一行
                    row = line.strip().split('\t')
                    csv_writer.writerow(row)
            
            converted_files += 1
            print(f"已转换: {txt_file} -> {csv_file}")
            
        except Exception as e:
            print(f"转换文件 {txt_file} 时出错: {str(e)}")
    
    print(f"转换完成! 共转换 {converted_files}/{total_files} 个文件。")

if __name__ == "__main__":
    convert_txt_to_csv()