import json
import re
import os
from typing import Dict, Any, List

INPUT_FILE = "processed_output_results_1_cleaned.json"
OUTPUT_FILE = "processed_output_results_1_summary.json"

# 需要处理的字段列表
FEATURE_FIELDS = [
    "Ontologies",
    "miRNAs",
    "Orthologs",
    "PolyA Sites",
    "Conserved Blocks",
    "Rfam motifs",
    "Repeats",
    "uORFs",
    "Variants",
    "Editing",
    "m6A",
    "CAGE(s)",
    "IRES"
]

# 正则表达式，用于解析形如 "1:2408704-2408731:-" 或 "2408704-2408731" 的位置信息
POS_REGEX = re.compile(r"(?P<start>\d+)\s*-\s*(?P<end>\d+)")


def parse_position_length(pos_str: str) -> int:
    """根据给定的字符串计算长度(end - start + 1)。

    兼容多种格式，例如：
    - "4..7"
    - "1471768-1471771"
    - "1:1471768-1471771:+"
    如果无法解析返回 0。
    """
    # 提取所有数字
    nums = re.findall(r"\d+", pos_str)
    if len(nums) < 2:
        return 0

    # 使用最后两个数字作为 start, end，可适配带染色体号的格式
    start = int(nums[-2])
    end = int(nums[-1])

    if end < start:
        start, end = end, start

    return end - start + 1


def summarise_feature(raw_value: Any) -> Dict[str, Any]:
    """将原始字段值转换为 {count, position_avg_length}。
    仅在 count > 0 时返回总结，否则返回空字符串。"""
    if raw_value in (None, "", []):
        return ""  # 返回空字符串保持与原数据一致

    # 将字符串转换为列表
    records: List[Any] = []
    if isinstance(raw_value, str):
        raw_value = raw_value.strip()
        if raw_value.startswith("["):
            # 尝试解析为 JSON 数组
            try:
                records = json.loads(raw_value)
            except Exception:
                # 如果不是有效 JSON，则按换行分割
                records = [line for line in raw_value.split("\n") if line.strip()]
        else:
            # 按换行分割
            records = [line for line in raw_value.split("\n") if line.strip()]
    elif isinstance(raw_value, list):
        records = raw_value
    else:
        # 其它类型，包装成单记录
        records = [raw_value]

    count = len(records)
    if count == 0:
        return ""

    # 计算 position 长度
    total_len = 0
    for rec in records:
        pos_str = None
        # 如果是 dict，查找常见键
        if isinstance(rec, dict):
            pos_str = rec.get("Position") or rec.get("Genomic position") or rec.get("Genomic Position")
        else:
            pos_str = str(rec)
        if pos_str:
            total_len += parse_position_length(pos_str)

    position_avg_length = total_len / count if count else 0

    return {
        "count": count,
        "position_avg_length": round(position_avg_length, 3)
    }


def main():
    if not os.path.exists(INPUT_FILE):
        print(f"输入文件 {INPUT_FILE} 不存在！")
        return

    with open(INPUT_FILE, "r", encoding="utf-8") as f:
        try:
            data = json.load(f)
        except json.JSONDecodeError as e:
            # 文件可能是多行 JSON 对象而不是数组，尝试逐行读取
            f.seek(0)
            data = [json.loads(line) for line in f if line.strip()]

    # data 可以是列表或字典
    if isinstance(data, dict):
        data_list = [data]
    else:
        data_list = data

    for entry in data_list:
        for field in FEATURE_FIELDS:
            if field in entry:
                summary = summarise_feature(entry[field])
                entry[field] = summary

    # 写入输出文件
    with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
        json.dump(data_list, f, ensure_ascii=False, indent=2)

    print(f"处理完成，结果已保存到 {OUTPUT_FILE}")


if __name__ == "__main__":
    main()

