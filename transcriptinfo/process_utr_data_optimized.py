#!/usr/bin/env python3
"""
优化版本：处理转录本UTR数据，将UTR信息添加到unique_transcript_ids.csv文件中
使用更高效的数据处理方法
"""

import pandas as pd
import json
import numpy as np
from typing import Dict, Any
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('process_utr_data_optimized.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)



def process_utr_data_optimized():
    """
    优化的主处理函数 - 使用pandas merge操作提高效率
    """
    logger.info("开始处理UTR数据（优化版本）...")
    
    # 1. 读取unique_transcript_ids.csv
    logger.info("读取unique_transcript_ids.csv...")
    unique_df = pd.read_csv('unique_transcript_ids.csv')
    logger.info(f"读取到 {len(unique_df)} 条转录本记录")
    
    # 2. 读取UTR数据文件
    logger.info("读取merged_transcriptID_utr_table_data_3_with_json_data.csv...")
    # merged_transcriptID_utr_table_data_3_with_json_data.csv
    utr_df = pd.read_csv('merged_transcriptID_utr_table_data_3_with_json_data.csv')
    logger.info(f"读取到 {len(utr_df)} 条UTR记录")
    
    # 3. 预处理UTR数据 - 分离3UTR和5UTR
    logger.info("预处理UTR数据...")
    
    # 分离3UTR和5UTR数据
    three_utr_df = utr_df[utr_df['UTR_Type'] == '3UTR'].copy()
    five_utr_df = utr_df[utr_df['UTR_Type'] == '5UTR'].copy()
    
    logger.info(f"3UTR记录数: {len(three_utr_df)}")
    logger.info(f"5UTR记录数: {len(five_utr_df)}")
    
    # 4. 处理3UTR数据
    logger.info("处理3UTR数据...")
    if len(three_utr_df) > 0:
        # 选择需要的列，包括原始字段
        columns_needed = ['TranscriptID', 'Entry name', 'UTR length', 'URL',
                         'miRNAs', 'PolyA Sites', 'Rfam motifs', 'Repeats', 'uORFs', 'IRES']
        three_utr_processed = three_utr_df[columns_needed].copy()

        # 重命名列以添加前缀
        three_utr_processed.columns = ['transcriptId', 'threeUtrEntryName', 'threeUtrLength', 'threeUtrUrl',
                                      'threeUtrMiRnas', 'threeUtrPolyaSites', 'threeUtrRfamMotifs',
                                      'threeUtrRepeats', 'threeUtrUorfs', 'threeUtrIres']

        # 处理NaN值，替换为空字符串
        utr_feature_cols = ['threeUtrMiRnas', 'threeUtrPolyaSites', 'threeUtrRfamMotifs',
                           'threeUtrRepeats', 'threeUtrUorfs', 'threeUtrIres']
        three_utr_processed[utr_feature_cols] = three_utr_processed[utr_feature_cols].fillna('')

        three_utr_final = three_utr_processed
    else:
        three_utr_final = pd.DataFrame()
    
    # 5. 处理5UTR数据
    logger.info("处理5UTR数据...")
    if len(five_utr_df) > 0:
        # 选择需要的列，包括原始字段
        columns_needed = ['TranscriptID', 'Entry name', 'UTR length', 'URL',
                         'miRNAs', 'PolyA Sites', 'Rfam motifs', 'Repeats', 'uORFs', 'IRES']
        five_utr_processed = five_utr_df[columns_needed].copy()

        # 重命名列以添加前缀
        five_utr_processed.columns = ['transcriptId', 'fiveUtrEntryName', 'fiveUtrLength', 'fiveUtrUrl',
                                     'fiveUtrMiRnas', 'fiveUtrPolyaSites', 'fiveUtrRfamMotifs',
                                     'fiveUtrRepeats', 'fiveUtrUorfs', 'fiveUtrIres']

        # 处理NaN值，替换为空字符串
        utr_feature_cols = ['fiveUtrMiRnas', 'fiveUtrPolyaSites', 'fiveUtrRfamMotifs',
                           'fiveUtrRepeats', 'fiveUtrUorfs', 'fiveUtrIres']
        five_utr_processed[utr_feature_cols] = five_utr_processed[utr_feature_cols].fillna('')

        five_utr_final = five_utr_processed
    else:
        five_utr_final = pd.DataFrame()
    
    # 6. 合并到主数据框
    logger.info("合并数据到主数据框...")
    
    # 先与3UTR数据合并
    if len(three_utr_final) > 0:
        unique_df = pd.merge(unique_df, three_utr_final, on='transcriptId', how='left')
    else:
        # 添加空的3UTR列
        three_utr_cols = ['threeUtrEntryName', 'threeUtrLength', 'threeUtrUrl', 'threeUtrMiRnas',
                         'threeUtrPolyaSites', 'threeUtrRfamMotifs', 'threeUtrRepeats', 'threeUtrUorfs', 'threeUtrIres']
        for col in three_utr_cols:
            unique_df[col] = ''
    
    # 再与5UTR数据合并
    if len(five_utr_final) > 0:
        unique_df = pd.merge(unique_df, five_utr_final, on='transcriptId', how='left')
    else:
        # 添加空的5UTR列
        five_utr_cols = ['fiveUtrEntryName', 'fiveUtrLength', 'fiveUtrUrl', 'fiveUtrMiRnas', 
                        'fiveUtrPolyaSites', 'fiveUtrRfamMotifs', 'fiveUtrRepeats', 'fiveUtrUorfs', 'fiveUtrIres']
        for col in five_utr_cols:
            unique_df[col] = ''
    
    # 填充NaN值为空字符串
    utr_columns = [col for col in unique_df.columns if 'Utr' in col]
    unique_df[utr_columns] = unique_df[utr_columns].fillna('')
    
    # 7. 保存结果
    output_file = 'unique_transcript_ids_with_utr_original_content.csv'
    unique_df.to_csv(output_file, index=False)
    logger.info(f"结果已保存到 {output_file}")
    
    # 8. 统计信息
    logger.info("生成统计信息...")
    
    # 统计有UTR信息的转录本数量
    three_utr_count = (unique_df['threeUtrEntryName'] != '').sum()
    five_utr_count = (unique_df['fiveUtrEntryName'] != '').sum()
    both_utr_count = ((unique_df['threeUtrEntryName'] != '') & 
                      (unique_df['fiveUtrEntryName'] != '')).sum()
    
    logger.info(f"统计结果:")
    logger.info(f"  总转录本数: {len(unique_df)}")
    logger.info(f"  有3'UTR信息的转录本: {three_utr_count}")
    logger.info(f"  有5'UTR信息的转录本: {five_utr_count}")
    logger.info(f"  同时有3'UTR和5'UTR信息的转录本: {both_utr_count}")
    
    return unique_df

if __name__ == '__main__':
    try:
        result_df = process_utr_data_optimized()
        logger.info("UTR数据处理完成！")
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise
