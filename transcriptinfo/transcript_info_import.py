#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将 transcriptInfo_with_updated_locations.csv 文件导入到 MySQL 数据库的 transcriptInfo 表中
"""

import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
import sys
import time

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306,
    'autocommit': False,
    'connection_timeout': 600,
    'use_unicode': True,
    'charset': 'utf8mb4'
}

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('transcript_info_import.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

def create_connection():
    """创建数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        logging.info("成功连接到MySQL数据库")
        return connection
    except Error as e:
        logging.error(f"连接数据库时出错: {e}")
        return None

def check_connection(connection):
    """检查并重新连接数据库"""
    try:
        if not connection.is_connected():
            logging.warning("数据库连接已断开，尝试重新连接...")
            connection.reconnect(attempts=3, delay=2)
            logging.info("数据库重新连接成功")
        return True
    except Error as e:
        logging.error(f"重新连接数据库失败: {e}")
        return False

def create_transcript_info_table(connection):
    """创建transcriptInfo表"""
    cursor = connection.cursor()
    
    try:
        # 删除已存在的表（如果存在）
        drop_table_query = "DROP TABLE IF EXISTS transcriptInfo"
        cursor.execute(drop_table_query)
        logging.info("已删除现有的transcriptInfo表（如果存在）")
        
        # 创建新表
        create_table_query = """
        CREATE TABLE transcriptInfo (
            transcriptId VARCHAR(255) NOT NULL PRIMARY KEY COMMENT '转录本ID',
            geneId VARCHAR(255) NOT NULL COMMENT '基因ID',
            geneSymbol VARCHAR(255) NOT NULL COMMENT '基因符号',
            threeUtrEntryName VARCHAR(500) COMMENT '3UTR条目名称',
            threeUtrLength DECIMAL(10,2) COMMENT '3UTR长度',
            threeUtrUrl TEXT COMMENT '3UTR链接',
            threeUtrMiRnas TEXT COMMENT '3UTR miRNAs信息',
            threeUtrPolyaSites TEXT COMMENT '3UTR PolyA Sites信息',
            threeUtrRfamMotifs TEXT COMMENT '3UTR Rfam motifs信息',
            threeUtrRepeats TEXT COMMENT '3UTR Repeats信息',
            threeUtrUorfs TEXT COMMENT '3UTR uORFs信息',
            threeUtrIres TEXT COMMENT '3UTR IRES信息',
            fiveUtrEntryName VARCHAR(500) COMMENT '5UTR条目名称',
            fiveUtrLength DECIMAL(10,2) COMMENT '5UTR长度',
            fiveUtrUrl TEXT COMMENT '5UTR链接',
            fiveUtrMiRnas TEXT COMMENT '5UTR miRNAs信息',
            fiveUtrPolyaSites TEXT COMMENT '5UTR PolyA Sites信息',
            fiveUtrRfamMotifs TEXT COMMENT '5UTR Rfam motifs信息',
            fiveUtrRepeats TEXT COMMENT '5UTR Repeats信息',
            fiveUtrUorfs TEXT COMMENT '5UTR uORFs信息',
            fiveUtrIres TEXT COMMENT '5UTR IRES信息',
            transcriptLocation VARCHAR(1000) COMMENT '转录本位置信息'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        
        cursor.execute(create_table_query)
        logging.info("成功创建transcriptInfo表")
        
        # 创建索引以提高检索速度
        indexes = [
            "CREATE INDEX idx_gene_id ON transcriptInfo (geneId)",
            "CREATE INDEX idx_gene_symbol ON transcriptInfo (geneSymbol)",
            "CREATE INDEX idx_gene_combo ON transcriptInfo (geneId, geneSymbol)"
        ]
        
        for index_query in indexes:
            cursor.execute(index_query)
            logging.info(f"成功创建索引: {index_query.split('INDEX ')[1].split(' ON')[0]}")
        
        # 提交事务
        connection.commit()
        
    except Error as e:
        logging.error(f"创建表时出错: {e}")
        connection.rollback()
        raise
    finally:
        cursor.close()

def import_transcript_info_data(connection, csv_file_path):
    """导入转录本信息数据到数据库"""
    try:
        # 读取CSV文件
        logging.info(f"开始读取CSV文件: {csv_file_path}")
        
        # 由于文件可能很大，使用分块读取
        chunk_size = 5000
        total_processed = 0
        success_count = 0
        
        # 准备插入语句
        insert_query = """
        INSERT INTO transcriptInfo (
            transcriptId, geneId, geneSymbol, threeUtrEntryName, threeUtrLength, threeUtrUrl,
            threeUtrMiRnas, threeUtrPolyaSites, threeUtrRfamMotifs, threeUtrRepeats, threeUtrUorfs, threeUtrIres,
            fiveUtrEntryName, fiveUtrLength, fiveUtrUrl, fiveUtrMiRnas, fiveUtrPolyaSites,
            fiveUtrRfamMotifs, fiveUtrRepeats, fiveUtrUorfs, fiveUtrIres, transcriptLocation
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        # 分块处理CSV文件
        for chunk_df in pd.read_csv(csv_file_path, chunksize=chunk_size):
            # 检查连接状态
            if not check_connection(connection):
                logging.error("数据库连接检查失败，停止导入")
                break
            
            # 清理数据 - 处理NaN值
            chunk_df = chunk_df.fillna('')
            
            cursor = connection.cursor()
            
            try:
                # 准备批量数据
                batch_data = []
                for _, row in chunk_df.iterrows():
                    # 处理数值字段
                    def safe_float(value):
                        if pd.isna(value) or value == '' or value == 'nan':
                            return None
                        try:
                            return float(value)
                        except (ValueError, TypeError):
                            return None
                    
                    # 处理文本字段
                    def safe_str(value):
                        if pd.isna(value):
                            return None
                        return str(value) if str(value) != 'nan' else None
                    
                    batch_data.append((
                        str(row['transcriptId']),
                        str(row['geneId']),
                        str(row['geneSymbol']),
                        safe_str(row['threeUtrEntryName']),
                        safe_float(row['threeUtrLength']),
                        safe_str(row['threeUtrUrl']),
                        safe_str(row['threeUtrMiRnas']),
                        safe_str(row['threeUtrPolyaSites']),
                        safe_str(row['threeUtrRfamMotifs']),
                        safe_str(row['threeUtrRepeats']),
                        safe_str(row['threeUtrUorfs']),
                        safe_str(row['threeUtrIres']),
                        safe_str(row['fiveUtrEntryName']),
                        safe_float(row['fiveUtrLength']),
                        safe_str(row['fiveUtrUrl']),
                        safe_str(row['fiveUtrMiRnas']),
                        safe_str(row['fiveUtrPolyaSites']),
                        safe_str(row['fiveUtrRfamMotifs']),
                        safe_str(row['fiveUtrRepeats']),
                        safe_str(row['fiveUtrUorfs']),
                        safe_str(row['fiveUtrIres']),
                        safe_str(row['transcriptLocation'])
                    ))
                
                # 执行批量插入
                cursor.executemany(insert_query, batch_data)
                connection.commit()
                
                success_count += len(batch_data)
                total_processed += len(chunk_df)
                
                logging.info(f"已处理 {total_processed} 行数据 (成功导入: {success_count})")
                
                # 每处理10个块休息一下，避免连接超时
                if (total_processed // chunk_size) % 10 == 0:
                    time.sleep(0.2)
                
            except Error as e:
                logging.error(f"批次导入失败 (行数 {total_processed-len(chunk_df)+1}-{total_processed}): {e}")
                connection.rollback()
                # 继续处理下一批次
                continue
            finally:
                cursor.close()
        
        logging.info(f"数据导入完成！总共处理 {total_processed} 行，成功导入 {success_count} 行数据")
        
        # 显示统计信息
        show_statistics(connection)
        
    except Exception as e:
        logging.error(f"导入数据时出错: {e}")
        raise

def show_statistics(connection):
    """显示导入统计信息"""
    if not check_connection(connection):
        logging.error("无法连接数据库，跳过统计信息显示")
        return
        
    cursor = connection.cursor()
    
    try:
        # 总记录数
        cursor.execute("SELECT COUNT(*) FROM transcriptInfo")
        total_count = cursor.fetchone()[0]
        logging.info(f"表中总记录数: {total_count}")
        
        # 唯一转录本数
        cursor.execute("SELECT COUNT(DISTINCT transcriptId) FROM transcriptInfo")
        unique_transcripts = cursor.fetchone()[0]
        logging.info(f"唯一转录本数: {unique_transcripts}")
        
        # 唯一基因数
        cursor.execute("SELECT COUNT(DISTINCT geneSymbol) FROM transcriptInfo")
        unique_genes = cursor.fetchone()[0]
        logging.info(f"唯一基因数: {unique_genes}")
        
        # 有3'UTR信息的记录数
        cursor.execute("SELECT COUNT(*) FROM transcriptInfo WHERE threeUtrEntryName IS NOT NULL AND threeUtrEntryName != ''")
        three_utr_count = cursor.fetchone()[0]
        logging.info(f"有3'UTR信息的记录数: {three_utr_count}")
        
        # 有5'UTR信息的记录数
        cursor.execute("SELECT COUNT(*) FROM transcriptInfo WHERE fiveUtrEntryName IS NOT NULL AND fiveUtrEntryName != ''")
        five_utr_count = cursor.fetchone()[0]
        logging.info(f"有5'UTR信息的记录数: {five_utr_count}")
        
        # 有位置信息的记录数
        cursor.execute("SELECT COUNT(*) FROM transcriptInfo WHERE transcriptLocation IS NOT NULL AND transcriptLocation != '' AND transcriptLocation != 'NA'")
        location_count = cursor.fetchone()[0]
        logging.info(f"有位置信息的记录数: {location_count}")
            
    except Error as e:
        logging.error(f"显示统计信息时出错: {e}")
    finally:
        cursor.close()

def main():
    """主函数"""
    csv_file_path = "unique_transcript_ids_with_utr_lastest.csv"
    
    # 检查文件是否存在
    import os
    if not os.path.exists(csv_file_path):
        logging.error(f"CSV文件不存在: {csv_file_path}")
        return
    
    # 创建数据库连接
    connection = create_connection()
    if connection is None:
        logging.error("无法连接到数据库，程序退出")
        return
    
    try:
        # 创建表
        create_transcript_info_table(connection)
        
        # 导入数据
        import_transcript_info_data(connection, csv_file_path)
        
        logging.info("转录本信息数据导入任务完成！")
        
    except Exception as e:
        logging.error(f"程序执行出错: {e}")
    finally:
        if connection and connection.is_connected():
            connection.close()
            logging.info("数据库连接已关闭")

if __name__ == "__main__":
    main() 