#!/usr/bin/env python3
"""
处理transcriptInfo_with_updated_locations.csv文件：
1. 将12个UTR特征列的值转换为yes/no
2. 计算所有可能的组合
3. 为每种组合收集对应的transcriptId
"""

import pandas as pd
import os
import sys
import json

def is_non_empty(value):
    """检查值是否非空"""
    if pd.isna(value):
        return False
    if isinstance(value, str):
        value = value.strip()
        if value == '' or value.lower() == 'nan' or value.lower() == 'na':
            return False
    return True

def convert_to_yes_no(value):
    """将值转换为yes或no"""
    return 'yes' if is_non_empty(value) else 'no'

def process_transcript_info(input_file, output_file, json_file, max_csv_transcripts=100, json_rows_file=None):
    """处理转录本信息文件"""
    
    print(f"开始处理文件: {input_file}")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 {input_file}")
        return False
    
    # 读取CSV文件
    try:
        df = pd.read_csv(input_file)
        print(f"成功读取文件，共 {len(df)} 行")
        print(f"列数: {len(df.columns)}")
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return False
    
    # 定义需要处理的12个UTR特征列
    utr_feature_columns = [
        'threeUtrMiRnas', 'threeUtrPolyaSites', 'threeUtrRfamMotifs',
        'threeUtrRepeats', 'threeUtrUorfs', 'threeUtrIres',
        'fiveUtrMiRnas', 'fiveUtrPolyaSites', 'fiveUtrRfamMotifs',
        'fiveUtrRepeats', 'fiveUtrUorfs', 'fiveUtrIres'
    ]
    
    # 检查必需的列是否存在
    missing_columns = []
    for col in utr_feature_columns + ['transcriptId', 'geneId']:
        if col not in df.columns:
            missing_columns.append(col)
    
    if missing_columns:
        print(f"错误: 缺少必需的列: {missing_columns}")
        return False
    
    print(f"找到所有必需的列")
    
    # 创建工作副本
    work_df = df.copy()
    
    # 转换UTR特征列为yes/no
    print(f"\n转换UTR特征列为yes/no...")
    for col in utr_feature_columns:
        original_non_empty = work_df[col].apply(is_non_empty).sum()
        work_df[col] = work_df[col].apply(convert_to_yes_no)
        yes_count = (work_df[col] == 'yes').sum()
        no_count = (work_df[col] == 'no').sum()
        
        print(f"  {col}: {original_non_empty} 非空 -> {yes_count} yes, {no_count} no")
    
    # 创建组合并统计
    print(f"\n计算UTR特征组合...")
    
    # 按UTR特征组合分组
    grouped = work_df.groupby(utr_feature_columns)
    
    print(f"找到 {len(grouped)} 种不同的UTR特征组合")
    
    # 创建结果列表（用于CSV与“CSV样式JSON”）
    results = []
    
    # 用于存储完整数据的字典
    all_combinations_data = {}

    for combination, group in grouped:
        # 获取该组合下的所有唯一transcriptId和geneId
        transcript_ids = group['transcriptId'].unique().tolist()
        # 过滤掉NaN值的geneId
        gene_ids = group['geneId'].dropna().unique().tolist()
        sorted_transcript_ids = sorted(transcript_ids)
        # 确保所有gene_ids都是字符串且非空
        valid_gene_ids = [str(gid) for gid in gene_ids if gid is not None and str(gid) != 'nan']
        sorted_gene_ids = sorted(valid_gene_ids)

        # 为CSV限制transcript和gene数量
        limited_transcript_ids = sorted_transcript_ids[:max_csv_transcripts]
        limited_gene_ids = sorted_gene_ids[:max_csv_transcripts]
        transcript_ids_str = ';'.join(limited_transcript_ids)
        gene_ids_str = ';'.join(limited_gene_ids)

        # 创建结果行（仅三列：transcriptId, geneId, UTR_Features）
        features_binary = ','.join(['1' if val == 'yes' else '0' for val in combination])
        result_row = {
            'transcriptId': transcript_ids_str,
            'geneId': gene_ids_str,
            'UTR_Features': features_binary,
        }
        results.append(result_row)

        # 为JSON保存完整数据
        combination_key = '|'.join(combination)
        combination_dict = {}
        for i, col in enumerate(utr_feature_columns):
            combination_dict[col] = combination[i]

        all_combinations_data[combination_key] = {
            'features': combination_dict,
            'transcript_count': len(transcript_ids),
            'transcript_ids': sorted_transcript_ids,
            'gene_count': len(gene_ids),
            'gene_ids': sorted_gene_ids
        }
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(results)
    
    # 按UTR_Features排序（可选，便于查看）
    result_df = result_df.sort_values(['UTR_Features']).reset_index(drop=True)
    
    print(f"\n组合统计:")
    print(f"总组合数: {len(result_df)}")
    
    # 统计每种组合的转录本和基因数量
    result_df['transcript_count'] = result_df['transcriptId'].apply(lambda x: len(x.split(';')) if isinstance(x, str) and x else 0)
    result_df['gene_count'] = result_df['geneId'].apply(lambda x: len(x.split(';')) if isinstance(x, str) and x else 0)

    print(f"转录本数量分布:")
    count_stats = result_df['transcript_count'].value_counts().sort_index()
    for count, freq in count_stats.items():
        print(f"  {count}个转录本: {freq} 种组合")

    print(f"基因数量分布:")
    gene_count_stats = result_df['gene_count'].value_counts().sort_index()
    for count, freq in gene_count_stats.items():
        print(f"  {count}个基因: {freq} 种组合")

    print(f"平均每种组合包含转录本数: {result_df['transcript_count'].mean():.2f}")
    print(f"最多转录本的组合: {result_df['transcript_count'].max()} 个")
    print(f"最少转录本的组合: {result_df['transcript_count'].min()} 个")

    print(f"平均每种组合包含基因数: {result_df['gene_count'].mean():.2f}")
    print(f"最多基因的组合: {result_df['gene_count'].max()} 个")
    print(f"最少基因的组合: {result_df['gene_count'].min()} 个")

    # 删除临时统计列
    result_df = result_df.drop(['transcript_count', 'gene_count'], axis=1)
    
    # 显示前几个组合示例
    print(f"\n前5个组合示例:")
    for i, (_, row) in enumerate(result_df.head(5).iterrows()):
        transcript_count = len(row['transcriptId'].split(';'))
        gene_count = len(row['geneId'].split(';'))
        print(f"  组合 {i+1}: {transcript_count} 个转录本, {gene_count} 个基因")

        # 显示UTR特征（根据UTR_Features的0/1串还原）
        three_utr_features = []
        five_utr_features = []
        bin_vals = [v.strip() for v in str(row['UTR_Features']).split(',')]
        for idx, col in enumerate(utr_feature_columns):
            if idx < len(bin_vals) and bin_vals[idx] == '1':
                if col.startswith('threeUtr'):
                    three_utr_features.append(col.replace('threeUtr', ''))
                elif col.startswith('fiveUtr'):
                    five_utr_features.append(col.replace('fiveUtr', ''))

        print(f"    3UTR特征: {', '.join(three_utr_features) if three_utr_features else '无'}")
        print(f"    5UTR特征: {', '.join(five_utr_features) if five_utr_features else '无'}")

        # 显示前几个转录本ID
        transcript_ids = row['transcriptId'].split(';')
        if len(transcript_ids) <= 3:
            print(f"    转录本: {', '.join(transcript_ids)}")
        else:
            print(f"    转录本: {', '.join(transcript_ids[:3])}... (共{len(transcript_ids)}个)")

        # 显示前几个基因ID
        gene_ids = row['geneId'].split(';')
        if len(gene_ids) <= 3:
            print(f"    基因: {', '.join(gene_ids)}")
        else:
            print(f"    基因: {', '.join(gene_ids[:3])}... (共{len(gene_ids)}个)")
    
    # 保存结果
    try:
        # 保存CSV文件
        result_df.to_csv(output_file, index=False)
        print(f"\n成功保存CSV结果到: {output_file}")
        print(f"CSV文件中每种组合最多显示 {max_csv_transcripts} 个transcript_id")

        # 保存JSON文件（完整数据：含全部ID列表和计数）
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(all_combinations_data, f, ensure_ascii=False, indent=2)
        print(f"成功保存完整数据到: {json_file}")

        # 额外保存：CSV样式的JSON（与CSV列一致，使用受限数量的ID，分号拼接）
        if json_rows_file:
            with open(json_rows_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"成功保存CSV样式JSON到: {json_rows_file}")

        # 验证保存的文件
        saved_df = pd.read_csv(output_file)
        if len(saved_df) == len(result_df):
            print(f"验证成功: CSV文件包含 {len(saved_df)} 行")
        else:
            print(f"警告: 保存的文件行数 ({len(saved_df)}) 与预期不符 ({len(result_df)})")

        # 验证JSON文件
        with open(json_file, 'r', encoding='utf-8') as f:
            saved_json = json.load(f)
        if len(saved_json) == len(all_combinations_data):
            print(f"验证成功: JSON文件包含 {len(saved_json)} 种组合")
        else:
            print(f"警告: JSON文件组合数 ({len(saved_json)}) 与预期不符 ({len(all_combinations_data)})")

        if json_rows_file:
            with open(json_rows_file, 'r', encoding='utf-8') as f:
                saved_rows_json = json.load(f)
            if isinstance(saved_rows_json, list) and len(saved_rows_json) == len(result_df):
                print(f"验证成功: CSV样式JSON包含 {len(saved_rows_json)} 行")
            else:
                print(f"警告: CSV样式JSON行数与预期不符 (预期 {len(result_df)})")

    except Exception as e:
        print(f"保存文件时出错: {e}")
        return False
    
    return True

def analyze_combinations(output_file):
    """分析组合结果"""
    print(f"\n分析组合结果...")
    
    try:
        df = pd.read_csv(output_file)

        # 统计yes分布（解析UTR_Features的0/1串）
        utr_feature_columns = [
            'threeUtrMiRnas', 'threeUtrPolyaSites', 'threeUtrRfamMotifs',
            'threeUtrRepeats', 'threeUtrUorfs', 'threeUtrIres',
            'fiveUtrMiRnas', 'fiveUtrPolyaSites', 'fiveUtrRfamMotifs',
            'fiveUtrRepeats', 'fiveUtrUorfs', 'fiveUtrIres'
        ]

        def parse_bin_list(s):
            vals = [v.strip() for v in str(s).split(',')]
            return [1 if (i < len(vals) and vals[i] == '1') else 0 for i in range(len(utr_feature_columns))]

        bin_matrix = df['UTR_Features'].apply(parse_bin_list)
        # 汇总每列的yes数量
        yes_counts = [int(sum(row[i] for row in bin_matrix)) for i in range(len(utr_feature_columns))]
        total_count = len(df)

        print(f"\n各特征的yes组合数统计:")
        for i, col in enumerate(utr_feature_columns):
            percentage = (yes_counts[i] / total_count * 100) if total_count > 0 else 0
            feature_name = col.replace('threeUtr', '3UTR-').replace('fiveUtr', '5UTR-')
            print(f"  {feature_name}: {yes_counts[i]}/{total_count} ({percentage:.1f}%)")

        # 统计特征数量分布
        print(f"\n特征数量分布:")
        df['total_yes_features'] = bin_matrix.apply(sum)
        feature_count_stats = df['total_yes_features'].value_counts().sort_index()
        for count, freq in feature_count_stats.items():
            print(f"  {count}个yes特征: {freq} 种组合")

        # 分析转录本数量最多的前5个组合
        print(f"\n转录本数量最多的前5个组合:")
        df['transcript_count'] = df['transcriptId'].apply(lambda x: len(x.split(';')))
        top_combinations = df.nlargest(5, 'transcript_count')
        
        for i, (_, row) in enumerate(top_combinations.iterrows()):
            print(f"  {i+1}. {row['transcript_count']} 个转录本:")
            
            yes_features = []
            vals = parse_bin_list(row['UTR_Features'])
            for idx, col in enumerate(utr_feature_columns):
                if vals[idx] == 1:
                    feature_name = col.replace('threeUtr', '3UTR-').replace('fiveUtr', '5UTR-')
                    yes_features.append(feature_name)
            
            print(f"     特征: {', '.join(yes_features) if yes_features else '全部为no'}")
        
    except Exception as e:
        print(f"分析组合结果时出错: {e}")

def main():
    """主函数"""
    input_file = "transcriptinfo/unique_transcript_ids_with_utr_lastest.csv"
    output_file = "transcriptinfo/transcriptInfo_utr_combinations.csv"
    json_file = "transcriptinfo/transcriptInfo_utr_combinations_full.json"   # 完整JSON（含全部ID列表）
    json_rows_file = "transcriptinfo/transcriptInfo_utr_combinations_rows.json"  # CSV样式JSON（与CSV列相同）
    max_csv_transcripts = 999999  # CSV文件中每种组合最多保存的transcript数量

    print("转录本UTR特征组合处理工具")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print(f"CSV输出文件: {output_file}")
    print(f"JSON输出文件(完整): {json_file}")
    print(f"JSON输出文件(行样式): {json_rows_file}")
    print(f"CSV文件中每种组合最多保存: {max_csv_transcripts} 个transcript")

    # 执行处理
    success = process_transcript_info(input_file, output_file, json_file, max_csv_transcripts, json_rows_file)

    if success:
        # 分析结果
        analyze_combinations(output_file)

        print(f"\n处理完成！")
        print(f"CSV结果已保存到: {output_file} (每种组合最多{max_csv_transcripts}个transcript和gene)")
        print(f"完整数据已保存到: {json_file} (包含所有transcript和gene)")
        print(f"CSV样式JSON已保存到: {json_rows_file} (与CSV列一致，ID为分号分隔字符串)")
        print(f"CSV文件包含3列：transcriptId, geneId, UTR_Features(12位0/1，用逗号分隔)")
    else:
        print(f"\n处理失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()
