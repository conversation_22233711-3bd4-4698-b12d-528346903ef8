#!/usr/bin/env python3
"""
处理转录本UTR数据，将UTR信息添加到unique_transcript_ids.csv文件中
"""

import pandas as pd
import json
import numpy as np
from typing import Dict, Any
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('process_utr_data.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def extract_utr_info_from_json(utr_comp_str: str) -> Dict[str, str]:
    """
    从UtrComp JSON字符串中提取UTR信息
    """
    try:
        if pd.isna(utr_comp_str) or utr_comp_str == '':
            return {}
        
        # 解析JSON
        utr_data = json.loads(utr_comp_str)
        
        # 提取需要的字段
        extracted = {}
        field_mapping = {
            'IRES': 'Ires',
            'miRNAs': 'MiRnas', 
            'PolyA Sites': 'PolyaSites',
            'Rfam motifs': 'RfamMotifs',
            'Repeats': 'Repeats',
            'uORFs': 'Uorfs'
        }
        
        for json_key, field_suffix in field_mapping.items():
            if json_key in utr_data:
                extracted[field_suffix] = str(utr_data[json_key])
            else:
                extracted[field_suffix] = ''
                
        return extracted
        
    except (json.JSONDecodeError, TypeError) as e:
        logger.warning(f"解析JSON失败: {e}, 原始数据: {utr_comp_str}")
        return {}

def process_utr_data():
    """
    主处理函数
    """
    logger.info("开始处理UTR数据...")
    
    # 1. 读取unique_transcript_ids.csv
    logger.info("读取unique_transcript_ids.csv...")
    unique_df = pd.read_csv('unique_transcript_ids.csv')
    logger.info(f"读取到 {len(unique_df)} 条转录本记录")

    # 2. 读取UTR数据文件
    logger.info("读取merged_transcriptID_utr_table_data_3_with_json_data.csv...")
    utr_df = pd.read_csv('merged_transcriptID_utr_table_data_3_with_json_data.csv')
    logger.info(f"读取到 {len(utr_df)} 条UTR记录")
    
    # 3. 添加新列到unique_df
    new_columns = [
        'threeUtrEntryName', 'threeUtrLength', 'threeUtrUrl', 'threeUtrMiRnas',
        'threeUtrPolyaSites', 'threeUtrRfamMotifs', 'threeUtrRepeats', 'threeUtrUorfs', 'threeUtrIres',
        'fiveUtrEntryName', 'fiveUtrLength', 'fiveUtrUrl', 'fiveUtrMiRnas', 
        'fiveUtrPolyaSites', 'fiveUtrRfamMotifs', 'fiveUtrRepeats', 'fiveUtrUorfs', 'fiveUtrIres'
    ]
    
    for col in new_columns:
        unique_df[col] = ''
    
    logger.info("添加了新列到数据框")
    
    # 4. 处理UTR数据
    logger.info("开始处理UTR数据映射...")
    
    processed_count = 0
    total_transcripts = len(unique_df)
    
    for idx, row in unique_df.iterrows():
        transcript_id = row['transcriptId']
        
        # 查找该转录本的UTR记录
        transcript_utrs = utr_df[utr_df['TranscriptID'] == transcript_id]
        
        if len(transcript_utrs) == 0:
            continue
            
        # 处理3UTR和5UTR
        for _, utr_row in transcript_utrs.iterrows():
            utr_type = utr_row['UTR_Type']
            
            if utr_type == '3UTR':
                prefix = 'threeUtr'
            elif utr_type == '5UTR':
                prefix = 'fiveUtr'
            else:
                continue
                
            # 基本信息
            unique_df.at[idx, f'{prefix}EntryName'] = str(utr_row.get('Entry name', ''))
            unique_df.at[idx, f'{prefix}Length'] = str(utr_row.get('UTR length', ''))
            unique_df.at[idx, f'{prefix}Url'] = str(utr_row.get('URL', ''))
            
            # 从UtrComp JSON中提取信息
            utr_comp = utr_row.get('UtrComp', '')
            json_info = extract_utr_info_from_json(utr_comp)
            
            # 设置JSON提取的信息
            for field_suffix, value in json_info.items():
                unique_df.at[idx, f'{prefix}{field_suffix}'] = value
        
        processed_count += 1
        if processed_count % 1000 == 0:
            logger.info(f"已处理 {processed_count}/{total_transcripts} 条转录本记录")
    
    logger.info(f"UTR数据处理完成，共处理 {processed_count} 条记录")
    
    # 5. 保存结果
    output_file = 'unique_transcript_ids_with_utr.csv'
    unique_df.to_csv(output_file, index=False)
    logger.info(f"结果已保存到 {output_file}")
    
    # 6. 统计信息
    logger.info("生成统计信息...")
    
    # 统计有UTR信息的转录本数量
    three_utr_count = (unique_df['threeUtrEntryName'] != '').sum()
    five_utr_count = (unique_df['fiveUtrEntryName'] != '').sum()
    both_utr_count = ((unique_df['threeUtrEntryName'] != '') & 
                      (unique_df['fiveUtrEntryName'] != '')).sum()
    
    logger.info(f"统计结果:")
    logger.info(f"  总转录本数: {len(unique_df)}")
    logger.info(f"  有3'UTR信息的转录本: {three_utr_count}")
    logger.info(f"  有5'UTR信息的转录本: {five_utr_count}")
    logger.info(f"  同时有3'UTR和5'UTR信息的转录本: {both_utr_count}")
    
    return unique_df

if __name__ == '__main__':
    try:
        result_df = process_utr_data()
        logger.info("UTR数据处理完成！")
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        raise
