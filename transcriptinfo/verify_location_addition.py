#!/usr/bin/env python3
"""
验证转录本位置信息添加的结果
"""

import pandas as pd

def verify_results():
    """
    验证处理结果
    """
    try:
        # 读取结果文件
        df = pd.read_csv('unique_transcript_ids_with_utr_and_location.csv')
        
        print("=== 转录本位置信息添加结果验证 ===")
        print(f"总转录本数: {len(df):,}")
        print(f"总列数: {len(df.columns)}")
        
        # 检查新添加的列
        if 'transcriptLocation' in df.columns:
            print("✅ transcriptLocation列已成功添加")
            
            # 统计有位置信息的转录本
            has_location = df['transcriptLocation'] != ''
            location_count = has_location.sum()
            no_location_count = len(df) - location_count
            
            print(f"\n=== 位置信息统计 ===")
            print(f"有位置信息的转录本: {location_count:,} ({location_count/len(df)*100:.1f}%)")
            print(f"没有位置信息的转录本: {no_location_count:,} ({no_location_count/len(df)*100:.1f}%)")
            
            # 显示一些示例
            print(f"\n=== 示例数据 ===")
            sample_with_location = df[has_location].head(5)
            for idx, row in sample_with_location.iterrows():
                print(f"转录本: {row['transcriptId']}")
                print(f"  基因: {row['geneSymbol']}")
                print(f"  位置: {row['transcriptLocation']}")
                print()
            
            # 检查没有位置信息的转录本
            if no_location_count > 0:
                print(f"=== 没有位置信息的转录本示例 ===")
                sample_no_location = df[~has_location].head(3)
                for idx, row in sample_no_location.iterrows():
                    print(f"转录本: {row['transcriptId']} ({row['geneSymbol']})")
            
            # 检查列的完整性
            print(f"\n=== 列信息 ===")
            print("所有列名:")
            for i, col in enumerate(df.columns, 1):
                print(f"{i:2d}. {col}")
                
        else:
            print("❌ transcriptLocation列未找到")
            
    except FileNotFoundError:
        print("❌ 结果文件未找到: unique_transcript_ids_with_utr_and_location.csv")
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")

if __name__ == '__main__':
    verify_results()
