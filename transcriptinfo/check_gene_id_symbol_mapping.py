#!/usr/bin/env python3
"""
检查基因ID和基因符号映射关系

检查unique_transcript_gene_mapping_with_location_updated.csv文件中
是否存在某一个GENE ID对应多个GENE symbol的情况。
"""

import csv
import logging
from collections import defaultdict

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('check_gene_id_symbol_mapping.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def check_gene_mapping():
    """检查基因ID和基因符号的映射关系"""
    try:
        # 存储基因ID到基因符号的映射
        gene_id_to_symbols = defaultdict(set)
        # 存储基因符号到基因ID的映射
        symbol_to_gene_ids = defaultdict(set)
        
        total_rows = 0
        
        logging.info("正在读取unique_transcript_gene_mapping_with_location_updated.csv文件...")
        
        with open('unique_transcript_gene_mapping_with_location_updated.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                total_rows += 1
                gene_id = row['GENE ID']
                gene_symbol = row['GENE symbol']
                
                if gene_id and gene_symbol:  # 确保不为空
                    gene_id_to_symbols[gene_id].add(gene_symbol)
                    symbol_to_gene_ids[gene_symbol].add(gene_id)
                
                # 每处理10000行输出一次进度
                if total_rows % 10000 == 0:
                    logging.info(f"已处理 {total_rows} 行")
        
        logging.info(f"文件读取完成，总共处理了 {total_rows} 行")
        
        # 检查一个GENE ID对应多个GENE symbol的情况
        multi_symbol_gene_ids = []
        for gene_id, symbols in gene_id_to_symbols.items():
            if len(symbols) > 1:
                multi_symbol_gene_ids.append((gene_id, symbols))
        
        # 检查一个GENE symbol对应多个GENE ID的情况
        multi_id_symbols = []
        for symbol, gene_ids in symbol_to_gene_ids.items():
            if len(gene_ids) > 1:
                multi_id_symbols.append((symbol, gene_ids))
        
        # 输出统计结果
        logging.info("\n=== 映射关系统计结果 ===")
        logging.info(f"唯一GENE ID数量: {len(gene_id_to_symbols)}")
        logging.info(f"唯一GENE symbol数量: {len(symbol_to_gene_ids)}")
        logging.info(f"一对一映射的GENE ID数量: {len(gene_id_to_symbols) - len(multi_symbol_gene_ids)}")
        logging.info(f"一个GENE ID对应多个GENE symbol的数量: {len(multi_symbol_gene_ids)}")
        logging.info(f"一个GENE symbol对应多个GENE ID的数量: {len(multi_id_symbols)}")
        
        # 保存一个GENE ID对应多个GENE symbol的详细信息
        if multi_symbol_gene_ids:
            logging.info(f"\n发现 {len(multi_symbol_gene_ids)} 个GENE ID对应多个GENE symbol:")
            
            with open('gene_id_multiple_symbols.csv', 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['GENE_ID', 'GENE_SYMBOLS', 'SYMBOL_COUNT'])
                
                for gene_id, symbols in multi_symbol_gene_ids:
                    symbols_str = '; '.join(sorted(symbols))
                    writer.writerow([gene_id, symbols_str, len(symbols)])
                    logging.info(f"  {gene_id}: {symbols_str} (共{len(symbols)}个符号)")
            
            logging.info("详细信息已保存到gene_id_multiple_symbols.csv")
        else:
            logging.info("\n✓ 没有发现一个GENE ID对应多个GENE symbol的情况")
        
        # 保存一个GENE symbol对应多个GENE ID的详细信息
        if multi_id_symbols:
            logging.info(f"\n发现 {len(multi_id_symbols)} 个GENE symbol对应多个GENE ID:")
            
            with open('gene_symbol_multiple_ids.csv', 'w', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['GENE_SYMBOL', 'GENE_IDS', 'ID_COUNT'])
                
                for symbol, gene_ids in multi_id_symbols:
                    gene_ids_str = '; '.join(sorted(gene_ids))
                    writer.writerow([symbol, gene_ids_str, len(gene_ids)])
                    if len(multi_id_symbols) <= 20:  # 只显示前20个，避免输出过多
                        logging.info(f"  {symbol}: {gene_ids_str} (共{len(gene_ids)}个ID)")
            
            logging.info("详细信息已保存到gene_symbol_multiple_ids.csv")
        else:
            logging.info("\n✓ 没有发现一个GENE symbol对应多个GENE ID的情况")
        
        # 检查空值情况
        empty_gene_id_count = 0
        empty_symbol_count = 0
        
        with open('unique_transcript_gene_mapping_with_location_updated.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if not row['GENE ID'] or row['GENE ID'].strip() == '':
                    empty_gene_id_count += 1
                if not row['GENE symbol'] or row['GENE symbol'].strip() == '':
                    empty_symbol_count += 1
        
        logging.info(f"\n=== 空值统计 ===")
        logging.info(f"空GENE ID的行数: {empty_gene_id_count}")
        logging.info(f"空GENE symbol的行数: {empty_symbol_count}")
        
        return {
            'total_rows': total_rows,
            'unique_gene_ids': len(gene_id_to_symbols),
            'unique_symbols': len(symbol_to_gene_ids),
            'multi_symbol_gene_ids': len(multi_symbol_gene_ids),
            'multi_id_symbols': len(multi_id_symbols),
            'empty_gene_id_count': empty_gene_id_count,
            'empty_symbol_count': empty_symbol_count
        }
        
    except Exception as e:
        logging.error(f"检查映射关系时出错: {str(e)}")
        raise

def main():
    """主函数"""
    logging.info("开始检查基因ID和基因符号映射关系")
    
    try:
        stats = check_gene_mapping()
        
        logging.info("\n=== 检查完成 ===")
        if stats['multi_symbol_gene_ids'] == 0:
            logging.info("✓ 映射关系良好：每个GENE ID只对应一个GENE symbol")
        else:
            logging.info(f"⚠ 发现问题：有 {stats['multi_symbol_gene_ids']} 个GENE ID对应多个GENE symbol")
        
        logging.info("检查任务完成")
        
    except Exception as e:
        logging.error(f"执行过程中出错: {str(e)}")
        raise

if __name__ == "__main__":
    main() 