#!/usr/bin/env python3
"""
更新基因信息脚本

通过transcript_id在Gene_transcript.csv中查找匹配的"Transcript stable ID"，
然后用对应的"Gene stable ID"和"Gene name"更新unique_transcript_gene_mapping_with_location.csv
文件中的"GENE ID"和"GENE symbol"列。
"""

import csv
import logging
from datetime import datetime
import os

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_gene_info_from_transcript.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def load_gene_transcript_mapping():
    """加载Gene_transcript.csv文件，创建transcript_id到基因信息的映射"""
    try:
        logging.info("正在加载Gene_transcript.csv文件...")
        transcript_to_gene = {}
        
        with open('Gene_transcript.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                transcript_id = row['Transcript stable ID']
                gene_id = row['Gene stable ID']
                gene_name = row['Gene name']
                
                if transcript_id and gene_id and gene_name:
                    transcript_to_gene[transcript_id] = {
                        'gene_id': gene_id,
                        'gene_name': gene_name
                    }
        
        logging.info(f"Gene_transcript.csv文件加载完成，共{len(transcript_to_gene)}个转录本映射")
        return transcript_to_gene
        
    except Exception as e:
        logging.error(f"加载Gene_transcript.csv文件时出错: {str(e)}")
        raise

def update_transcript_mapping_file(transcript_to_gene):
    """更新unique_transcript_gene_mapping_with_location.csv文件"""
    try:
        logging.info("正在处理unique_transcript_gene_mapping_with_location.csv文件...")
        
        # 读取原始文件
        rows = []
        with open('unique_transcript_gene_mapping_with_location.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            fieldnames = reader.fieldnames
            
            for row in reader:
                rows.append(row)
        
        logging.info(f"原始文件加载完成，共{len(rows)}行数据")
        
        # 统计更新信息
        updated_gene_id_count = 0
        updated_gene_symbol_count = 0
        matched_transcript_count = 0
        total_rows = len(rows)
        
        # 更新数据
        for i, row in enumerate(rows):
            transcript_id = row['transcript_id']
            current_gene_id = row['GENE ID']
            current_gene_symbol = row['GENE symbol']
            
            # 查找匹配的基因信息
            if transcript_id in transcript_to_gene:
                matched_transcript_count += 1
                new_gene_id = transcript_to_gene[transcript_id]['gene_id']
                new_gene_symbol = transcript_to_gene[transcript_id]['gene_name']
                
                # 检查是否需要更新GENE ID
                if current_gene_id != new_gene_id:
                    logging.info(f"更新转录本 {transcript_id} 的GENE ID: {current_gene_id} -> {new_gene_id}")
                    row['GENE ID'] = new_gene_id
                    updated_gene_id_count += 1
                
                # 检查是否需要更新GENE symbol
                if current_gene_symbol != new_gene_symbol:
                    logging.info(f"更新转录本 {transcript_id} 的GENE symbol: {current_gene_symbol} -> {new_gene_symbol}")
                    row['GENE symbol'] = new_gene_symbol
                    updated_gene_symbol_count += 1
            
            # 每处理1000行输出一次进度
            if (i + 1) % 1000 == 0:
                logging.info(f"已处理 {i + 1}/{total_rows} 行")
        
        # 保存更新后的文件
        output_file = 'unique_transcript_gene_mapping_with_location_updated.csv'
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)
        
        logging.info(f"更新后的文件已保存到{output_file}")
        
        return {
            'total_rows': total_rows,
            'matched_transcript_count': matched_transcript_count,
            'updated_gene_id_count': updated_gene_id_count,
            'updated_gene_symbol_count': updated_gene_symbol_count
        }
        
    except Exception as e:
        logging.error(f"更新文件时出错: {str(e)}")
        raise

def main():
    """主函数"""
    logging.info("开始执行基因信息更新任务")
    logging.info(f"当前工作目录: {os.getcwd()}")
    
    try:
        # 加载基因转录本映射
        transcript_to_gene = load_gene_transcript_mapping()
        
        # 更新文件
        stats = update_transcript_mapping_file(transcript_to_gene)
        
        # 输出统计信息
        logging.info("\n=== 更新统计结果 ===")
        logging.info(f"总行数: {stats['total_rows']}")
        logging.info(f"找到匹配转录本的行数: {stats['matched_transcript_count']}")
        logging.info(f"更新GENE ID的行数: {stats['updated_gene_id_count']}")
        logging.info(f"更新GENE symbol的行数: {stats['updated_gene_symbol_count']}")
        logging.info(f"匹配率: {stats['matched_transcript_count']/stats['total_rows']*100:.2f}%")
        
        if stats['updated_gene_id_count'] > 0 or stats['updated_gene_symbol_count'] > 0:
            logging.info(f"总共有 {max(stats['updated_gene_id_count'], stats['updated_gene_symbol_count'])} 行数据被更新")
        else:
            logging.info("没有数据需要更新")
        
        logging.info("任务完成")
        
    except Exception as e:
        logging.error(f"执行过程中出错: {str(e)}")
        raise

if __name__ == "__main__":
    main() 