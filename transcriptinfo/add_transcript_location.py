#!/usr/bin/env python3
"""
为unique_transcript_ids_with_utr_original_content.csv文件添加转录本位置信息
根据transcriptId匹配transcript_locations_final_updated.csv中的location_text
"""

import pandas as pd
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('add_transcript_location.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def add_transcript_location():
    """
    主处理函数：为UTR数据文件添加转录本位置信息
    """
    logger.info("开始处理转录本位置信息添加任务...")
    
    try:
        # 1. 读取主要的UTR数据文件
        logger.info("读取unique_transcript_ids_with_utr_original_content.csv...")
        main_df = pd.read_csv('unique_transcript_ids_with_utr_original_content.csv')
        logger.info(f"读取到 {len(main_df)} 条UTR数据记录")
        logger.info(f"列数: {len(main_df.columns)}")
        
        # 2. 读取位置信息文件
        logger.info("读取transcript_locations_final_updated.csv...")
        location_df = pd.read_csv('transcript_locations_final_updated.csv')
        logger.info(f"读取到 {len(location_df)} 条位置信息记录")
        logger.info(f"位置文件列名: {list(location_df.columns)}")
        
        # 3. 检查关键列是否存在
        if 'transcriptId' not in main_df.columns:
            logger.error("主文件中没有找到 'transcriptId' 列")
            return
            
        if 'transcript_id' not in location_df.columns:
            logger.error("位置文件中没有找到 'transcript_id' 列")
            return
            
        if 'location_text' not in location_df.columns:
            logger.error("位置文件中没有找到 'location_text' 列")
            return
        
        # 4. 显示一些示例数据
        logger.info("主文件前3行transcriptId:")
        logger.info(main_df['transcriptId'].head(3).tolist())
        
        logger.info("位置文件前3行transcript_id:")
        logger.info(location_df['transcript_id'].head(3).tolist())
        
        # 5. 执行左连接，添加位置信息
        logger.info("执行数据合并...")
        
        # 重命名位置文件的列以避免冲突
        location_df_renamed = location_df[['transcript_id', 'location_text']].copy()
        location_df_renamed.columns = ['transcriptId', 'transcriptLocation']
        
        # 执行左连接
        merged_df = pd.merge(
            main_df, 
            location_df_renamed, 
            on='transcriptId', 
            how='left'
        )
        
        logger.info(f"合并后数据行数: {len(merged_df)}")
        logger.info(f"合并后数据列数: {len(merged_df.columns)}")
        
        # 6. 检查合并结果
        # 统计有位置信息的转录本数量
        has_location = merged_df['transcriptLocation'].notna()
        location_count = has_location.sum()
        no_location_count = len(merged_df) - location_count
        
        logger.info(f"有位置信息的转录本: {location_count} ({location_count/len(merged_df)*100:.1f}%)")
        logger.info(f"没有位置信息的转录本: {no_location_count} ({no_location_count/len(merged_df)*100:.1f}%)")
        
        # 7. 处理缺失值
        # 将NaN值替换为空字符串
        merged_df['transcriptLocation'] = merged_df['transcriptLocation'].fillna('')
        
        # 8. 保存结果
        output_file = 'unique_transcript_ids_with_utr_and_location.csv'
        merged_df.to_csv(output_file, index=False)
        logger.info(f"结果已保存到 {output_file}")
        
        # 9. 显示一些示例数据
        logger.info("合并结果示例:")
        sample_with_location = merged_df[merged_df['transcriptLocation'] != ''].head(3)
        for idx, row in sample_with_location.iterrows():
            logger.info(f"  {row['transcriptId']} ({row['geneSymbol']}) -> {row['transcriptLocation']}")
        
        # 10. 验证数据完整性
        logger.info("数据完整性验证:")
        logger.info(f"  原始数据行数: {len(main_df)}")
        logger.info(f"  合并后行数: {len(merged_df)}")
        logger.info(f"  数据完整性: {'✅ 通过' if len(main_df) == len(merged_df) else '❌ 失败'}")
        
        return merged_df
        
    except FileNotFoundError as e:
        logger.error(f"文件未找到: {e}")
        return None
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        return None

def main():
    """
    主函数
    """
    try:
        result_df = add_transcript_location()
        if result_df is not None:
            logger.info("转录本位置信息添加完成！")
        else:
            logger.error("处理失败")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")

if __name__ == '__main__':
    main()
