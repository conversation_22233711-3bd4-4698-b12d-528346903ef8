#!/usr/bin/env python3
"""
UTR特征过滤脚本
处理UTR_features_filtered.csv文件，根据transcriptId数量和UTR_Features值数量的匹配关系进行过滤
"""

import csv
import os
import sys
import json
from collections import Counter

def analyze_utr_features(input_file):
    """分析UTR_Features的唯一值和数量（支持CSV或JSON）"""
    print(f"开始分析文件: {input_file}")

    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件不存在 {input_file}")
        return None

    data = []
    headers = []
    utr_features_counts = Counter()

    try:
        if input_file.lower().endswith('.json'):
            # 读取并转换JSON为与CSV相同的结构
            with open(input_file, 'r', encoding='utf-8') as f:
                json_obj = json.load(f)

            # 期望的输出结构：每条记录包含 'transcriptId'（分号分隔），'geneId'（分号分隔），'UTR_Features'（组合键）
            headers = ['transcriptId', 'geneId', 'UTR_Features']

            for combination_key, entry in json_obj.items():
                transcripts = entry.get('transcript_ids', []) or []
                genes = entry.get('gene_ids', []) or []
                json_transcript_count = entry.get('transcript_count', len(transcripts))
                json_gene_count = entry.get('gene_count', len(genes))
                # 构造一行与旧CSV兼容的数据
                row = {
                    'transcriptId': ';'.join(transcripts),
                    'geneId': ';'.join(genes),
                    'UTR_Features': combination_key,
                    'json_transcript_count': json_transcript_count,
                    'json_gene_count': json_gene_count,
                    '_combination_key': combination_key,
                    '_json_entry': entry,
                }
                data.append(row)
                # 在JSON输入下，使用每个组合的transcript总数作为该UTR_Features的“计数”
                utr_features_counts[combination_key] = len(transcripts)

            print(f"成功读取JSON，共 {len(data)} 条组合")
            print(f"列名: {headers}")
        else:
            # 读取CSV文件
            with open(input_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                headers = reader.fieldnames
                for row in reader:
                    data.append(row)

            print(f"成功读取CSV，共 {len(data)} 行")
            print(f"列名: {headers}")

            # 检查必需的列
            required_columns = ['transcriptId', 'geneId', 'UTR_Features']
            missing_columns = [col for col in required_columns if col not in headers]
            if missing_columns:
                print(f"错误: 缺少必需的列: {missing_columns}")
                return None

            # 分析UTR_Features的唯一值（CSV场景，按出现次数计数）
            for row in data:
                utr_features_counts[row['UTR_Features']] += 1

        # 输出计数信息
        print("\n分析UTR_Features的唯一值...")
        print(f"UTR_Features唯一值数量: {len(utr_features_counts)}")
        print("\nUTR_Features值及其出现次数:")
        for feature, count in sorted(utr_features_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"  '{feature}': {count} 次")

    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

    return data, headers, utr_features_counts

def filter_data(data, headers, utr_features_counts, output_file):
    """根据条件过滤数据"""
    print(f"\n开始过滤数据...")

    filtered_rows = []
    filtered_json = {}
    filter_stats = {
        'total': len(data),
        'filtered_mismatch': 0,            # 行内transcript数 vs 组合全局计数（CSV时为出现次数；JSON时为ID列表长度）
        'filtered_json_mismatch': 0,       # 行内transcript数 vs JSON自带的transcript_count
        'kept': 0
    }

    # 12个UTR特征列名称（用于重建features字典）
    utr_feature_columns = [
        'threeUtrMiRnas', 'threeUtrPolyaSites', 'threeUtrRfamMotifs',
        'threeUtrRepeats', 'threeUtrUorfs', 'threeUtrIres',
        'fiveUtrMiRnas', 'fiveUtrPolyaSites', 'fiveUtrRfamMotifs',
        'fiveUtrRepeats', 'fiveUtrUorfs', 'fiveUtrIres'
    ]

    for index, row in enumerate(data):
        # 计算transcriptId的数量
        transcript_ids = [tid for tid in row.get('transcriptId', '').split(';') if tid]
        transcript_count = len(transcript_ids)

        # 获取当前行的UTR_Features值
        utr_features = row['UTR_Features']

        # 获取这个UTR_Features值在整个数据集中出现的次数
        utr_feature_count = utr_features_counts[utr_features]

        # JSON自带的transcript_count（若无则回退为行内计数）
        json_transcript_count = int(row.get('json_transcript_count', transcript_count))

        # 一致性校验 1：行内transcript数量 vs JSON的transcript_count
        json_count_mismatch = (transcript_count != json_transcript_count)
        if json_count_mismatch:
            filter_stats['filtered_json_mismatch'] += 1

        # 一致性校验 2：行内transcript数量 vs 全局计数
        global_count_mismatch = (transcript_count != utr_feature_count)
        if global_count_mismatch:
            filter_stats['filtered_mismatch'] += 1

        # 若任一不一致则过滤
        if json_count_mismatch or global_count_mismatch:
            continue

        # 保留这一行
        filtered_rows.append(row)
        filter_stats['kept'] += 1

        # 构建输出JSON的该组合数据
        combination_key = row['UTR_Features']
        # 优先使用输入JSON的原始entry（避免巨大列表再拆分/拼接误差）
        entry = row.get('_json_entry')
        if entry is None:
            # 非JSON输入或缺少原始entry时，根据行数据重建
            values = combination_key.split('|')
            features = {col: (values[i] if i < len(values) else 'no') for i, col in enumerate(utr_feature_columns)}
            gene_ids = [gid for gid in row.get('geneId', '').split(';') if gid]
            filtered_json[combination_key] = {
                'features': features,
                'transcript_count': transcript_count,
                'transcript_ids': transcript_ids,
                'gene_count': len(gene_ids),
                'gene_ids': gene_ids,
            }
        else:
            # 直接使用原始结构，确保与输入一致
            filtered_json[combination_key] = {
                'features': entry.get('features', {}),
                'transcript_count': entry.get('transcript_count', transcript_count),
                'transcript_ids': entry.get('transcript_ids', transcript_ids),
                'gene_count': entry.get('gene_count', len([gid for gid in row.get('geneId', '').split(';') if gid])),
                'gene_ids': entry.get('gene_ids', [gid for gid in row.get('geneId', '').split(';') if gid]),
            }

        # 每处理1000行显示进度
        if (index + 1) % 1000 == 0:
            print(f"已处理 {index + 1} 行...")

    # 保存过滤后的数据（JSON）
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_json, f, ensure_ascii=False, indent=2)
        print(f"\n过滤后的数据已保存到(JSON): {output_file}")
    except Exception as e:
        print(f"保存文件时出错: {e}")
        return None

    # 显示过滤统计
    print(f"\n过滤统计:")
    print(f"  总行数: {filter_stats['total']}")
    print(f"  因与全局计数不匹配被过滤: {filter_stats['filtered_mismatch']}")
    print(f"  因与JSON计数不匹配被过滤: {filter_stats['filtered_json_mismatch']}")
    print(f"  保留的行数: {filter_stats['kept']}")
    print(f"  过滤率: {((filter_stats['total'] - filter_stats['kept']) / filter_stats['total'] * 100):.2f}%")

    return filtered_rows, filter_stats

def analyze_filtered_data(filtered_data):
    """分析过滤后的数据"""
    if len(filtered_data) == 0:
        print("\n过滤后没有数据保留")
        return

    print(f"\n分析过滤后的数据...")

    # 分析transcriptId数量分布
    transcript_counts = []
    for row in filtered_data:
        transcript_ids = row['transcriptId'].split(';')
        transcript_counts.append(len(transcript_ids))

    transcript_count_dist = Counter(transcript_counts)
    print(f"\n过滤后transcriptId数量分布:")
    for count, freq in sorted(transcript_count_dist.items()):
        print(f"  {count}个transcript: {freq} 行")

    print(f"\ntranscriptId数量统计:")
    print(f"  最小值: {min(transcript_counts)}")
    print(f"  最大值: {max(transcript_counts)}")
    print(f"  平均值: {sum(transcript_counts)/len(transcript_counts):.2f}")

    # 分析过滤后的UTR_Features分布
    utr_features_counts = Counter()
    for row in filtered_data:
        utr_features_counts[row['UTR_Features']] += 1

    print(f"\n过滤后UTR_Features分布 (前10个):")
    for feature, count in sorted(utr_features_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  '{feature}': {count} 次")

def main():
    """主函数"""
    input_file = "transcriptinfo/transcriptInfo_utr_combinations_rows.json"
    output_file = "transcriptinfo/UTR_features_filtered_processed.json"
    
    print("UTR特征过滤工具")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print(f"输出文件(JSON): {output_file}")
    
    # 分析原始数据
    result = analyze_utr_features(input_file)
    if result is None:
        print("分析失败，程序退出")
        sys.exit(1)

    data, headers, utr_features_counts = result

    # 过滤数据
    filter_result = filter_data(data, headers, utr_features_counts, output_file)
    if filter_result is None:
        print("过滤失败，程序退出")
        sys.exit(1)

    filtered_data, filter_stats = filter_result

    # 分析过滤后的数据
    analyze_filtered_data(filtered_data)
    
    print("\n" + "=" * 50)
    print("处理完成！")
    print(f"原始数据: {filter_stats['total']} 行")
    print(f"过滤后数据: {filter_stats['kept']} 行")
    print(f"输出文件(JSON): {output_file}")

if __name__ == "__main__":
    main()
