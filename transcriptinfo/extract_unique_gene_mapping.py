#!/usr/bin/env python3
"""
提取唯一基因映射

从unique_transcript_gene_mapping_with_location_updated.csv文件中
提取唯一的GENE ID和对应的GENE symbol，生成两列的CSV文件。
"""

import csv
import logging
from collections import OrderedDict

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extract_unique_gene_mapping.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def extract_unique_gene_mapping():
    """提取唯一的基因ID和基因符号映射"""
    try:
        # 使用OrderedDict保持插入顺序，存储唯一的基因ID和符号映射
        unique_gene_mapping = OrderedDict()
        
        total_rows = 0
        processed_rows = 0
        
        logging.info("正在读取unique_transcript_gene_mapping_with_location_updated.csv文件...")
        
        with open('unique_transcript_gene_mapping_with_location_updated.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                total_rows += 1
                gene_id = row['GENE ID']
                gene_symbol = row['GENE symbol']
                
                # 确保GENE ID和GENE symbol都不为空
                if gene_id and gene_symbol and gene_id.strip() and gene_symbol.strip():
                    gene_id = gene_id.strip()
                    gene_symbol = gene_symbol.strip()
                    
                    # 如果这个GENE ID还没有记录，则添加
                    if gene_id not in unique_gene_mapping:
                        unique_gene_mapping[gene_id] = gene_symbol
                        processed_rows += 1
                    else:
                        # 检查是否存在不一致的映射（理论上不应该存在）
                        if unique_gene_mapping[gene_id] != gene_symbol:
                            logging.warning(f"发现不一致的映射: {gene_id} -> {unique_gene_mapping[gene_id]} vs {gene_symbol}")
                
                # 每处理10000行输出一次进度
                if total_rows % 10000 == 0:
                    logging.info(f"已处理 {total_rows} 行，提取了 {len(unique_gene_mapping)} 个唯一基因映射")
        
        logging.info(f"文件读取完成，总共处理了 {total_rows} 行")
        logging.info(f"提取了 {len(unique_gene_mapping)} 个唯一的GENE ID-GENE symbol映射")
        
        # 保存唯一的基因映射到新文件
        output_file = 'unique_gene_id_symbol_mapping_final.csv'
        
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['GENE_ID', 'GENE_symbol'])
            
            for gene_id, gene_symbol in unique_gene_mapping.items():
                writer.writerow([gene_id, gene_symbol])
        
        logging.info(f"唯一基因映射已保存到 {output_file}")
        
        # 验证结果
        logging.info("\n=== 验证结果 ===")
        with open(output_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            verification_count = sum(1 for row in reader)
        
        logging.info(f"输出文件中的记录数: {verification_count}")
        logging.info(f"预期记录数: {len(unique_gene_mapping)}")
        
        if verification_count == len(unique_gene_mapping):
            logging.info("✓ 验证通过：输出文件记录数与预期一致")
        else:
            logging.warning("⚠ 验证失败：输出文件记录数与预期不一致")
        
        return {
            'total_rows': total_rows,
            'unique_mappings': len(unique_gene_mapping),
            'output_file': output_file
        }
        
    except Exception as e:
        logging.error(f"提取唯一基因映射时出错: {str(e)}")
        raise

def analyze_gene_mapping():
    """分析基因映射的统计信息"""
    try:
        logging.info("\n=== 分析基因映射统计信息 ===")
        
        # 读取输出文件进行分析
        gene_ids = []
        gene_symbols = []
        
        with open('unique_gene_id_symbol_mapping_final.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                gene_ids.append(row['GENE_ID'])
                gene_symbols.append(row['GENE_symbol'])
        
        unique_gene_ids = set(gene_ids)
        unique_gene_symbols = set(gene_symbols)
        
        logging.info(f"唯一GENE ID数量: {len(unique_gene_ids)}")
        logging.info(f"唯一GENE symbol数量: {len(unique_gene_symbols)}")
        
        # 检查是否有重复的GENE ID（理论上不应该有）
        if len(gene_ids) != len(unique_gene_ids):
            logging.warning(f"⚠ 发现重复的GENE ID: 总数 {len(gene_ids)}, 唯一数 {len(unique_gene_ids)}")
        else:
            logging.info("✓ 所有GENE ID都是唯一的")
        
        # 检查GENE symbol的重复情况
        symbol_duplicates = len(gene_symbols) - len(unique_gene_symbols)
        if symbol_duplicates > 0:
            logging.info(f"有 {symbol_duplicates} 个GENE symbol被多个GENE ID共享（这是正常的生物学现象）")
        else:
            logging.info("所有GENE symbol都是唯一的")
        
    except Exception as e:
        logging.error(f"分析基因映射统计信息时出错: {str(e)}")

def main():
    """主函数"""
    logging.info("开始提取唯一基因映射")
    
    try:
        # 提取唯一基因映射
        stats = extract_unique_gene_mapping()
        
        # 分析统计信息
        analyze_gene_mapping()
        
        logging.info("\n=== 任务完成 ===")
        logging.info(f"从 {stats['total_rows']} 行数据中提取了 {stats['unique_mappings']} 个唯一的基因映射")
        logging.info(f"结果已保存到 {stats['output_file']}")
        
    except Exception as e:
        logging.error(f"执行过程中出错: {str(e)}")
        raise

if __name__ == "__main__":
    main() 