#!/usr/bin/env python3
"""
添加基因详细信息到映射文件

根据unique_gene_id_symbol_mapping_final.csv文件的GENE_ID列查找
ensembl_gene_id_with_full_info_split_2.csv的ensembl_gene_id列，
将"Approved Name,Locus Type,Chromosome,Chromosome_URL,transcript_count,transcripts"
这些列复制到第一个文件。
"""

import csv
import logging
from datetime import datetime
import os

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('add_gene_details_to_mapping.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def load_gene_details():
    """加载基因详细信息"""
    try:
        logging.info("正在加载ensembl_gene_id_with_full_info_split_2.csv...")
        gene_details = {}
        
        with open('../ensembl_gene_id_with_full_info_split_2.csv', 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                ensembl_gene_id = row.get('ensembl_gene_id', '').strip()
                
                if ensembl_gene_id:
                    gene_details[ensembl_gene_id] = {
                        'Approved Name': row.get('Approved Name', ''),
                        'Locus Type': row.get('Locus Type', ''),
                        'Chromosome': row.get('Chromosome', ''),
                        'Chromosome_URL': row.get('Chromosome_URL', ''),
                        'transcript_count': row.get('transcript_count', ''),
                        'transcripts': row.get('transcripts', '')
                    }
        
        logging.info(f"成功加载{len(gene_details)}个基因的详细信息")
        return gene_details
        
    except Exception as e:
        logging.error(f"加载基因详细信息时出错: {str(e)}")
        return {}

def add_details_to_mapping(gene_details):
    """向映射文件添加详细信息"""
    try:
        input_file = 'unique_gene_id_symbol_mapping_final.csv'
        output_file = 'unique_gene_id_symbol_mapping_with_details.csv'
        
        logging.info(f"正在处理{input_file}...")
        
        total_rows = 0
        matched_count = 0
        not_found_count = 0
        
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8', newline='') as outfile:
            
            reader = csv.DictReader(infile)
            
            # 定义新的字段名
            new_fieldnames = reader.fieldnames + [
                'Approved Name', 'Locus Type', 'Chromosome', 
                'Chromosome_URL', 'transcript_count', 'transcripts'
            ]
            
            writer = csv.DictWriter(outfile, fieldnames=new_fieldnames)
            writer.writeheader()
            
            for row in reader:
                total_rows += 1
                
                gene_id = row.get('GENE_ID', '').strip()
                
                # 在基因详细信息中查找匹配
                if gene_id in gene_details:
                    details = gene_details[gene_id]
                    
                    # 添加详细信息到行中
                    row['Approved Name'] = details['Approved Name']
                    row['Locus Type'] = details['Locus Type']
                    row['Chromosome'] = details['Chromosome']
                    row['Chromosome_URL'] = details['Chromosome_URL']
                    row['transcript_count'] = details['transcript_count']
                    row['transcripts'] = details['transcripts']
                    
                    matched_count += 1
                else:
                    # 如果未找到匹配，填充空值
                    row['Approved Name'] = ''
                    row['Locus Type'] = ''
                    row['Chromosome'] = ''
                    row['Chromosome_URL'] = ''
                    row['transcript_count'] = ''
                    row['transcripts'] = ''
                    
                    not_found_count += 1
                    if not_found_count <= 10:  # 只记录前10个未找到的
                        logging.warning(f"未找到GENE_ID: {gene_id}")
                
                writer.writerow(row)
                
                # 每处理1000行显示进度
                if total_rows % 1000 == 0:
                    logging.info(f"已处理 {total_rows} 行...")
        
        # 统计结果
        logging.info("=" * 50)
        logging.info("添加详细信息完成！统计结果：")
        logging.info(f"总处理行数: {total_rows}")
        logging.info(f"成功匹配的行数: {matched_count}")
        logging.info(f"未找到匹配的行数: {not_found_count}")
        logging.info(f"匹配成功率: {(matched_count / total_rows * 100):.2f}%")
        logging.info(f"输出文件: {output_file}")
        logging.info("=" * 50)
        
        return True
        
    except Exception as e:
        logging.error(f"添加详细信息时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logging.info("开始添加基因详细信息到映射文件...")
    start_time = datetime.now()
    
    # 加载基因详细信息
    gene_details = load_gene_details()
    
    if not gene_details:
        logging.error("无法加载基因详细信息，程序退出")
        return
    
    # 添加详细信息到映射文件
    success = add_details_to_mapping(gene_details)
    
    end_time = datetime.now()
    duration = end_time - start_time
    
    if success:
        logging.info(f"程序执行成功！总耗时: {duration}")
    else:
        logging.error(f"程序执行失败！总耗时: {duration}")

if __name__ == "__main__":
    main() 