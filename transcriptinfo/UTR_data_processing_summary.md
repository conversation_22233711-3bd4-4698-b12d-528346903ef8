# UTR数据处理总结报告

## 任务概述
将UTR信息添加到 `unique_transcript_ids.csv` 文件中，根据 `merged_transcriptID_utr_table_data_3_with_json_data.csv` 文件中的数据，按照TranscriptID和UTR_Type进行匹配。

## 处理过程

### 1. 输入文件
- **主文件**: `unique_transcript_ids.csv` (154,622条转录本记录)
- **UTR数据文件**: `merged_transcriptID_utr_table_data_3_with_json_data.csv` (181,358条UTR记录)
  - 3UTR记录: 87,916条
  - 5UTR记录: 93,442条

### 2. 添加的字段
根据要求添加了以下18个新字段：

#### 3'UTR相关字段 (9个)
1. `threeUtrEntryName` - 3UTR条目名称
2. `threeUtrLength` - 3UTR长度
3. `threeUtrUrl` - 3UTR数据库链接
4. `threeUtrMiRnas` - 3UTR中的miRNA信息
5. `threeUtrPolyaSites` - 3UTR中的PolyA位点信息
6. `threeUtrRfamMotifs` - 3UTR中的Rfam基序信息
7. `threeUtrRepeats` - 3UTR中的重复序列信息
8. `threeUtrUorfs` - 3UTR中的uORF信息
9. `threeUtrIres` - 3UTR中的IRES信息

#### 5'UTR相关字段 (9个)
1. `fiveUtrEntryName` - 5UTR条目名称
2. `fiveUtrLength` - 5UTR长度
3. `fiveUtrUrl` - 5UTR数据库链接
4. `fiveUtrMiRnas` - 5UTR中的miRNA信息
5. `fiveUtrPolyaSites` - 5UTR中的PolyA位点信息
6. `fiveUtrRfamMotifs` - 5UTR中的Rfam基序信息
7. `fiveUtrRepeats` - 5UTR中的重复序列信息
8. `fiveUtrUorfs` - 5UTR中的uORF信息
9. `fiveUtrIres` - 5UTR中的IRES信息

### 3. 数据映射逻辑
- 根据 `TranscriptID` 字段进行匹配
- 根据 `UTR_Type` 字段区分3UTR和5UTR数据
- 基本信息（EntryName, Length, Url）直接从对应列获取
- 功能注释信息（MiRnas, PolyaSites等）从 `UtrComp` JSON字段解析获取

### 4. 处理结果统计

#### 总体统计
- **总转录本数**: 154,622
- **处理成功率**: 100%

#### UTR数据分布
- **只有3'UTR信息**: 11,527 (7.5%)
- **只有5'UTR信息**: 17,382 (11.2%)
- **同时有3'UTR和5'UTR信息**: 68,495 (44.3%)
- **没有任何UTR信息**: 57,218 (37.0%)

#### 覆盖率分析
- **有3'UTR信息的转录本**: 80,022 (51.8%)
- **有5'UTR信息的转录本**: 85,877 (55.5%)
- **至少有一种UTR信息**: 97,404 (63.0%)

## 输出文件

### 主要输出
- **`unique_transcript_ids_with_utr_final.csv`** - 最终处理结果文件
  - 包含原始的3列 + 新增的18列UTR信息
  - 总计21列
  - 154,622行数据

### 辅助文件
- **`process_utr_data_optimized.py`** - 优化版处理脚本
- **`process_utr_data_optimized.log`** - 处理日志文件
- **`unique_transcript_ids_with_utr_optimized.csv`** - 中间处理文件

## 数据质量说明

### 1. 数据完整性
- 所有原始转录本记录都保留
- 新增字段对于没有对应UTR信息的转录本填充为空字符串
- 无数据丢失

### 2. 数据准确性
- UTR信息严格按照TranscriptID匹配
- JSON数据解析准确提取各项功能注释
- 3'UTR和5'UTR信息正确分类

### 3. 数据格式
- 长度信息保持数值格式
- URL信息保持完整链接格式
- 功能注释信息标准化为"yes"/"no"格式

## 使用建议

### 1. 数据筛选
```python
# 筛选有完整UTR信息的转录本
complete_utr = df[(df['threeUtrEntryName'] != '') & (df['fiveUtrEntryName'] != '')]

# 筛选有特定功能注释的转录本
has_mirnas = df[df['threeUtrMiRnas'] == 'yes']
```

### 2. 数据分析
- 可以基于UTR长度进行分析
- 可以基于功能注释进行功能富集分析
- 可以结合基因表达数据进行综合分析

## 技术细节

### 处理性能
- 使用pandas merge操作优化处理速度
- 总处理时间约8秒
- 内存使用效率高

### 错误处理
- JSON解析错误自动跳过并记录
- 缺失数据用空字符串填充
- 完整的日志记录便于问题追踪

## 结论
UTR数据处理任务已成功完成，所有要求的字段都已正确添加到原始文件中。数据质量良好，覆盖率达到预期，可以用于后续的生物信息学分析。
