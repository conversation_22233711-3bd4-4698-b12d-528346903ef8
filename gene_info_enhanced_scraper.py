import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
from tqdm import tqdm

def fetch_gene_info(gene_id):
    """
    Fetch gene information from NCBI Gene database
    
    Args:
        gene_id: The gene ID or transcript ID (e.g., ENST00000673477)
        
    Returns:
        Dictionary containing the gene information
    """
    url = f"https://www.ncbi.nlm.nih.gov/gene/?term={gene_id}"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Initialize the results dictionary
            gene_info = {}
            gene_info['gene_id'] = gene_id
            
            # Extract information from the summary section
            summary_div = soup.find('div', class_='rprt-section gene-summary')
            if summary_div:
                dl = summary_div.find('dl', id='summaryDl')
                if dl:
                    # Extract all the requested fields
                    fields = [
                        "Official Symbol", 
                        "Official Full Name", 
                        "Primary source", 
                        "See related", 
                        "Gene type", 
                        "RefSeq status", 
                        "Organism", 
                        "Lineage", 
                        "Also known as", 
                        "Summary"
                    ]
                    
                    for field in fields:
                        dt = dl.find('dt', string=lambda text: text and field in text)
                        if dt:
                            dd = dt.find_next_sibling('dd')
                            if dd:
                                # Handle special case for fields with links
                                if field == "See related":
                                    # Get all links
                                    links = dd.find_all('a')
                                    link_texts = [link.get_text(strip=True) for link in links]
                                    gene_info[field] = "; ".join(link_texts)
                                elif field == "Primary source":
                                    # Get the link text
                                    link = dd.find('a')
                                    if link:
                                        gene_info[field] = link.get_text(strip=True)
                                    else:
                                        gene_info[field] = dd.get_text(strip=True)
                                elif field in ["Official Symbol", "Official Full Name"]:
                                    # Remove "provided by" text
                                    text = dd.get_text(strip=True)
                                    if "provided by" in text:
                                        gene_info[field] = text.split("provided by")[0].strip()
                                    else:
                                        gene_info[field] = text
                                else:
                                    gene_info[field] = dd.get_text(strip=True)
            
            # Extract chromosome location
            chr_info = soup.find('dl', class_='dl-chr-info')
            if chr_info:
                location_dt = chr_info.find('dt', string=lambda text: text and "Location" in text)
                if location_dt:
                    location_dd = location_dt.find_next_sibling('dd')
                    if location_dd:
                        gene_info["Location"] = location_dd.get_text(strip=True)
            
            return gene_info
        else:
            print(f"Error fetching gene information: Status code {response.status_code}")
            return None
    except Exception as e:
        print(f"Exception when fetching gene information: {str(e)}")
        return None

def process_gene_list(gene_ids, output_file):
    """
    Process a list of gene IDs and save the results to a CSV file
    
    Args:
        gene_ids: List of gene IDs to process
        output_file: Path to the output CSV file
    """
    results = []
    
    for gene_id in tqdm(gene_ids, desc="Fetching gene information"):
        gene_info = fetch_gene_info(gene_id)
        if gene_info:
            results.append(gene_info)
        
        # Add a small delay to avoid hitting rate limits
        time.sleep(1)
    
    # Convert to DataFrame and save to CSV
    if results:
        df = pd.DataFrame(results)
        df.to_csv(output_file, index=False)
        print(f"Data saved to {output_file}")
    else:
        print("No results to save.")

def process_gene_file(input_file, output_file, id_column):
    """
    Process a CSV file containing gene IDs
    
    Args:
        input_file: Path to the input CSV file
        output_file: Path to the output CSV file
        id_column: Name of the column containing gene IDs
    """
    df = pd.read_csv(input_file)
    gene_ids = df[id_column].tolist()
    
    # Remove any NaN values
    gene_ids = [str(gene_id) for gene_id in gene_ids if str(gene_id) != 'nan']
    
    process_gene_list(gene_ids, output_file)

if __name__ == "__main__":
    # Example usage
    gene_info = fetch_gene_info("ENST00000673477")
    print(gene_info)
    
    # To process a list of gene IDs and save to CSV:
    # gene_ids = ["ENST00000673477", "ENSG00000160072"]
    # process_gene_list(gene_ids, "gene_info.csv")
    
    # To process a CSV file:
    # process_gene_file("input.csv", "gene_info_output.csv", "gene_id_column") 