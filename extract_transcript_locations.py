#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理 transcriptLength/transcript_locations_final_1.csv 文件
通过访问 source_url 获取 location_text
"""

import csv
import requests
import re
import time
import logging
from datetime import datetime
from bs4 import BeautifulSoup
import random

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extract_transcript_locations.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

INPUT_FILE = 'transcriptLength/transcript_locations_final_1.csv'
OUTPUT_FILE = 'transcriptLength/transcript_locations_final_1_updated.csv'

# 请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

def extract_location_from_html(html_content, base_url):
    """从HTML内容中提取位置信息和URL"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找包含Location的行
        location_divs = soup.find_all('div', class_='lhs')
        for div in location_divs:
            if div.get_text().strip() == 'Location':
                # 找到对应的rhs div
                rhs_div = div.find_next_sibling('div', class_='rhs')
                if rhs_div:
                    # 提取位置文本和链接
                    location_link = rhs_div.find('a', class_='constant dynamic-link')
                    if location_link:
                        location_text = location_link.get_text().strip()
                        # 移除" forward strand."或" reverse strand."后缀
                        location_text = re.sub(r'\s+(forward|reverse)\s+strand\.$', '', location_text)

                        # 获取链接URL
                        location_url = location_link.get('href', '')
                        if location_url and location_url.startswith('/'):
                            # 转换为绝对URL
                            from urllib.parse import urljoin
                            location_url = urljoin(base_url, location_url)

                        return location_text, location_url
                    else:
                        # 如果没有链接，尝试提取纯文本
                        text = rhs_div.get_text().strip()
                        # 使用正则表达式提取染色体位置信息
                        match = re.search(r'Chromosome\s+[^:]+:\s*[\d,]+-[\d,]+', text)
                        if match:
                            return match.group(0), ""

        return None, None

    except Exception as e:
        logging.error(f"解析HTML时出错: {e}")
        return None, None

def get_location_from_url(url, transcript_id):
    """从URL获取位置信息和URL"""
    try:
        # 添加随机延迟，避免请求过于频繁
        time.sleep(random.uniform(0.5, 1.5))

        response = requests.get(url, headers=HEADERS, timeout=30)
        response.raise_for_status()

        location_text, location_url = extract_location_from_html(response.text, url)

        if location_text:
            logging.info(f"成功获取 {transcript_id} 的位置信息: {location_text}")
            return location_text, location_url, "success"
        else:
            logging.warning(f"未能从 {transcript_id} 的页面中提取位置信息")
            return None, None, "no_location_found"

    except requests.exceptions.RequestException as e:
        logging.error(f"请求 {transcript_id} 时出错: {e}")
        return None, None, "request_failed"
    except Exception as e:
        logging.error(f"处理 {transcript_id} 时出错: {e}")
        return None, None, "processing_error"

def process_transcript_locations():
    """处理转录本位置信息"""
    try:
        logging.info(f'开始处理文件: {INPUT_FILE}')
        
        # 读取CSV文件
        rows = []
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        
        logging.info(f'文件读取完成，共 {len(rows)} 条记录')
        
        # 统计信息
        processed_count = 0
        success_count = 0
        failed_count = 0
        
        # 处理每条记录
        for i, row in enumerate(rows):
            transcript_id = row['transcript_id']
            source_url = row['source_url']
            current_status = row['status']
            
            # 如果已经有位置信息且状态为success，跳过
            if row['location_text'] and current_status == 'success':
                logging.info(f"跳过已处理的记录: {transcript_id}")
                success_count += 1
                continue
            
            logging.info(f"处理第 {i+1}/{len(rows)} 条记录: {transcript_id}")
            
            # 获取位置信息
            location_text, location_url, status = get_location_from_url(source_url, transcript_id)

            # 更新记录
            if location_text:
                row['location_text'] = location_text
                row['location_url'] = location_url or ""
                row['status'] = status
                success_count += 1
            else:
                row['location_text'] = ""
                row['location_url'] = ""
                row['status'] = status
                failed_count += 1
            
            row['processed_at'] = datetime.now().isoformat()
            row['attempts'] = str(int(row.get('attempts', 0)) + 1)
            
            processed_count += 1
            
            # 每处理10条记录显示一次进度
            if processed_count % 10 == 0:
                logging.info(f"已处理 {processed_count} 条记录，成功: {success_count}, 失败: {failed_count}")
            
            # 每处理50条记录保存一次，防止数据丢失
            if processed_count % 50 == 0:
                save_results(rows)
                logging.info(f"中间保存完成，已处理 {processed_count} 条记录")
        
        # 保存最终结果
        save_results(rows)
        
        logging.info(f'处理完成！总计: {len(rows)}, 成功: {success_count}, 失败: {failed_count}')
        
        return True
        
    except Exception as e:
        logging.error(f'处理过程中发生错误: {str(e)}')
        return False

def save_results(rows):
    """保存结果到文件"""
    with open(OUTPUT_FILE, 'w', encoding='utf-8', newline='') as f:
        if rows:
            fieldnames = rows[0].keys()
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(rows)

def main():
    """主函数"""
    logging.info('开始提取转录本位置信息...')
    success = process_transcript_locations()
    
    if success:
        logging.info('转录本位置信息提取任务完成！')
    else:
        logging.error('转录本位置信息提取任务失败！')

if __name__ == '__main__':
    main()
