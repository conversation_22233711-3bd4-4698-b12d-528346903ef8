import pandas as pd
import mysql.connector
import re

# 数据库配置信息
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def clean_text_field(text):
    """清理文本字段，去除换行符和多余空格"""
    if pd.isna(text) or text == '':
        return text
    # 将换行符替换为空格，并清理多余空格
    cleaned = re.sub(r'\s+', ' ', str(text).replace('\n', ' ').replace('\r', ' '))
    return cleaned.strip()

def create_project_table(cursor):
    """创建项目信息表（使用驼峰命名法）"""
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS projectInfo (
        projectId VARCHAR(100),
        bioProjectId VARCHAR(100),
        geoAccession VARCHAR(100),
        title TEXT,
        strategy VARCHAR(100),
        tissueOrCellType VARCHAR(200),
        cellLine VARCHAR(100),
        `condition` VARCHAR(200),
        diseaseCategory VARCHAR(200),
        runNumber INT,
        pmid VARCHAR(50),
        detail LONGTEXT,
        releaseDate VARCHAR(100),
        submissionDate VARCHAR(100),
        updateDate VARCHAR(100)
    )
    """
    cursor.execute(create_table_sql)
    print("项目信息表创建成功！")

def create_indexes(cursor):
    """为每一列创建普通索引"""
    indexes = [
        ('idx_projectId', 'projectId'),
        ('idx_bioProjectId', 'bioProjectId'),
        ('idx_geoAccession', 'geoAccession'),
        ('idx_title', 'title(255)'),  # TEXT字段索引需指定长度
        ('idx_strategy', 'strategy'),
        ('idx_tissueOrCellType', 'tissueOrCellType'),
        ('idx_cellLine', 'cellLine'),
        ('idx_condition', '`condition`'),
        ('idx_diseaseCategory', 'diseaseCategory'),
        ('idx_runNumber', 'runNumber'),
        ('idx_pmid', 'pmid'),
        ('idx_detail', 'detail(255)'),  # LONGTEXT字段索引需指定长度
        ('idx_releaseDate', 'releaseDate'),
        ('idx_submissionDate', 'submissionDate'),
        ('idx_updateDate', 'updateDate')
    ]
    
    for index_name, column_spec in indexes:
        try:
            cursor.execute(f"CREATE INDEX {index_name} ON projectInfo ({column_spec})")
            print(f"索引 {index_name} 创建成功！")
        except mysql.connector.Error as e:
            if "Duplicate key name" in str(e):
                print(f"索引 {index_name} 已存在，跳过创建")
            else:
                print(f"创建索引 {index_name} 时出错: {e}")

def import_data_to_mysql(csv_file_path):
    """将CSV数据导入MySQL数据库"""
    try:
        # 连接数据库
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 创建表
        create_project_table(cursor)
        
        # 创建索引
        create_indexes(cursor)
        
        # 读取CSV文件
        print("正在读取CSV文件...")
        df = pd.read_csv(csv_file_path, encoding='utf-8')
        print(f"读取到 {len(df)} 条记录")
        
        # 清理Detail字段中的换行符
        print("正在清理数据...")
        df['Detail'] = df['Detail'].apply(clean_text_field)
        df['Title'] = df['Title'].apply(clean_text_field)
        
        # 清理所有文本字段的空格
        text_columns = ['Project ID', 'BioProject ID', 'GEO_Accession', 'Title', 'Strategy', 
                       'Tissue/Cell Type', 'Cell line', 'Condition', 'Disease Category', 
                       'PMID', 'Detail', 'Release Date', 'Submission Date', 'Update Date']
        
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].apply(lambda x: clean_text_field(x) if pd.notna(x) else x)
        
        # 准备插入SQL语句
        insert_sql = """
        INSERT INTO projectInfo (
            projectId, bioProjectId, geoAccession, title, strategy,
            tissueOrCellType, cellLine, `condition`, diseaseCategory,
            runNumber, pmid, detail, releaseDate, submissionDate, updateDate
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        # 插入数据
        success_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            try:
                data = (
                    row['Project ID'],
                    row['BioProject ID'], 
                    row['GEO_Accession'],
                    row['Title'],
                    row['Strategy'],
                    row['Tissue/Cell Type'],
                    row['Cell line'],
                    row['Condition'],
                    row['Disease Category'],
                    int(row['Run Number']) if pd.notna(row['Run Number']) else None,
                    str(row['PMID']) if pd.notna(row['PMID']) else None,
                    row['Detail'],
                    row['Release Date'],
                    row['Submission Date'],
                    row['Update Date']
                )
                cursor.execute(insert_sql, data)
                success_count += 1
                
                if success_count % 100 == 0:
                    print(f"已处理 {success_count} 条记录...")
                    
            except Exception as e:
                error_count += 1
                print(f"插入第 {index+1} 行数据时出错: {e}")
                continue
        
        # 提交事务
        conn.commit()
        print(f"\n数据导入完成！")
        print(f"成功导入: {success_count} 条记录")
        print(f"失败记录: {error_count} 条")
        
        # 验证导入结果
        cursor.execute("SELECT COUNT(*) FROM projectInfo")
        total_records = cursor.fetchone()[0]
        print(f"数据库中总记录数: {total_records}")
        
    except Exception as e:
        print(f"导入过程中发生错误: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    csv_file_path = "Browse-Project/Project.csv"
    import_data_to_mysql(csv_file_path) 