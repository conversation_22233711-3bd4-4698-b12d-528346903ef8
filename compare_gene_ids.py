#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

def compare_gene_ids(csv_file, txt_file):
    """比较两个文件中的基因ID，找出差异"""
    
    # 读取CSV文件（跳过标题行）
    print(f"读取CSV文件: {csv_file}")
    csv_df = pd.read_csv(csv_file)
    csv_gene_ids = set(csv_df['ensembl_gene_id'].astype(str))
    print(f"CSV文件中的基因ID数量: {len(csv_gene_ids)}")
    
    # 读取TXT文件
    print(f"读取TXT文件: {txt_file}")
    with open(txt_file, 'r') as f:
        txt_gene_ids = set(line.strip() for line in f if line.strip())
    print(f"TXT文件中的基因ID数量: {len(txt_gene_ids)}")
    
    # 找出在CSV中但不在TXT中的基因ID
    csv_only = csv_gene_ids - txt_gene_ids
    print(f"\n在CSV中但不在TXT中的基因ID数量: {len(csv_only)}")
    
    if csv_only:
        print("在CSV中但不在TXT中的基因ID:")
        for gene_id in sorted(csv_only):
            print(f"  {gene_id}")
    else:
        print("没有发现在CSV中但不在TXT中的基因ID")
    
    # 找出在TXT中但不在CSV中的基因ID
    txt_only = txt_gene_ids - csv_gene_ids
    print(f"\n在TXT中但不在CSV中的基因ID数量: {len(txt_only)}")
    
    if txt_only:
        print("在TXT中但不在CSV中的基因ID:")
        for gene_id in sorted(txt_only):
            print(f"  {gene_id}")
    else:
        print("没有发现在TXT中但不在CSV中的基因ID")
    
    # 统计信息
    print(f"\n统计信息:")
    print(f"  CSV文件中的基因ID数量: {len(csv_gene_ids)}")
    print(f"  TXT文件中的基因ID数量: {len(txt_gene_ids)}")
    print(f"  共同的基因ID数量: {len(csv_gene_ids & txt_gene_ids)}")
    print(f"  CSV独有的基因ID数量: {len(csv_only)}")
    print(f"  TXT独有的基因ID数量: {len(txt_only)}")
    
    return csv_only, txt_only

def main():
    """主函数"""
    csv_file = "gene_info/gene_ids/ensembl_gene_id_unique.csv"
    txt_file = "gene_info/gene_ids/all_gene_ids.txt"
    
    csv_only, txt_only = compare_gene_ids(csv_file, txt_file)
    
    # 如果有差异，保存到文件
    if csv_only:
        with open("csv_only_gene_ids.txt", "w") as f:
            for gene_id in sorted(csv_only):
                f.write(f"{gene_id}\n")
        print(f"\nCSV独有的基因ID已保存到: csv_only_gene_ids.txt")
    
    if txt_only:
        with open("txt_only_gene_ids.txt", "w") as f:
            for gene_id in sorted(txt_only):
                f.write(f"{gene_id}\n")
        print(f"TXT独有的基因ID已保存到: txt_only_gene_ids.txt")

if __name__ == "__main__":
    main() 