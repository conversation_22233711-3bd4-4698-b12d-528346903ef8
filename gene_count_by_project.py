import pandas as pd
import csv

def count_genes_by_project():
    """
    统计translation_indices_results_grouped_filtered_processed_final_updated.csv文件中
    每个GENE ID在每个Project id中的出现次数
    """
    print("正在读取文件...")
    
    # 读取CSV文件
    df = pd.read_csv('translation_indices_results_grouped_filtered_processed_final_updated.csv')
    print(f"文件总行数: {len(df)}")
    
    # 显示列名
    print("文件列名:", df.columns.tolist())
    
    # 检查数据质量
    print("\nGENE ID和GENE symbol的唯一组合数:")
    unique_gene_combinations = df[['GENE ID', 'GENE symbol']].drop_duplicates()
    print(f"唯一组合数: {len(unique_gene_combinations)}")
    
    # 检查原始文件中的唯一基因符号数
    print(f"原始文件中唯一GENE symbol数: {df['GENE symbol'].nunique()}")
    print(f"原始文件中唯一GENE ID数: {df['GENE ID'].nunique()}")
    
    # 检查是否有重复的GENE ID对应不同的GENE symbol
    gene_id_mapping = df.groupby('GENE ID')['GENE symbol'].nunique()
    problematic_ids = gene_id_mapping[gene_id_mapping > 1]
    if len(problematic_ids) > 0:
        print(f"警告: 发现 {len(problematic_ids)} 个GENE ID对应多个GENE symbol的情况")
        print("前10个问题ID:")
        for gene_id in problematic_ids.head(10).index:
            symbols = df[df['GENE ID'] == gene_id]['GENE symbol'].unique()
            print(f"  {gene_id}: {list(symbols)}")
    else:
        print("确认: GENE ID和GENE symbol是一一对应的")
    
    # 检查是否有重复的GENE symbol对应不同的GENE ID
    symbol_id_mapping = df.groupby('GENE symbol')['GENE ID'].nunique()
    problematic_symbols = symbol_id_mapping[symbol_id_mapping > 1]
    if len(problematic_symbols) > 0:
        print(f"警告: 发现 {len(problematic_symbols)} 个GENE symbol对应多个GENE ID的情况")
        print("前10个问题symbol:")
        for symbol in problematic_symbols.head(10).index:
            gene_ids = df[df['GENE symbol'] == symbol]['GENE ID'].unique()
            print(f"  {symbol}: {list(gene_ids)}")
    
    print("\n正在统计每个基因ID在每个项目中的出现次数...")
    
    # 按Project id和GENE ID分组，计算每个基因ID在每个项目中的出现次数
    result = df.groupby(['Project id', 'GENE ID']).size().reset_index(name='count')
    
    # 添加GENE symbol信息 - 使用第一个遇到的symbol（如果有多个的话）
    gene_name_mapping = df.groupby('GENE ID')['GENE symbol'].first().reset_index()
    result = result.merge(gene_name_mapping, on='GENE ID', how='left')
    
    # 重新排列列的顺序
    result = result[['GENE ID', 'GENE symbol', 'Project id', 'count']]
    
    # 按GENE ID排序
    result = result.sort_values(['GENE ID', 'Project id'])
    
    print(f"统计结果总行数: {len(result)}")
    print(f"唯一基因ID数: {result['GENE ID'].nunique()}")
    print(f"唯一基因symbol数: {result['GENE symbol'].nunique()}")
    print(f"涉及项目数: {result['Project id'].nunique()}")
    
    # 分析为什么symbol数量会增加
    print("\n分析基因符号数量变化:")
    original_symbols = set(df['GENE symbol'].unique())
    result_symbols = set(result['GENE symbol'].unique())
    
    print(f"原始文件唯一symbol数: {len(original_symbols)}")
    print(f"结果文件唯一symbol数: {len(result_symbols)}")
    
    # 检查是否有新增的symbol
    new_symbols = result_symbols - original_symbols
    if new_symbols:
        print(f"新增的symbol数: {len(new_symbols)}")
        print("前10个新增symbol:", list(new_symbols)[:10])
    
    # 检查是否有丢失的symbol
    lost_symbols = original_symbols - result_symbols
    if lost_symbols:
        print(f"丢失的symbol数: {len(lost_symbols)}")
        print("前10个丢失symbol:", list(lost_symbols)[:10])
    
    # 显示统计信息
    print("\n统计概览:")
    print(f"平均每个基因ID-项目组合的转录本数: {result['count'].mean():.2f}")
    print(f"最大转录本数: {result['count'].max()}")
    print(f"最小转录本数: {result['count'].min()}")
    
    # 保存结果
    output_file = 'gene_count_by_project_results.csv'
    result.to_csv(output_file, index=False)
    print(f"\n结果已保存到: {output_file}")
    
    # 显示前几行结果
    print("\n结果示例(前10行):")
    print(result.head(10).to_string(index=False))
    
    # 显示一些统计信息
    print(f"\n各项目的基因ID数量:")
    project_gene_counts = result.groupby('Project id')['GENE ID'].nunique().sort_values(ascending=False)
    print(project_gene_counts.head(10).to_string())
    
    print(f"\n出现在最多项目中的基因ID(前10个):")
    gene_project_counts = result.groupby('GENE ID')['Project id'].nunique().sort_values(ascending=False)
    print(gene_project_counts.head(10).to_string())
    
    return result

if __name__ == "__main__":
    result = count_genes_by_project() 