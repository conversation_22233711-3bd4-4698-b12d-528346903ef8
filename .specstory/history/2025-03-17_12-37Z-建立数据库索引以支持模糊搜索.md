<!-- Generated by SpecStory -->

# 建立数据库索引以支持模糊搜索 (2025-03-17 12:37Z)

_**User**_

我需要搜索时候可能用到PMID	Title	Author	Doi，这四列进行模糊搜索。怎么给这四列建立数据库索引，这个搜索就或的意思，检查是否存在于PMID或xx或xx。这是这几列存储的类型。PMID INT PRIMARY KEY,
        Title VARCHAR(1024),
        Doi VARCHAR(255),
        Corresponding Author Information TEXT,
        Author TEXT,

---

_**Assistant**_

<think>
您想要为 `PMID`, `Title`, `Author`, 和 `Doi` 这四列建立索引，以便可以对它们进行模糊搜索，实现一个"或"的查询逻辑（如果搜索词出现在任何一列中，就返回结果）。

让我们分析这些列的数据类型：
- `PMID` 是 INT PRIMARY KEY - 已经有主键索引
- `Title` 是 VARCHAR(1024)
- `Doi` 是 VARCHAR(255)
- `Corresponding Author Information` 是 TEXT
- `Author` 是 TEXT

在MySQL中，对于模糊搜索（如使用LIKE操作符或全文搜索），有几种索引方式：

1. **对于VARCHAR字段**：可以使用常规索引，但对于LIKE '%sometext%'这样的查询，常规索引并不会很有效，因为它只能加速前缀匹配的查询（如LIKE 'sometext%'）。

2. **对于TEXT字段**：TEXT字段不能直接建立常规索引，但可以：
   - 为前缀建立索引，如 INDEX(column_name(255))
   - 使用全文索引（FULLTEXT INDEX）

对于您的需求（模糊搜索并支持"或"逻辑），最合适的解决方案是**使用FULLTEXT索引**。全文索引允许使用MySQL的MATCH ... AGAINST语法进行复杂的文本搜索，包括自然语言模式和布尔模式。

需要考虑的因素：
1. PMID已经是主键，有索引
2. Title (VARCHAR)和Doi (VARCHAR)可以使用常规索引或FULLTEXT索引
3. Author (TEXT)和Corresponding Author Information (TEXT)应该使用FULLTEXT索引

基于以上分析，我将提供创建适当索引并实现模糊搜索的代码。
</think>

# 为数据库表创建索引支持模糊搜索

要对 `PMID`, `Title`, `Author`, 和 `Doi` 这四列建立索引以支持模糊搜索（OR 逻辑），最好的方法是使用 MySQL 的 FULLTEXT 索引。这种索引专为文本搜索而设计，可以处理模糊匹配和"OR"条件。

## 创建 FULLTEXT 索引

```sql
-- 为文本字段创建全文索引
ALTER TABLE paper_info ADD FULLTEXT INDEX ft_paper_search (Title, Doi, `Corresponding Author Information`, Author);
```

这将创建一个跨这些列的联合全文索引。对于 `PMID` 列，它已经是主键，已经有索引了。

## 实现代码

以下是创建索引和实现搜索的完整 Python 代码：

```python
import pandas as pd
import mysql.connector

# 数据库配置信息
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def create_fulltext_index():
    """创建全文索引以支持模糊搜索"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        # 创建全文索引
        fulltext_index_sql = """
        ALTER TABLE paper_info ADD FULLTEXT INDEX ft_paper_search 
        (Title, Doi, `Corresponding Author Information`, Author);
        """
        cursor.execute(fulltext_index_sql)
        conn.commit()
        print("全文索引创建成功！")
    except mysql.connector.Error as err:
        print(f"创建索引时出错: {err}")
    finally:
        cursor.close()
        conn.close()

def search_papers(search_term):
    """使用全文搜索查询论文信息"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor(dictionary=True)
    
    try:
        # 使用全文搜索
        search_sql = """
        SELECT * FROM paper_info
        WHERE MATCH(Title, Doi, `Corresponding Author Information`, Author) 
        AGAINST (%s IN NATURAL LANGUAGE MODE)
        OR PMID = %s
        """
        
        # 尝试将搜索词转换为整数（如果是PMID）
        try:
            pmid = int(search_term)
        except ValueError:
            pmid = 0  # 如果不是整数，设为0（不会匹配任何PMID）
        
        cursor.execute(search_sql, (search_term, pmid))
        results = cursor.fetchall()
        
        return results
    except mysql.connector.Error as err:
        print(f"搜索时出错: {err}")
        return []
    finally:
        cursor.close()
        conn.close()

# 示例：创建索引并执行搜索
if __name__ == '__main__':
    # 1. 创建全文索引（只需要执行一次）
    create_fulltext_index()
    
    # 2. 执行搜索示例
    search_keyword = input("请输入搜索关键词: ")
    results = search_papers(search_keyword)
    
    # 3. 显示结果
    print(f"找到 {len(results)} 条匹配结果:")
    for i, paper in enumerate(results):
        print(f"\n--- 结果 {i+1} ---")
        print(f"PMID: {paper['PMID']}")
        print(f"标题: {paper['Title']}")
        print(f"作者: {paper['Author']}")
        print(f"DOI: {paper['Doi']}")
```

## 高级搜索选项

您还可以使用 MySQL 的布尔模式来实现更复杂的搜索：

```python
def advanced_search(search_term):
    """使用布尔模式进行高级搜索"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor(dictionary=True)
    
    search_sql = """
    SELECT * FROM paper_info
    WHERE MATCH(Title, Doi, `Corresponding Author Information`, Author) 
    AGAINST (%s IN BOOLEAN MODE)
    OR PMID = %s
    """
    
    try:
        pmid = int(search_term)
    except ValueError:
        pmid = 0
    
    cursor.execute(search_sql, (search_term, pmid))
    results = cursor.fetchall()
    
    cursor.close()
    conn.close()
    return results
```

### 布尔模式搜索示例

- 搜索包含"protein"和"synthesis"的文章: `+protein +synthesis`
- 搜索包含"cancer"但不包含"lung"的文章: `cancer -lung`
- 搜索包含"gene"或"dna"的文章: `gene dna`

## 注意事项

1. FULLTEXT索引只能在MyISAM或InnoDB存储引擎中使用，且MySQL 5.6+版本对它有更好的支持。

2. 默认情况下，MySQL全文搜索会忽略少于3个字符的单词，这是由`ft_min_word_len`参数控制的。

3. 对于整数类型的PMID，虽然已经有主键索引，但在全文搜索中不会被匹配，所以代码中特别处理了这种情况。

4. 如果表中数据量很大，创建索引可能需要一些时间。

这种索引和搜索方法将为您提供高效的模糊搜索功能，满足您在多个列中进行"OR"逻辑搜索的需求。

---

_**User**_

是的，我只需要你为这几列创建FULL_TEXT索引在创建的时候。PMID, Title, Author, 和 Doi 。帮我修改代码即可。

---

_**Assistant**_

<think>
我需要修改代码，为 `PMID`、`Title`、`Author` 和 `Doi` 这四列创建 FULLTEXT 索引。需要注意的是，`PMID` 是整数类型，通常不能直接添加到 FULLTEXT 索引中，但我可以为其他三列创建 FULLTEXT 索引。

让我修改代码，在创建表的部分添加 FULLTEXT 索引。我会修改 `create_table_sql` 语句，在创建表的同时添加 FULLTEXT 索引。

我需要修改的是主函数中的创建表部分代码。
</think>

下面是修改后的代码，在创建表的同时为 `Title`, `Author` 和 `Doi` 创建 FULLTEXT 索引（注意 `PMID` 是整数类型，不能加入 FULLTEXT 索引，但它已经是主键已经有索引了）：

```python:data_storage.ipynb
import pandas as pd
import mysql.connector

# 数据库配置信息
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306  # 端口号设置为整型
}

def main():
    # 1. 读取 Excel 文件
    # 注意：确保 Excel 文件的列名与下方 SQL 中的字段名完全一致
    df = pd.read_excel('Browse-Publication/paper_info.xlsx')

    # 2. 连接 MySQL 数据库
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()

    # 3. 创建数据表（如果不存在则创建）
    #    表名为 paper_info，以 PMID 为主键，其他字段类型根据数据长度设置
    #    并为Title, Author, Doi创建FULLTEXT索引用于模糊搜索
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS paper_info (
        PMID INT PRIMARY KEY,
        Title VARCHAR(1024),
        Doi VARCHAR(255),
        `Corresponding Author Information` TEXT,
        Author TEXT,
        Abstract TEXT,
        FULLTEXT INDEX ft_paper_search (Title, Doi, Author)
    )
    """
    cursor.execute(create_table_sql)

    # 4. 准备插入数据的 SQL 语句
    # ... 以下代码保持不变 ...
```

如果表已经存在，需要额外添加 FULLTEXT 索引，可以使用以下代码：

```python
def add_fulltext_index():
    """为已存在的表添加FULLTEXT索引"""
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    try:
        # 尝试添加FULLTEXT索引
        add_index_sql = """
        ALTER TABLE paper_info 
        ADD FULLTEXT INDEX ft_paper_search (Title, Doi, Author);
        """
        cursor.execute(add_index_sql)
        conn.commit()
        print("FULLTEXT索引创建成功！")
    except mysql.connector.Error as err:
        print(f"创建索引时出错: {err}")
    finally:
        cursor.close()
        conn.close()

# 如果表已存在且需要添加索引，可以调用此函数
# add_fulltext_index()
```

注意：
1. FULLTEXT 索引只能用于文本类型字段，如 VARCHAR, TEXT, CHAR 等，不能用于 INT 类型的 PMID
2. PMID 已经是主键，自动有索引了，可以高效查询
3. 如果使用搜索功能，可以同时检索 PMID 和其他带有全文索引的字段

---

