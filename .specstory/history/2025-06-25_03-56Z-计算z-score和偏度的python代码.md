<!-- Generated by SpecStory -->

# 计算Z-score和偏度的Python代码 (2025-06-25 03:56Z)

_**User**_

只要TE、TR或EVI存在值，就计算一下ZSCORE、LEVEL，还有每种情况的的skewness，写一下python代码完成。参考下面代码：# 清空环境变量，防止旧变量干扰
rm(list = ls())

# 加载包
packages <- c("ggplot2", "readr", "dplyr", "e1071")
installed <- rownames(installed.packages())
for (pkg in packages) {
  if (!(pkg %in% installed)) install.packages(pkg)
}
lapply(packages, library, character.only = TRUE)

# 路径设置
base_path <- "D:/UTR_TE/Page_Design/Project_Detail_fig/split_output"
filename <- "PRJNA379447_GSE96716_PC9_Lung_adenocarcinoma.csv"
setwd(base_path)

# 读取数据
df <- read_csv(filename)

# 确保df是tibble，兼容dplyr函数
df <- as_tibble(df)

# 打印原始数据行数
cat("原始数据行数:", nrow(df), "\n")

# 去除 NA 和 TE ≤ 0 的行
df <- df[!is.na(df$TE) & df$TE > 0, ]
cat("过滤后剩余数据行数:", nrow(df), "\n")

# 计算 log2TE 和 z-score
df$log2TE <- log2(df$TE)
df$zscore <- as.numeric(scale(df$log2TE))

# 检查是否有 NA
if (any(is.na(df$zscore))) {
  df <- df[!is.na(df$zscore), ]
}

# 保留基本信息，使用dplyr::select
df <- dplyr::select(df, transcript_id, TE, log2TE, zscore)

# 计算分位点
quartiles <- quantile(df$zscore, probs = c(0.25, 0.5, 0.75))

# 分组（include.lowest=TRUE，防止边界问题）
df$quantile_group <- cut(df$zscore,
                         breaks = c(-Inf, quartiles[1], quartiles[2], quartiles[3], Inf),
                         labels = c("Q1", "Q2", "Q3", "Q4"),
                         include.lowest = TRUE,
                         right = TRUE)

# 颜色设置
color_map <- c("Q1" = "#D73027", "Q2" = "#FC8D59", "Q3" = "#91BFDB", "Q4" = "#4575B4")
df$color <- color_map[as.character(df$quantile_group)]

# 检查每组数量
cat("各分组数量:\n")
print(table(df$quantile_group))

# 计算偏度和总数
z_skew <- e1071::skewness(df$zscore)
n_total <- nrow(df)

# 图形输出目录
out_dir <- file.path("D:/UTR_TE/Page_Design/Project_Detail_fig/fig", filename)
if (!dir.exists(out_dir)) dir.create(out_dir, recursive = TRUE)

# 绘图
binwidth <- 0.2
p <- ggplot(df, aes(x = zscore, fill = quantile_group)) +
  geom_histogram(binwidth = binwidth, color = "black", boundary = 0) +
  scale_fill_manual(values = color_map) +
  labs(x = expression(log[2]~"TE (z-score)"),
       y = "Frequency",
       fill = "Quantile") +
  theme_minimal(base_size = 16) +
  
  # 添加 0σ 竖线
  geom_vline(xintercept = 0, linetype = "dashed", color = "black", linewidth = 1) +
  
  # 添加 skewness 和样本量信息
  geom_text(x = min(df$zscore), y = max(table(cut(df$zscore, breaks = seq(-6, 6, binwidth)))) * 0.9,
            label = paste0("skewness = ", round(z_skew, 3)),
            hjust = 0, size = 5) +
  geom_text(x = min(df$zscore), y = max(table(cut(df$zscore, breaks = seq(-6, 6, binwidth)))) * 0.8,
            label = paste0("n = ", format(n_total, big.mark = ",")),
            hjust = 0, size = 5) +
  scale_x_continuous(breaks = seq(-6, 6, 2), labels = paste0(seq(-6, 6, 2), "σ"))

# 保存图像
ggsave(filename = file.path(out_dir, "TE_zscore_histogram.png"),
       plot = p,
       width = 8, height = 6, dpi = 300)

# 导出每个分组的 transcript_id 和 zscore
for (q in c("Q1", "Q2", "Q3", "Q4")) {
  sub_df <- df %>% filter(quantile_group == q) %>% dplyr::select(transcript_id, zscore)
  write.table(sub_df,
              file = file.path(out_dir, paste0(q, "_transcripts_zscore.txt")),
              quote = FALSE, sep = "\t", row.names = FALSE)
}


---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要基于数据中的TE、TR或EVI列，只要存在值就计算ZSCORE、LEVEL，还有每种情况的skewness。用户提供的R代码是用来处理TE数据的例子，现在需要我写一个Python代码来完成类似的功能。

从附加的数据可以看到，数据格式是CSV，包含：
- TRANSCRIPT_ID
- GENEID  
- DATASETID
- TE, TE_ZSCORE, TE_LEVEL
- TR, TR_ZSCORE, TR_LEVEL  
- EVI, EVI_ZSCORE, EVI_LEVEL

用户想要：
1. 对于TE、TR、EVI，只要存在值就计算ZSCORE和LEVEL
2. 计算每种情况的skewness
3. 参考提供的R代码逻辑

R代码的主要逻辑：
1. 读取数据
2. 过滤掉NA和<=0的值
3. 计算log2变换
4. 计算z-score
5. 按z-score分四分位数分组(Q1-Q4)
6. 计算偏度
7. 绘图和保存结果

我需要写Python代码来实现类似功能，需要：
1. 读取CSV数据
2. 对TE、TR、EVI三列分别处理
3. 计算log2、z-score、分位数分组
4. 计算skewness
5. 更新对应的ZSCORE和LEVEL列

我先看看当前目录中是否有相关的数据文件，然后写代码。</details></think>

---

我来帮您编写Python代码来处理TE、TR、EVI数据，计算ZSCORE、LEVEL和skewness。首先让我查看一下您的数据文件结构。

---

<details>
            <summary>Listed current directory • **100** results</summary>
        
| Name |
|-------|
| 📁 `skewness_test` |
| 📁 `gene` |
| 📄 `import_tbkb_direct.py` |
| 📄 `import_tbkb_backup_improved.py` |
| 📄 `tbkb_direct_import.log` |
| 📄 `tbkb_safe_import.log` |
| 📄 `import_tbkb_backup_safe.py` |
| 📄 `tbkb_import.log` |
| 📄 `import_tbkb_backup.py` |
| 📁 `tbkb` |
| 📁 `Statistic` |
| 📁 `go_kegg` |
| 📄 `Project_info_import.py` |
| 📄 `transcript_info_import.log` |
| 📄 `transcript_info_import.py` |
| 📄 `gene_count_by_project_results.csv` |
| 📄 `Sample_info_import.py` |
| 📄 `translation_indices_import.log` |
| 📁 `transcriptinfo` |
| 📁 `gene_info` |
| 📄 `gene_count_by_project.py` |
| 📄 `translation_indices_import.py` |
| 📄 `update_translation_indices_gene_info.log` |
| 📄 `translation_indices_results_grouped_filtered_processed_final_updated.csv` |
| 📄 `update_translation_indices_gene_info.py` |
| 📄 `process_ensembl_gene_mapping.log` |
| 📄 `ensembl_gene_id_with_full_info_split_2_processed.csv` |
| 📄 `process_ensembl_gene_mapping.py` |
| 📄 `unique_gene_id_symbol_mapping copy.csv` |
| 📁 `Browse-Sample` |
| 📄 `unique_gene_id_symbol_mapping.csv` |
| 📄 `extract_unique_gene_info.log` |
| 📄 `extract_unique_gene_info.py` |
| 📄 `unique_gene_id_symbol_mapping_2.csv` |
| 📄 `extract_unique_transcripts.log` |
| 📄 `unique_transcript_gene_mapping.csv` |
| 📄 `extract_unique_transcripts.py` |
| 📄 `ensembl_gene_id_with_full_info_split_2.csv` |
| 📄 `unique_transcript_gene_mapping_with_location.csv` |
| 📄 `add_transcript_location.log` |
| 📄 `add_transcript_location.py` |
| 📄 `unique_transcript_gene_mapping_with_utr_updated.csv` |
| 📄 `ensembl_gene_info_scraper.log` |
| 📄 `ensembl_gene_id_with_full_info_2.csv` |
| 📄 `ensembl_gene_info_scraper.py` |
| 📄 `gene_info_results_2.json.checkpoint` |
| 📄 `gene_info_batch_scraper_enhanced.py` |
| 📄 `ensembl_gene_id_unique_2.csv` |
| 📄 `check_gene_id_existence.log` |
| 📄 `gene_ids_not_in_ensembl.csv` |
| 📄 `check_gene_id_existence.py` |
| 📄 `check_data_statistics.log` |
| 📄 `check_data_statistics.py` |
| 📄 `add_utr_components.log` |
| 📄 `translation_indices_results_grouped_filtered_processed_final.csv` |
| 📄 `add_utr_components.py` |
| 📄 `Transcript_id_with_UTR_info_and_genes_cleaned.csv` |
| 📄 `clean_utr_characteristics.log` |
| 📄 `clean_utr_characteristics.py` |
| 📄 `update_gene_symbols.log` |
| 📄 `translation_indices_results_grouped_filtered_processed_updated.csv` |
| 📄 `update_gene_symbols.py` |
| 📄 `Transcript_id_with_UTR_info_and_genes.csv` |
| 📄 `gene_import.log` |
| 📄 `gene_count_by_project_results_with_info.csv` |
| 📄 `ensembl_gene_id_with_transcript_info.csv` |
| 📄 `add_gene_info_to_csv.py` |
| 📄 `add_gene_info.log` |
| 📄 `ensembl_gene_id_with_info.csv` |
| 📄 `compare_gene_ids.py` |
| 📄 `extract_gene_ids.log` |
| 📄 `extract_unique_gene_ids.py` |
| 📄 `gene_info_import.py` |
| 📄 `translation_indices_results_grouped_filtered_processed.csv` |
| 📁 `Browse-Project` |
| 📄 `Project.csv` |
| 📁 `reference` |
| 📁 `.specstory` |
| 📄 `bulk_translation_indices.py` |
| 📄 `calculate_translation_indices.py` |
| 📄 `import_tpm_to_db.py` |
| 📄 `data_storage.ipynb` |
| 📄 `.gitignore` |
| 📄 `.cursorindexingignore` |
| 📄 `paper_info_import.py` |
| 📄 `gene_info_batch_scraper.py` |
| 📄 `gene_info_results.csv.checkpoint` |
| 📄 `gene_batch_processor.py` |
| 📄 `ensembl_gene_id_unique.csv` |
| 📄 `gene_info_enhanced_scraper.py` |
| 📄 `gene_info_scraper.py` |
| 📁 `Browse-Publication` |
| 📄 `paper_info_html_scraper.py` |
| 📄 `paper_info_web_scraper.py` |
| 📄 `paper_info_enhanced.py` |
| 📄 `paper_info_fetcher.py` |
| 📄 `extract_unique_values.py` |
| 📄 `import_gse_match_to_db.py` |
| 📄 `local_translation_metrics.py` |
| 📄 `optimized_bulk_indices.py` |

</details>

---

让我查看一下skewness_test目录，看是否有相关的测试数据：

---

<details>
            <summary>Listed directory skewness_test • **1** results</summary>
        
| Name |
|-------|
| 📄 `TEDD00001_transcripts.csv` |

</details>

---

很好！让我查看一下这个CSV文件的结构，看是否包含TE、TR、EVI数据：

---

File is too large, >2.00MB

---

让我先用grep来查看文件的头部信息：

---

```bash
head -5 skewness_test/TEDD00001_transcripts.csv
```

```
TRANSCRIPT_ID,GENEID,DATASETID,TE,TE_ZSCORE,TE_LEVEL,TR,TR_ZSCORE,TR_LEVEL,EVI,E
VI_ZSCORE,EVI_LEVEL
ENST00000000233,ENSG00000004059,TEDD00001,4.140894,1.3901,Q4,,,,,,
ENST00000000412,ENSG00000003056,TEDD00001,1.741946,0.2844,Q3,,,,,,
ENST00000000442,ENSG00000173153,TEDD00001,0.311053,-1.9155,Q1,,,,,,
ENST00000001008,ENSG00000004478,TEDD00001,2.452474,0.7212,Q4,,,,,,

```

---

完美！现在我看到了数据结构。基于您提供的R代码逻辑，我来编写一个Python脚本来处理TE、TR、EVI数据，计算ZSCORE、LEVEL和skewness：

---

```diff
- 
+ #!/usr/bin/env python3
+ # -*- coding: utf-8 -*-
+ """
+ 计算翻译效率指标的Z-score、分位数等级和偏度
+ 基于R代码逻辑的Python实现
+ """
+ 
+ import pandas as pd
+ import numpy as np
+ import matplotlib.pyplot as plt
+ import seaborn as sns
+ from scipy import stats
+ import os
+ import warnings
+ warnings.filterwarnings('ignore')
+ 
+ class TranslationIndicesAnalyzer:
+     """翻译效率指标分析器"""
+     
+     def __init__(self, input_file, output_dir=None):
+         """
+         初始化分析器
+         
+         Args:
+             input_file: 输入CSV文件路径
+             output_dir: 输出目录，如果为None则使用输入文件名创建目录
+         """
+         self.input_file = input_file
+         self.df = None
+         self.results = {}
+         
+         # 设置输出目录
+         if output_dir is None:
+             base_name = os.path.splitext(os.path.basename(input_file))[0]
+             self.output_dir = f"analysis_output_{base_name}"
+         else:
+             self.output_dir = output_dir
+             
+         # 创建输出目录
+         os.makedirs(self.output_dir, exist_ok=True)
+         
+         # 设置中文字体（如果需要）
+         plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial', 'DejaVu Sans']
+         plt.rcParams['axes.unicode_minus'] = False
+         
+     def load_data(self):
+         """加载数据"""
+         print(f"正在加载数据: {self.input_file}")
+         self.df = pd.read_csv(self.input_file)
+         print(f"原始数据行数: {len(self.df)}")
+         return self.df
+     
+     def calculate_metrics_for_column(self, column_name):
+         """
+         为指定列计算指标
+         
+         Args:
+             column_name: 列名 ('TE', 'TR', 'EVI')
+             
+         Returns:
+             dict: 包含处理结果的字典
+         """
+         print(f"\n处理 {column_name} 数据...")
+         
+         # 获取对应的列名
+         zscore_col = f"{column_name}_ZSCORE"
+         level_col = f"{column_name}_LEVEL"
+         
+         # 创建工作副本
+         work_df = self.df.copy()
+         
+         # 过滤有效数据：不为NA且大于0
+         valid_mask = (work_df[column_name].notna()) & (work_df[column_name] > 0)
+         valid_data = work_df[valid_mask].copy()
+         
+         if len(valid_data) == 0:
+             print(f"{column_name} 没有有效数据")
+             return None
+             
+         print(f"{column_name} 有效数据行数: {len(valid_data)}")
+         
+         # 计算log2变换
+         valid_data[f'log2_{column_name}'] = np.log2(valid_data[column_name])
+         
+         # 计算z-score
+         log_values = valid_data[f'log2_{column_name}']
+         z_scores = stats.zscore(log_values, nan_policy='omit')
+         valid_data['zscore'] = z_scores
+         
+         # 移除z-score中的NA值
+         valid_data = valid_data.dropna(subset=['zscore'])
+         print(f"{column_name} 计算z-score后数据行数: {len(valid_data)}")
+         
+         # 计算分位数
+         quartiles = np.percentile(valid_data['zscore'], [25, 50, 75])
+         
+         # 分组
+         def assign_quantile(zscore):
+             if zscore <= quartiles[0]:
+                 return 'Q1'
+             elif zscore <= quartiles[1]:
+                 return 'Q2'
+             elif zscore <= quartiles[2]:
+                 return 'Q3'
+             else:
+                 return 'Q4'
+         
+         valid_data['quantile_group'] = valid_data['zscore'].apply(assign_quantile)
+         
+         # 计算偏度
+         skewness = stats.skew(valid_data['zscore'])
+         
+         # 更新原始数据框中的ZSCORE和LEVEL列
+         # 首先清空这些列
+         work_df[zscore_col] = np.nan
+         work_df[level_col] = ''
+         
+         # 更新有效数据的值
+         for idx, row in valid_data.iterrows():
+             work_df.loc[idx, zscore_col] = row['zscore']
+             work_df.loc[idx, level_col] = row['quantile_group']
+         
+         # 统计各组数量
+         group_counts = valid_data['quantile_group'].value_counts().sort_index()
+         print(f"{column_name} 各分组数量:")
+         for group, count in group_counts.items():
+             print(f"  {group}: {count}")
+         
+         # 保存结果
+         result = {
+             'column_name': column_name,
+             'valid_data': valid_data,
+             'total_count': len(valid_data),
+             'skewness': skewness,
+             'quartiles': quartiles,
+             'group_counts': group_counts,
+             'updated_df': work_df
+         }
+         
+         print(f"{column_name} 偏度 (skewness): {skewness:.4f}")
+         
+         return result
+     
+     def create_histogram(self, result):
+         """创建直方图"""
+         column_name = result['column_name']
+         valid_data = result['valid_data']
+         skewness = result['skewness']
+         total_count = result['total_count']
+         
+         # 颜色映射
+         color_map = {'Q1': '#D73027', 'Q2': '#FC8D59', 'Q3': '#91BFDB', 'Q4': '#4575B4'}
+         
+         # 创建图形
+         plt.figure(figsize=(10, 6))
+         
+         # 绘制直方图
+         binwidth = 0.2
+         bins = np.arange(valid_data['zscore'].min(), valid_data['zscore'].max() + binwidth, binwidth)
+         
+         for group in ['Q1', 'Q2', 'Q3', 'Q4']:
+             group_data = valid_data[valid_data['quantile_group'] == group]['zscore']
+             if len(group_data) > 0:
+                 plt.hist(group_data, bins=bins, alpha=0.7, label=group, 
+                         color=color_map[group], edgecolor='black', linewidth=0.5)
+         
+         # 添加0σ竖线
+         plt.axvline(x=0, linetype='dashed', color='black', linewidth=1)
+         
+         # 设置标签和标题
+         plt.xlabel(f'log₂ {column_name} (z-score)', fontsize=14)
+         plt.ylabel('Frequency', fontsize=14)
+         plt.title(f'{column_name} Z-score Distribution', fontsize=16)
+         plt.legend(title='Quantile')
+         
+         # 添加偏度和样本量信息
+         y_max = plt.ylim()[1]
+         x_min = plt.xlim()[0]
+         plt.text(x_min + 0.5, y_max * 0.9, f'skewness = {skewness:.3f}', 
+                 fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
+         plt.text(x_min + 0.5, y_max * 0.8, f'n = {total_count:,}', 
+                 fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
+         
+         # 设置x轴刻度
+         x_ticks = np.arange(-6, 7, 2)
+         plt.xticks(x_ticks, [f'{x}σ' for x in x_ticks])
+         
+         plt.tight_layout()
+         
+         # 保存图像
+         output_path = os.path.join(self.output_dir, f'{column_name}_zscore_histogram.png')
+         plt.savefig(output_path, dpi=300, bbox_inches='tight')
+         plt.close()
+         
+         print(f"已保存 {column_name} 直方图: {output_path}")
+     
+     def save_quantile_data(self, result):
+         """保存各分位数组的数据"""
+         column_name = result['column_name']
+         valid_data = result['valid_data']
+         
+         # 为每个分组保存transcript_id和zscore
+         for group in ['Q1', 'Q2', 'Q3', 'Q4']:
+             group_data = valid_data[valid_data['quantile_group'] == group]
+             if len(group_data) > 0:
+                 output_data = group_data[['TRANSCRIPT_ID', 'zscore']].copy()
+                 output_path = os.path.join(self.output_dir, f'{column_name}_{group}_transcripts_zscore.txt')
+                 output_data.to_csv(output_path, sep='\t', index=False)
+                 print(f"已保存 {column_name} {group} 数据: {output_path}")
+     
+     def process_all_metrics(self):
+         """处理所有指标"""
+         # 加载数据
+         self.load_data()
+         
+         # 处理三个指标
+         metrics = ['TE', 'TR', 'EVI']
+         updated_df = self.df.copy()
+         
+         for metric in metrics:
+             if metric in self.df.columns:
+                 result = self.calculate_metrics_for_column(metric)
+                 if result is not None:
+                     self.results[metric] = result
+                     # 更新数据框
+                     updated_df = result['updated_df']
+                     # 创建图表
+                     self.create_histogram(result)
+                     # 保存分组数据
+                     self.save_quantile_data(result)
+             else:
+                 print(f"警告: 列 {metric} 不存在于数据中")
+         
+         # 保存更新后的完整数据
+         output_path = os.path.join(self.output_dir, 'updated_data_with_zscore_level.csv')
+         updated_df.to_csv(output_path, index=False)
+         print(f"\n已保存更新后的完整数据: {output_path}")
+         
+         # 生成总结报告
+         self.generate_summary_report()
+     
+     def generate_summary_report(self):
+         """生成总结报告"""
+         report_path = os.path.join(self.output_dir, 'analysis_summary.txt')
+         
+         with open(report_path, 'w', encoding='utf-8') as f:
+             f.write("翻译效率指标分析总结报告\n")
+             f.write("=" * 50 + "\n\n")
+             
+             f.write(f"输入文件: {self.input_file}\n")
+             f.write(f"原始数据总行数: {len(self.df)}\n\n")
+             
+             for metric, result in self.results.items():
+                 f.write(f"{metric} 指标分析结果:\n")
+                 f.write("-" * 30 + "\n")
+                 f.write(f"有效数据行数: {result['total_count']}\n")
+                 f.write(f"偏度 (skewness): {result['skewness']:.4f}\n")
+                 f.write(f"分位数 (25%, 50%, 75%): {result['quartiles']}\n")
+                 f.write("各分组数量:\n")
+                 for group, count in result['group_counts'].items():
+                     f.write(f"  {group}: {count}\n")
+                 f.write("\n")
+         
+         print(f"已生成分析报告: {report_path}")
+ 
+ def main():
+     """主函数"""
+     # 示例使用
+     input_file = "skewness_test/TEDD00001_transcripts.csv"
+     
+     if not os.path.exists(input_file):
+         print(f"错误: 文件 {input_file} 不存在")
+         return
+     
+     # 创建分析器并处理数据
+     analyzer = TranslationIndicesAnalyzer(input_file)
+     analyzer.process_all_metrics()
+     
+     print("\n分析完成！请查看输出目录中的结果文件。")
+ 
+ if __name__ == "__main__":
+     main() 
```

---

现在让我运行这个脚本来处理您的数据：

---

```bash
python calculate_zscore_and_skewness.py
```

```
(base) zhy@zhouhengyudeMacBook-Pro 数据库存储信息 % python calculate_zscore_and_skewness.py
Traceback (most recent call last):
  File "/Volumes/zhy/项目/Utr翻译效率/数据库存储信息/calculate_zscore_and_skewness.py", line 11, in <module>
    import seaborn as sns
ModuleNotFoundError: No module named 'seaborn'
(base) zhy@zhouhengyudeMacBook-Pro 数据库存储信息 %
```

---

让我修改代码，移除对seaborn的依赖，并安装所需的scipy库：

---

```bash
pip install scipy matplotlib
```

```
(base) zhy@zhouhengyudeMacBook-Pro 数据库存储信息 % pip install scipy matplotlib
Requirement already satisfied: scipy in /Users/<USER>/anaconda3/lib/python3.11/site-packages (1.14.1)
Requirement already satisfied: matplotlib in /Users/<USER>/anaconda3/lib/python3.11/site-packages (3.9.3)
Requirement already satisfied: numpy<2.3,>=1.23.5 in /Users/<USER>/anaconda3/lib/python3.11/site-packages (from
 scipy) (1.26.4)
Requirement already satisfied: contourpy>=1.0.1 in /Users/<USER>/anaconda3/lib/python3.11/site-packages (from m
atplotlib) (1.3.1)
Requirement already satisfied: cycler>=0.10 in /Users/<USER>/anaconda3/lib/python3.11/site-packages (from matpl
otlib) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in /Users/<USER>/anaconda3/lib/python3.11/site-packages (from 
matplotlib) (4.55.2)
Requirement already satisfied: kiwisolver>=1.3.1 in /Users/<USER>/anaconda3/lib/python3.11/site-packages (from 
matplotlib) (1.4.7)
Requirement already satisfied: packaging>=20.0 in /Users/<USER>/anaconda3/lib/python3.11/site-packages (from ma
tplotlib) (24.1)
Requirement already satisfied: pillow>=8 in /Users/<USER>/anaconda3/lib/python3.11/site-packages (from matplotl
ib) (10.4.0)
Requirement already satisfied: pyparsing>=2.3.1 in /Users/<USER>/anaconda3/lib/python3.11/site-packages (from m
atplotlib) (3.2.0)
Requirement already satisfied: python-dateutil>=2.7 in /Users/<USER>/anaconda3/lib/python3.11/site-packages (fr
om matplotlib) (2.9.0.post0)
Requirement already satisfied: six>=1.5 in /Users/<USER>/anaconda3/lib/python3.11/site-packages (from python-da
teutil>=2.7->matplotlib) (1.16.0)
(base) zhy@zhouhengyudeMacBook-Pro 数据库存储信息 %
```

---

<think><details><summary>Thought Process</summary>用户想要基于数据中的TE、TR或EVI列，只要存在值就计算ZSCORE、LEVEL，还有每种情况的skewness。用户提供的R代码是用来处理TE数据的例子，现在需要我写一个Python代码来完成类似的功能。

从附加的数据可以看到，数据格式是CSV，包含：
- TRANSCRIPT_ID
- GENEID  
- DATASETID
- TE, TE_ZSCORE, TE_LEVEL
- TR, TR_ZSCORE, TR_LEVEL  
- EVI, EVI_ZSCORE, EVI_LEVEL

用户想要：
1. 对于TE、TR、EVI，只要存在值就计算ZSCORE和LEVEL
2. 计算每种情况的skewness
3. 参考提供的R代码逻辑

R代码的主要逻辑：
1. 读取数据
2. 过滤掉NA和<=0的值
3. 计算log2变换
4. 计算z-score
5. 按z-score分四分位数分组(Q1-Q4)
6. 计算偏度
7. 绘图和保存结果

我需要写Python代码来实现类似功能，需要：
1. 读取CSV数据
2. 对TE、TR、EVI三列分别处理
3. 计算log2、z-score、分位数分组
4. 计算skewness
5. 更新对应的ZSCORE和LEVEL列

我先看看当前目录中是否有相关的数据文件，然后写代码。</details></think>

---

现在让我修改代码，移除seaborn依赖：

---



---

现在再次运行脚本：

---

```bash
python calculate_zscore_and_skewness.py
```

```
(base) zhy@zhouhengyudeMacBook-Pro 数据库存储信息 % 
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 143, in _scalar_str
    elif np.issubdtype(dtype, np.number):
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/numerictypes.py", line 417, in issubdty
pe
    arg1 = dtype(arg1).type
           ^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 46, in __repr__
    arg_str = _construction_repr(dtype, include_align=False)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 100, in _construction_
repr
    return _scalar_str(dtype, short=short)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 104, in _scalar_str
    byteorder = _byte_order_str(dtype)
                ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/anaconda3/lib/python3.11/site-packages/numpy/core/_dtype.py", line 163, in _byte_order_st
r
    swapped = np.dtype(int).newbyteorder('S')
              ^^^^^^^^^^^^^
RecursionError: maximum recursion depth exceeded while calling a Python object

```

---

<think><details><summary>Thought Process</summary>看起来有一个numpy的递归错误问题。这可能是因为numpy版本兼容性问题或者环境配置问题。让我创建一个更简单的版本，避免这些复杂的依赖问题。

我将创建一个更简化的脚本，只使用最基本的库。</details></think>

---

看起来有numpy递归错误。让我创建一个更简化的版本，避免复杂的依赖问题：

---

```diff
- 
+ #!/usr/bin/env python3
+ # -*- coding: utf-8 -*-
+ """
+ 简化版本的翻译效率指标Z-score和偏度计算脚本
+ 避免复杂依赖问题
+ """
+ 
+ import pandas as pd
+ import math
+ import statistics
+ import os
+ 
+ def calculate_skewness(data):
+     """计算偏度"""
+     n = len(data)
+     if n < 3:
+         return 0
+     
+     mean = statistics.mean(data)
+     std = statistics.stdev(data)
+     
+     if std == 0:
+         return 0
+     
+     # 计算偏度
+     skew_sum = sum(((x - mean) / std) ** 3 for x in data)
+     skewness = skew_sum / n
+     
+     return skewness
+ 
+ def calculate_zscore(values):
+     """计算z-score"""
+     if len(values) == 0:
+         return []
+     
+     mean = statistics.mean(values)
+     std = statistics.stdev(values) if len(values) > 1 else 0
+     
+     if std == 0:
+         return [0] * len(values)
+     
+     return [(x - mean) / std for x in values]
+ 
+ def assign_quantile_level(zscore, quartiles):
+     """根据z-score分配分位数等级"""
+     if zscore <= quartiles[0]:
+         return 'Q1'
+     elif zscore <= quartiles[1]:
+         return 'Q2'
+     elif zscore <= quartiles[2]:
+         return 'Q3'
+     else:
+         return 'Q4'
+ 
+ def process_metric(df, metric_name):
+     """处理单个指标"""
+     print(f"\n处理 {metric_name} 数据...")
+     
+     # 获取对应列名
+     zscore_col = f"{metric_name}_ZSCORE"
+     level_col = f"{metric_name}_LEVEL"
+     
+     # 过滤有效数据：不为空且大于0
+     valid_data = []
+     valid_indices = []
+     
+     for idx, value in enumerate(df[metric_name]):
+         if pd.notna(value) and value > 0:
+             valid_data.append(value)
+             valid_indices.append(idx)
+     
+     if len(valid_data) == 0:
+         print(f"{metric_name} 没有有效数据")
+         return df, None
+     
+     print(f"{metric_name} 有效数据行数: {len(valid_data)}")
+     
+     # 计算log2变换
+     log2_values = [math.log2(x) for x in valid_data]
+     
+     # 计算z-score
+     z_scores = calculate_zscore(log2_values)
+     
+     # 计算分位数
+     sorted_zscores = sorted(z_scores)
+     n = len(sorted_zscores)
+     q1_idx = int(n * 0.25)
+     q2_idx = int(n * 0.5) 
+     q3_idx = int(n * 0.75)
+     
+     quartiles = [
+         sorted_zscores[q1_idx],
+         sorted_zscores[q2_idx], 
+         sorted_zscores[q3_idx]
+     ]
+     
+     # 分配等级
+     levels = [assign_quantile_level(z, quartiles) for z in z_scores]
+     
+     # 计算偏度
+     skewness = calculate_skewness(z_scores)
+     
+     # 更新数据框
+     df[zscore_col] = None
+     df[level_col] = ''
+     
+     for i, idx in enumerate(valid_indices):
+         df.loc[idx, zscore_col] = z_scores[i]
+         df.loc[idx, level_col] = levels[i]
+     
+     # 统计各组数量
+     level_counts = {}
+     for level in levels:
+         level_counts[level] = level_counts.get(level, 0) + 1
+     
+     print(f"{metric_name} 各分组数量:")
+     for level in ['Q1', 'Q2', 'Q3', 'Q4']:
+         count = level_counts.get(level, 0)
+         print(f"  {level}: {count}")
+     
+     print(f"{metric_name} 偏度 (skewness): {skewness:.4f}")
+     
+     # 保存分组数据
+     result = {
+         'metric_name': metric_name,
+         'valid_count': len(valid_data),
+         'skewness': skewness,
+         'quartiles': quartiles,
+         'level_counts': level_counts,
+         'z_scores': z_scores,
+         'levels': levels,
+         'valid_indices': valid_indices
+     }
+     
+     return df, result
+ 
+ def save_quantile_files(result, output_dir):
+     """保存各分位数组的文件"""
+     metric_name = result['metric_name']
+     z_scores = result['z_scores']
+     levels = result['levels']
+     valid_indices = result['valid_indices']
+     
+     # 按组保存
+     for target_level in ['Q1', 'Q2', 'Q3', 'Q4']:
+         output_data = []
+         for i, level in enumerate(levels):
+             if level == target_level:
+                 transcript_id = df.loc[valid_indices[i], 'TRANSCRIPT_ID']
+                 zscore = z_scores[i]
+                 output_data.append(f"{transcript_id}\t{zscore:.6f}")
+         
+         if output_data:
+             filename = os.path.join(output_dir, f"{metric_name}_{target_level}_transcripts_zscore.txt")
+             with open(filename, 'w') as f:
+                 f.write("TRANSCRIPT_ID\tzscore\n")
+                 f.write("\n".join(output_data))
+             print(f"已保存 {metric_name} {target_level} 数据: {filename}")
+ 
+ def main():
+     """主函数"""
+     input_file = "skewness_test/TEDD00001_transcripts.csv"
+     output_dir = "analysis_output_simple"
+     
+     # 创建输出目录
+     os.makedirs(output_dir, exist_ok=True)
+     
+     print(f"正在加载数据: {input_file}")
+     global df
+     df = pd.read_csv(input_file)
+     print(f"原始数据行数: {len(df)}")
+     
+     # 处理三个指标
+     metrics = ['TE', 'TR', 'EVI']
+     all_results = {}
+     
+     for metric in metrics:
+         if metric in df.columns:
+             df, result = process_metric(df, metric)
+             if result is not None:
+                 all_results[metric] = result
+                 save_quantile_files(result, output_dir)
+         else:
+             print(f"警告: 列 {metric} 不存在于数据中")
+     
+     # 保存更新后的数据
+     output_file = os.path.join(output_dir, "updated_data_with_zscore_level.csv")
+     df.to_csv(output_file, index=False)
+     print(f"\n已保存更新后的完整数据: {output_file}")
+     
+     # 生成总结报告
+     report_file = os.path.join(output_dir, "analysis_summary.txt")
+     with open(report_file, 'w', encoding='utf-8') as f:
+         f.write("翻译效率指标分析总结报告\n")
+         f.write("=" * 50 + "\n\n")
+         f.write(f"输入文件: {input_file}\n")
+         f.write(f"原始数据总行数: {len(df)}\n\n")
+         
+         for metric, result in all_results.items():
+             f.write(f"{metric} 指标分析结果:\n")
+             f.write("-" * 30 + "\n")
+             f.write(f"有效数据行数: {result['valid_count']}\n")
+             f.write(f"偏度 (skewness): {result['skewness']:.4f}\n")
+             f.write(f"分位数 (25%, 50%, 75%): {result['quartiles']}\n")
+             f.write("各分组数量:\n")
+             for level in ['Q1', 'Q2', 'Q3', 'Q4']:
+                 count = result['level_counts'].get(level, 0)
+                 f.write(f"  {level}: {count}\n")
+             f.write("\n")
+     
+     print(f"已生成分析报告: {report_file}")
+     print("\n分析完成！")
+ 
+ if __name__ == "__main__":
+     main() 
```

---

<think><details><summary>Thought Process</summary>用户想要基于数据中的TE、TR或EVI列，只要存在值就计算ZSCORE、LEVEL，还有每种情况的skewness。用户提供的R代码是用来处理TE数据的例子，现在需要我写一个Python代码来完成类似的功能。

从附加的数据可以看到，数据格式是CSV，包含：
- TRANSCRIPT_ID
- GENEID  
- DATASETID
- TE, TE_ZSCORE, TE_LEVEL
- TR, TR_ZSCORE, TR_LEVEL  
- EVI, EVI_ZSCORE, EVI_LEVEL

用户想要：
1. 对于TE、TR、EVI，只要存在值就计算ZSCORE和LEVEL
2. 计算每种情况的skewness
3. 参考提供的R代码逻辑

R代码的主要逻辑：
1. 读取数据
2. 过滤掉NA和<=0的值
3. 计算log2变换
4. 计算z-score
5. 按z-score分四分位数分组(Q1-Q4)
6. 计算偏度
7. 绘图和保存结果

我需要写Python代码来实现类似功能，需要：
1. 读取CSV数据
2. 对TE、TR、EVI三列分别处理
3. 计算log2、z-score、分位数分组
4. 计算skewness
5. 更新对应的ZSCORE和LEVEL列

我先看看当前目录中是否有相关的数据文件，然后写代码。</details></think>

---

现在运行简化版本的脚本：

---

```bash
python simple_zscore_calculator.py
```

```
正在加载数据: skewness_test/TEDD00001_transcripts.csv
原始数据行数: 57449

处理 TE 数据...
TE 有效数据行数: 57449
TE 各分组数量:
  Q1: 14363
  Q2: 14362
  Q3: 14362
  Q4: 14362
TE 偏度 (skewness): -0.0329
已保存 TE Q1 数据: analysis_output_simple/TE_Q1_transcripts_zscore.txt
已保存 TE Q2 数据: analysis_output_simple/TE_Q2_transcripts_zscore.txt
已保存 TE Q3 数据: analysis_output_simple/TE_Q3_transcripts_zscore.txt
已保存 TE Q4 数据: analysis_output_simple/TE_Q4_transcripts_zscore.txt

处理 TR 数据...
TR 没有有效数据

处理 EVI 数据...
EVI 没有有效数据

已保存更新后的完整数据: analysis_output_simple/updated_data_with_zscore_level.csv
已生成分析报告: analysis_output_simple/analysis_summary.txt

分析完成！

```

---



---

_**User**_

simple_zscore_calculator.py，修改代码，直接更新值在csv文件的基础写几列更新值即可。

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户希望修改simple_zscore_calculator.py代码，让它直接在原有CSV文件的基础上更新数值，而不是创建新的输出文件。也就是说，直接在原CSV文件中添加或更新ZSCORE和LEVEL列的值。

我需要修改代码，让它：
1. 读取原始CSV文件
2. 计算TE、TR、EVI的ZSCORE和LEVEL
3. 直接在原文件中更新这些列的值
4. 保存回原文件，覆盖原始数据

让我修改代码来实现这个需求。</details></think>

---

我来修改代码，让它直接在原CSV文件中更新ZSCORE和LEVEL列的值：

---



---

现在我也需要移除不再需要的`save_quantile_files`函数，因为我们不再生成单独的文件：

---



---

现在运行修改后的脚本来直接更新原CSV文件：

---

```bash
python simple_zscore_calculator.py
```

```
正在加载数据: skewness_test/TEDD00001_transcripts.csv
原始数据行数: 57449

处理 TE 数据...
TE 有效数据行数: 57449
TE 各分组数量:
  Q1: 14363
  Q2: 14362
  Q3: 14362
  Q4: 14362
TE 偏度 (skewness): -0.0329

处理 TR 数据...
TR 没有有效数据

处理 EVI 数据...
EVI 没有有效数据

已直接更新原文件: skewness_test/TEDD00001_transcripts.csv

==================================================
翻译效率指标分析总结报告
==================================================
文件: skewness_test/TEDD00001_transcripts.csv
总行数: 57449

TE 指标分析结果:
------------------------------
有效数据行数: 57449
偏度 (skewness): -0.0329
分位数 (25%, 50%, 75%): ['-0.5767', '-0.0173', '0.5768']
各分组数量:
  Q1: 14363
  Q2: 14362
  Q3: 14362
  Q4: 14362

分析完成！已直接更新CSV文件中的ZSCORE和LEVEL列。

```

---

让我们验证一下文件是否已正确更新：

---

```bash
head -5 skewness_test/TEDD00001_transcripts.csv
```

```
TRANSCRIPT_ID,GENEID,DATASETID,TE,TE_ZSCORE,TE_LEVEL,TR,TR_ZSCORE,TR_LEVEL,EVI,EVI_ZSCORE,EVI_LEVEL
ENST00000000233,ENSG00000004059,TEDD00001,4.140894,1.390078711394334,Q4,,,,,,
ENST00000000412,ENSG00000003056,TEDD00001,1.741946,0.2843736985006026,Q3,,,,,,
ENST00000000442,ENSG00000173153,TEDD00001,0.311053,-1.9155148267621933,Q1,,,,,,
ENST00000001008,ENSG00000004478,TEDD00001,2.452474,0.7212043626707589,Q4,,,,,,

```

---

<think><details><summary>Thought Process</summary>用户希望修改simple_zscore_calculator.py代码，让它直接在原有CSV文件的基础上更新数值，而不是创建新的输出文件。也就是说，直接在原CSV文件中添加或更新ZSCORE和LEVEL列的值。

我需要修改代码，让它：
1. 读取原始CSV文件
2. 计算TE、TR、EVI的ZSCORE和LEVEL
3. 直接在原文件中更新这些列的值
4. 保存回原文件，覆盖原始数据

让我修改代码来实现这个需求。</details></think>

---

完美！现在让我们验证一下TE_LEVEL列的分布情况：

---

```bash
cut -d',' -f6 skewness_test/TEDD00001_transcripts.csv | sort | uniq -c
```

---

