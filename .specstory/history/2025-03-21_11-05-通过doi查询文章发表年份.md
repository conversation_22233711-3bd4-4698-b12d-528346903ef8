<!-- Generated by SpecStory -->

# 通过DOI查询文章发表年份 (2025-03-21 19:05:19)

_**User**_

添加一列文章发表年份，通过DOI查询文章发表年份。具体到年即可。

---

_**Assistant**_

<think>
The user is asking me to add a publication year column to the CSV file, which would be derived from the DOI (Digital Object Identifier) information. This is a common task when working with academic paper datasets.

Let me understand what we need to do:

1. We have a CSV file with papers and their DOIs.
2. We need to add a new column for the publication year.
3. We need to determine the publication year by querying information using the DOI.

To accomplish this, we would need to create a Python script that:
1. Reads the CSV file
2. For each DOI, queries an API or service that can provide metadata about the publication, including the year
3. Adds this information as a new column to the CSV
4. Writes the updated CSV back to file

For querying DOI information, we could use libraries like:
- `requests` to make HTTP requests to DOI resolution services 
- `crossref-commons` or similar libraries that provide interfaces to the Crossref API
- `habanero` (a Python client for the Crossref API)

The DOI system redirects to the publisher's page, and services like Crossref have APIs that can provide metadata about academic publications.

Let me write a script that would accomplish this:
</think>

# Adding Publication Year Column to Paper Dataset

I'll create a Python script that reads your CSV file, queries publication years for each DOI, and adds a new "Year" column to the dataset.

This script will use the Crossref API (through the habanero library) to look up publication dates for each DOI in your dataset.

```python:YueGuangxi_Papers/add_publication_year.py
import pandas as pd
import time
import requests
from habanero import Crossref

# Initialize Crossref client with your email (recommended for API usage)
# Replace with your email for better rate limits
cr = Crossref(mailto="<EMAIL>")

# Read the CSV file
df = pd.read_csv('YueGuangxi_Papers/Sheet1_Deduplicated.csv')

# Add a new Year column
df['Year'] = None

# Function to get publication year from DOI
def get_publication_year(doi):
    try:
        # Get metadata from Crossref
        result = cr.works(ids=doi)
        
        # Extract publication date information
        if 'message' in result and 'published-print' in result['message']:
            date_parts = result['message']['published-print']['date-parts'][0]
            if len(date_parts) > 0:
                return date_parts[0]  # Return the year
        
        # If published-print is not available, try other date fields
        if 'message' in result and 'published' in result['message']:
            date_parts = result['message']['published']['date-parts'][0]
            if len(date_parts) > 0:
                return date_parts[0]
                
        # Try created date if publication date is not available
        if 'message' in result and 'created' in result['message']:
            date_parts = result['message']['created']['date-parts'][0]
            if len(date_parts) > 0:
                return date_parts[0]
        
        # Alternative: try direct content negotiation if API fails
        headers = {"Accept": "application/json"}
        r = requests.get(f"https://doi.org/{doi}", headers=headers)
        if r.status_code == 200 and 'published-print' in r.json():
            date_parts = r.json()['published-print']['date-parts'][0]
            if len(date_parts) > 0:
                return date_parts[0]
                
        return None
    except Exception as e:
        print(f"Error retrieving data for DOI {doi}: {str(e)}")
        return None

# Process each DOI
for index, row in df.iterrows():
    if pd.notna(row['DOI']):
        print(f"Processing {index+1}/{len(df)}: DOI {row['DOI']}")
        year = get_publication_year(row['DOI'])
        df.at[index, 'Year'] = year
        # Add a small delay to avoid hitting API rate limits
        time.sleep(1)
    else:
        print(f"Skipping row {index+1} (no DOI)")

# Save the updated CSV
df.to_csv('YueGuangxi_Papers/Sheet1_Deduplicated_with_years.csv', index=False)
print("Finished adding publication years to dataset.")
```

## How This Script Works

1. The script uses the `habanero` library to access the Crossref API, which is a standard way to retrieve metadata for DOIs.

2. For each DOI in your dataset, it:
   - Queries the Crossref API for publication metadata
   - Extracts the publication year from the response
   - Updates the dataset with this information
   - Adds a small delay between requests to avoid hitting API rate limits

3. The script includes fallback mechanisms to handle different date formats in the API response, maximizing the chance of finding a publication year.

4. The updated dataset is saved as a new CSV file with the year information included.

## Installation Requirements

Before running this script, you'll need to install the required libraries:

```
pip install pandas habanero requests
```

## Note on API Usage

- The script includes a `mailto` parameter in the Crossref client initialization. It's good practice to include your email when using their API, as it can help with troubleshooting and provides better rate limits.

- If you encounter rate limiting issues, you might need to increase the sleep time between requests.

This approach should work for most DOIs in your dataset, although some publishers might have incomplete or differently structured metadata.

---

_**User**_

@Sheet1_Deduplicated_with_years.csv 修改代码，重命名后的文件名为year+"_"+file_name

---

_**Assistant**_

<think>
The user wants to modify the existing file naming format in the PDF renaming script so that the file names include the publication year as a prefix. Let's look at the files we have:

1. `Sheet1_Deduplicated_with_years.csv` - this is the CSV file containing paper information including titles, DOIs, and now publication years.
2. `rename_pdfs.py` - this script renames PDF files based on the CSV data.

What the user wants is to modify the `rename_pdfs.py` script to use the format `year_file_name.pdf` as the new naming convention, instead of just using `file_name.pdf`.

Let's examine the current `rename_pdfs.py` code to understand how it works and what needs to be modified:

The current script works as follows:
1. Reads the CSV file with paper information
2. Creates a mapping from paper numbers (序号) to file names
3. For each PDF file in the source folder, it tries to extract a numeric part from the filename
4. If a match is found in the mapping, it renames the file to the corresponding name from the CSV

To implement the user's request, we need to modify the part where the new filename is constructed. Instead of just using the `file_name` from the CSV, we need to prepend the year information.

The key part to change is around this area in the original code:
```python
if new_name:
    new_name = sanitize_filename(new_name)
    
    # Ensure new file name ends with .pdf
    if not new_name.lower().endswith('.pdf'):
        new_name += '.pdf'
```

We need to modify the code to include the year in the format `year_file_name.pdf`. We'll need to access the "Year" column in the CSV and use it as part of the filename.
</think>

# Modified PDF Renaming Script to Include Publication Year

I'll modify the `rename_pdfs.py` script to include the publication year as a prefix in the filename format, following the pattern: `year_file_name.pdf`.

Here's the implementation:

```python:YueGuangxi_Papers/rename_pdfs_with_year.py
import os
import pandas as pd
import re
import shutil
from pathlib import Path

def sanitize_filename(name):
    """清理文件名中的非法字符"""
    # 替换Windows文件名中不允许的字符
    illegal_chars = r'[<>:"/\\|?*]'
    name = re.sub(illegal_chars, '_', name)
    # 移除前后空格
    name = name.strip()
    # 确保文件名不为空
    if not name:
        name = "unnamed_file"
    return name[:240]  # 确保文件名不超过Windows最大长度限制

def get_numeric_part(filename):
    """提取文件名中的数字部分"""
    match = re.search(r'\d+', filename)
    if match:
        return match.group(0)
    return None

def rename_pdfs():
    """根据Excel文件中的映射关系重命名PDF文件，使用年份_文件名的格式"""
    
    # 设置路径
    pdf_folder = "paper_0321_3"
    excel_file = "Sheet1_Deduplicated_with_years.csv"
    
    # 确保文件夹存在
    if not os.path.exists(pdf_folder):
        print(f"错误: 文件夹 {pdf_folder} 不存在")
        return
    
    # 确保Excel文件存在
    if not os.path.exists(excel_file):
        print(f"错误: Excel文件 {excel_file} 不存在")
        return
    
    # 读取Excel文件
    try:
        df = pd.read_csv(excel_file)
        print(f"成功读取Excel文件，共有 {len(df)} 行数据")
    except Exception as e:
        print(f"读取Excel文件时出错: {str(e)}")
        return
    
    # 确保存在所需的列
    required_columns = ['序号', 'file_name', '

---



---

