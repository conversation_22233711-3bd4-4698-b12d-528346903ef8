{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 插入paper_info信息\n", "PMID\tTitle\tAuthor\t<PERSON><PERSON>\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["读取到 113 行有效数据\n", "删除已存在的表: paperInfo\n", "准备插入 113 条记录到数据库\n", "数据插入成功！\n"]}], "source": ["!python paper_info_import.py"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 插入Project_info信息\n", "Project ID\tBioProject ID Tissue\tTissue Category\t     Cell Type\tCelI line\tHealthy Condition\tDisease Category  PMID<br>\n", "-- 在所有索引列中搜索含有\"lung cancer\"的记录\n", "SELECT * FROM project_info \n", "WHERE MATCH(`Project ID`, `BioProject ID`, `Tissue`, `Tissue Category`, \n", "           `Cell Type`, `Cell line`, `Healthy Condition`, `Disease Category`) \n", "AGAINST('lung cancer' IN NATURAL LANGUAGE MODE);\n", "\n", "-- 搜索包含\"HEK293\"的所有记录\n", "SELECT * FROM project_info \n", "WHERE MATCH(`Project ID`, `BioProject ID`, `Tissue`, `Tissue Category`, \n", "           `Cell Type`, `Cell line`, `Healthy Condition`, `Disease Category`) \n", "AGAINST('HEK293' IN BOOLEAN MODE);"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["项目信息表创建成功！\n", "导入前表中有 0 条记录\n", "CSV 文件头: \"Project ID\",\"BioProject ID\",Title,Strategy,Tissue,\"Tissue Category\",\"Cell Type\",\"Cell line\",\"Healthy Condition\",\"Disease Category\",\"Run Number\",PMID,Detail,\"Release Date\",\"Submission Date\",\"Update Date\"\n", "CSV文件总行数: 127，跳过行数: 0，有效行数: 127\n", "成功导入 127 条项目数据\n", "导入前表中有 0 条记录，导入后有 127 条记录，净增加 127 条\n", "创建索引 idx_title 失败: 1071 (42000): Specified key was too long; max key length is 3072 bytes\n", "创建索引 idx_tissue 成功\n", "创建索引 idx_cell_type 成功\n", "创建索引 idx_cell_line 成功\n", "创建索引 idx_pmid 成功\n", "索引创建完成\n", "完成数据导入和索引创建！\n"]}], "source": ["!python Project_info_import.py"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 插入Project_info信息\n", "SRA Accession\tProject ID\tBioProject ID\tBioSample ID\tTissue\tCell Type\tCelI line\tHealthy Condition\tStrategy\tPlatform\tInstrument\tLibraryLayout"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["样本信息表创建成功！\n", "成功导入 1621 / 1621 条样本数据\n", "创建索引 idx_project 成功\n", "创建索引 idx_bioproject 成功\n", "创建索引 idx_tissue 成功\n", "创建索引 idx_cell_type 成功\n", "创建索引 idx_cell_line 成功\n", "创建索引 idx_strategy 成功\n", "索引创建完成\n", "完成样本数据导入和索引创建！\n"]}], "source": ["!python Sample_info_import.py"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing with gene ID: ENSG00000092607\n", "Found result: TBX15 (ID: 6913). Clicking to view details...\n", "{\n", "  \"gene_id\": \"ENSG00000092607\",\n", "  \"ncbi_gene_id\": \"6913\",\n", "  \"Official Symbol\": \"TBX15\",\n", "  \"Official Full Name\": \"T-box transcription factor 15\",\n", "  \"Primary source\": \"HGNC:HGNC:11594\",\n", "  \"See related\": \"Ensembl:ENSG00000092607; MIM:604127;; AllianceGenome:HGNC:11594\",\n", "  \"Gene type\": \"protein coding\",\n", "  \"RefSeq status\": \"REVIEWED\",\n", "  \"Organism\": \"Homo sapiens\",\n", "  \"Lineage\": \"Eukaryota; Metazoa; Chordata; Craniata; Vertebrata; Euteleostomi; Mammalia; Eutheria; Euarchontoglires; Primates; Haplorrhini; Catarrhini; Hominidae; Homo\",\n", "  \"Also known as\": \"TBX14\",\n", "  \"Summary\": \"This gene belongs to the T-box family of genes, which encode a phylogenetically conserved family of transcription factors that regulate a variety of developmental processes. All these genes contain a common T-box DNA-binding domain. Mutations in this gene are associated with <PERSON><PERSON><PERSON> syndrome.[provided by RefSeq, Oct 2009]\",\n", "  \"Location\": \"1p12\"\n", "}\n", "\n", "Starting processing of missing gene IDs from gene_info_results_missing.csv\n", "\n", "Error: [Errno 2] No such file or directory: 'gene_info_results_missing.csv'\n", "Progress has been saved in the checkpoint file.\n"]}], "source": ["!python gene_info/gene_info_batch_scraper_enhanced.py"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Full response: ChatCompletion(id=None, choices=None, created=None, model=None, object=None, service_tier=None, system_fingerprint=None, usage=None, error={'message': 'Provider returned error', 'code': 429, 'metadata': {'raw': '{\\n  \"error\": {\\n    \"code\": 429,\\n    \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits.\",\\n    \"status\": \"RESOURCE_EXHAUSTED\",\\n    \"details\": [\\n      {\\n        \"@type\": \"type.googleapis.com/google.rpc.QuotaFailure\",\\n        \"violations\": [\\n          {\\n            \"quotaMetric\": \"generativelanguage.googleapis.com/generate_content_paid_tier_input_token_count\",\\n            \"quotaId\": \"GenerateContentPaidTierInputTokensPerModelPerMinute\",\\n            \"quotaDimensions\": {\\n              \"location\": \"global\",\\n              \"model\": \"gemini-2.0-pro-exp\"\\n            },\\n            \"quotaValue\": \"10000000\"\\n          }\\n        ]\\n      },\\n      {\\n        \"@type\": \"type.googleapis.com/google.rpc.Help\",\\n        \"links\": [\\n          {\\n            \"description\": \"Learn more about Gemini API quotas\",\\n            \"url\": \"https://ai.google.dev/gemini-api/docs/rate-limits\"\\n          }\\n        ]\\n      },\\n      {\\n        \"@type\": \"type.googleapis.com/google.rpc.RetryInfo\",\\n        \"retryDelay\": \"48s\"\\n      }\\n    ]\\n  }\\n}\\n', 'provider_name': 'Google AI Studio'}}, user_id='user_2py8MEG44WcTGnM2Q23vkGd4hed')\n", "No choices found in the response. Check the full response above.\n"]}], "source": ["from openai import OpenAI\n", "\n", "client = OpenAI(\n", "  base_url=\"https://openrouter.ai/api/v1\",\n", "  api_key=\"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\",\n", ")\n", "\n", "try:\n", "    completion = client.chat.completions.create(\n", "        extra_headers={\n", "            \"HTTP-Referer\": \"https://your-actual-site.com\",  # Replace with your actual site URL\n", "            \"X-Title\": \"My Application\",  # Replace with your actual application name\n", "        },\n", "        extra_body={},\n", "        model=\"google/gemini-2.5-pro-exp-03-25:free\",\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": [\n", "                    {\n", "                        \"type\": \"text\",\n", "                        \"text\": \"What is in this image?\"\n", "                    },\n", "                    {\n", "                        \"type\": \"image_url\",\n", "                        \"image_url\": {\n", "                            \"url\": \"https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg\"\n", "                        }\n", "                    }\n", "                ]\n", "            }\n", "        ]\n", "    )\n", "     \n", "    # First print the entire response to inspect its structure\n", "    print(\"Full response:\", completion)\n", "    \n", "    # Then try to access the content with proper error handling\n", "    if hasattr(completion, 'choices') and completion.choices:\n", "        print(completion.choices[0].message.content)\n", "    else:\n", "        print(\"No choices found in the response. Check the full response above.\")\n", "        \n", "except Exception as e:\n", "    print(f\"Error occurred: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "torch-gpuprivate", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 2}