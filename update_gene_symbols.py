#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基因符号更新脚本
通过GENE ID匹配更新translation_indices_results_grouped_filtered_processed.csv中的GENE symbol值
"""

import pandas as pd
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_gene_symbols.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def update_gene_symbols():
    """更新基因符号"""
    try:
        # 读取基因信息文件
        logging.info("正在读取基因信息文件...")
        gene_info_df = pd.read_csv('gene_info/ensembl_gene_id_with_full_info_final.csv')
        logging.info(f"基因信息文件读取完成，共 {len(gene_info_df)} 行")
        
        # 创建基因ID到基因符号的映射字典
        gene_id_to_symbol = {}
        for index, row in gene_info_df.iterrows():
            gene_id = str(row['GENE ID']).strip()
            gene_symbol = str(row['GENE symbol']).strip()
            if gene_id and gene_symbol and gene_symbol != 'nan':
                gene_id_to_symbol[gene_id] = gene_symbol
        
        logging.info(f"创建了 {len(gene_id_to_symbol)} 个基因ID到符号的映射")
        
        # 读取需要更新的文件
        logging.info("正在读取翻译指数文件...")
        translation_df = pd.read_csv('translation_indices_results_grouped_filtered_processed.csv')
        logging.info(f"翻译指数文件读取完成，共 {len(translation_df)} 行")
        
        # 统计更新情况
        updated_count = 0
        not_found_count = 0
        already_correct_count = 0
        
        # 更新GENE symbol
        logging.info("开始更新基因符号...")
        for index, row in translation_df.iterrows():
            gene_id = str(row['GENE ID']).strip()
            current_symbol = str(row['GENE symbol']).strip()
            
            if gene_id in gene_id_to_symbol:
                new_symbol = gene_id_to_symbol[gene_id]
                if current_symbol != new_symbol:
                    translation_df.at[index, 'GENE symbol'] = new_symbol
                    updated_count += 1
                    if updated_count % 1000 == 0:
                        logging.info(f"已更新 {updated_count} 个基因符号...")
                else:
                    already_correct_count += 1
            else:
                # 未匹配到基因ID，填充为 'NA'
                translation_df.at[index, 'GENE symbol'] = 'NA'
                not_found_count += 1
                if not_found_count <= 10:  # 只记录前10个未找到的
                    logging.warning(f"未找到基因ID {gene_id} 对应的符号，已填充为 NA")
        
        # 保存更新后的文件
        output_file = 'translation_indices_results_grouped_filtered_processed_updated.csv'
        logging.info(f"正在保存更新后的文件到 {output_file}...")
        translation_df.to_csv(output_file, index=False)
        
        # 输出统计信息
        logging.info("=" * 60)
        logging.info("更新完成！统计信息：")
        logging.info(f"总行数: {len(translation_df)}")
        logging.info(f"更新的符号数: {updated_count}")
        logging.info(f"已正确的符号数: {already_correct_count}")
        logging.info(f"未找到匹配的基因ID数: {not_found_count}")
        logging.info(f"更新后的文件保存为: {output_file}")
        logging.info("=" * 60)
        
        return True
        
    except Exception as e:
        logging.error(f"处理过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始更新基因符号...")
    success = update_gene_symbols()
    if success:
        print("基因符号更新完成！")
    else:
        print("基因符号更新失败，请查看日志文件。") 