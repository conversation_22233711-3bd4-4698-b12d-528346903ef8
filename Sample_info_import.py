import pandas as pd
import mysql.connector
import csv
from datetime import datetime
import os

# 数据库配置信息
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def main():
    # 连接 MySQL 数据库
    conn = mysql.connector.connect(**DB_CONFIG)
    cursor = conn.cursor()
    
    # 处理 Browse_Sample.csv 文件
    create_sample_table(cursor)
    import_sample_data(cursor)
    create_indexes(cursor)
    
    # 提交更改并关闭连接
    conn.commit()
    cursor.close()
    conn.close()
    print("完成样本数据导入和索引创建！")

def create_sample_table(cursor):
    """创建样本信息表"""
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS sample_info (
        `SRA Accession` VARCHAR(50) PRIMARY KEY,
        `Project ID` VARCHAR(50),
        `BioProject ID` VARCHAR(50),
        `BioSample ID` VARCHAR(50),
        `Tissue` VARCHAR(255),
        `Cell Type` VARCHAR(255),
        `CelI line` VARCHAR(255),
        `Healthy Condition` VARCHAR(255),
        `Strategy` VARCHAR(255),
        `Platform` VARCHAR(255),
        `Instrument` VARCHAR(255),
        `LibraryLayout` VARCHAR(50)
    )
    """
    cursor.execute(create_table_sql)
    
    # 清空表以防止重复导入
    try:
        cursor.execute("TRUNCATE TABLE sample_info")
        print("已清空原有数据表，准备重新导入")
    except mysql.connector.Error as err:
        print(f"清空表时出错: {err}")
    
    print("样本信息表创建成功！")

def import_sample_data(cursor):
    """导入样本数据"""
    # CSV文件路径
    csv_path = 'Browse-Sample/Browse_Sample.csv'
    
    # 准备SQL语句 - 使用ON DUPLICATE KEY UPDATE防止重复主键问题
    insert_sql = """
    INSERT INTO sample_info 
    (`SRA Accession`, `Project ID`, `BioProject ID`, `BioSample ID`, 
     `Tissue`, `Cell Type`, `CelI line`, `Healthy Condition`, 
     `Strategy`, `Platform`, `Instrument`, `LibraryLayout`)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE 
        `Project ID` = VALUES(`Project ID`),
        `BioProject ID` = VALUES(`BioProject ID`),
        `BioSample ID` = VALUES(`BioSample ID`),
        `Tissue` = VALUES(`Tissue`),
        `Cell Type` = VALUES(`Cell Type`),
        `CelI line` = VALUES(`CelI line`),
        `Healthy Condition` = VALUES(`Healthy Condition`),
        `Strategy` = VALUES(`Strategy`),
        `Platform` = VALUES(`Platform`),
        `Instrument` = VALUES(`Instrument`),
        `LibraryLayout` = VALUES(`LibraryLayout`)
    """
    
    # 读取CSV并处理数据
    samples = []
    row_count = 0
    valid_count = 0
    
    with open(csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            row_count += 1
            
            # 跳过空行或SRA Accession为空的行
            if not row or not row.get('SRA Accession') or row.get('SRA Accession') == 'NA':
                print(f"跳过第 {row_count} 行: SRA Accession为空或NA")
                continue
                
            # 处理空值和特殊值
            sample_data = (
                row['SRA Accession'] if row['SRA Accession'] != 'NA' else None,
                row['Project ID'] if row['Project ID'] != 'NA' else None,
                row['BioProject ID'] if row['BioProject ID'] != 'NA' else None,
                row['BioSample ID'] if row['BioSample ID'] != 'NA' else None,
                row['Tissue'] if row['Tissue'] != 'NA' else None,
                row['Cell Type'] if row['Cell Type'] != 'NA' else None,
                row['CelI line'] if row['CelI line'] != 'NA' else None,
                row['Healthy Condition'] if row['Healthy Condition'] != 'NA' else None,
                row['Strategy'] if row['Strategy'] != 'NA' else None,
                row['Platform'] if row['Platform'] != 'NA' else None,
                row['Instrument'] if row['Instrument'] != 'NA' else None,
                row['LibraryLayout'] if row['LibraryLayout'] != 'NA' else None
            )
            samples.append(sample_data)
            valid_count += 1
    
    print(f"CSV文件共读取 {row_count} 行，有效数据 {valid_count} 行")
    
    # 批量插入数据
    try:
        cursor.executemany(insert_sql, samples)
        print(f"成功导入 {len(samples)} 条样本数据")
    except mysql.connector.Error as err:
        print(f"导入数据时出错: {err}")
        # 单条插入以确定哪些记录有问题
        successful = 0
        for i, sample_data in enumerate(samples):
            try:
                cursor.execute(insert_sql, sample_data)
                successful += 1
            except mysql.connector.Error as err:
                print(f"无法导入第 {i+1} 行数据: {err}")
        print(f"成功导入 {successful} / {len(samples)} 条记录")

def create_indexes(cursor):
    """为指定列创建索引，优化模糊匹配搜索"""
    
    # 为所有列创建FULLTEXT索引，支持模糊匹配
    search_columns = [
        'SRA Accession', 'Project ID', 'BioProject ID', 'BioSample ID', 
        'Tissue', 'Cell Type', 'CelI line', 'Healthy Condition',
        'Strategy', 'Platform', 'Instrument', 'LibraryLayout'
    ]
    
    # 创建联合FULLTEXT索引来支持在任何一列找到匹配项
    fulltext_index_sql = f"""
    CREATE FULLTEXT INDEX ft_sample_search 
    ON sample_info ({', '.join([f'`{col}`' for col in search_columns])})
    """
    try:
        cursor.execute(fulltext_index_sql)
        print("创建全文搜索索引成功，支持模糊匹配以下列：")
        for col in search_columns:
            print(f"- {col}")
    except mysql.connector.Error as err:
        print(f"创建全文搜索索引失败: {err}")
    
    # 主键索引已在表创建时添加，不需要再尝试添加
    print("SRA Accession已设置为主键")
    
if __name__ == '__main__':
    main() 