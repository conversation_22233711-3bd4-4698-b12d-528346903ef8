{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Quick Start\n", "## Evaluation Quick Start\n", "This quick start will get you up and running with our evaluation SDK and experiments UI.\n", "此快速入门将帮助您启动并运行我们的评估 SDK 和实验 UI。\n", "\n", "### 1. Install Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install -U langsmith openai pydantic"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. Create an API key\n", "To create an API key head to the Settings page. Then click Create API Key.\n", "### 3. Set up your \n", "### 4. Import dependencies"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["\n", "from langsmith import wrappers, Client\n", "from pydantic import BaseModel, Field\n", "from openai import OpenAI\n", "import os\n", "\n", "# 方法1：设置为环境变量\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-MJvRwaS8HbXDWqrPA7692eBeC1454448A0661eF96b482c42\"\n", "os.environ[\"OPENAI_API_BASE\"] = \"https://api.yesapikey.com/v1\"  # 如果需要自定义API基础URL\n", "os.environ[\"LANGSMITH_API_KEY\"] = \"***************************************************\"\n", "os.environ[\"LANGSMITH_TRACING\"] = \"true\"\n", "\n", "# client = Client(api_key=\"***************************************************\")\n", "client = Client()\n", "openai_client = wrappers.wrap_openai(OpenAI())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5. Create a dataset"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"ename": "LangSmithConflictError", "evalue": "Conflict for /datasets. HTTPError('409 Client Error: Conflict for url: https://api.smith.langchain.com/datasets', '{\"detail\":\"Dataset with this name already exists.\"}')", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mHTTPError\u001b[0m                                 Traceback (most recent call last)", "File \u001b[0;32m~/anaconda3/envs/pyani_env/lib/python3.8/site-packages/langsmith/utils.py:150\u001b[0m, in \u001b[0;36mraise_for_status_with_text\u001b[0;34m(response)\u001b[0m\n\u001b[1;32m    149\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 150\u001b[0m     \u001b[43mresponse\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraise_for_status\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    151\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m requests\u001b[38;5;241m.\u001b[39mHTTPError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/anaconda3/envs/pyani_env/lib/python3.8/site-packages/requests/models.py:1024\u001b[0m, in \u001b[0;36mResponse.raise_for_status\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1023\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m http_error_msg:\n\u001b[0;32m-> 1024\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m HTTPError(http_error_msg, response\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m)\n", "\u001b[0;31mHTTPError\u001b[0m: 409 Client Error: Conflict for url: https://api.smith.langchain.com/datasets", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mHTTPError\u001b[0m                                 Traceback (most recent call last)", "File \u001b[0;32m~/anaconda3/envs/pyani_env/lib/python3.8/site-packages/langsmith/client.py:763\u001b[0m, in \u001b[0;36mClient.request_with_retries\u001b[0;34m(self, method, pathname, request_kwargs, stop_after_attempt, retry_on, to_ignore, handle_response, _context, **kwargs)\u001b[0m\n\u001b[1;32m    753\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msession\u001b[38;5;241m.\u001b[39mrequest(\n\u001b[1;32m    754\u001b[0m         method,\n\u001b[1;32m    755\u001b[0m         (\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    761\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mrequest_kwargs,\n\u001b[1;32m    762\u001b[0m     )\n\u001b[0;32m--> 763\u001b[0m \u001b[43mls_utils\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mraise_for_status_with_text\u001b[49m\u001b[43m(\u001b[49m\u001b[43mresponse\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    764\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "File \u001b[0;32m~/anaconda3/envs/pyani_env/lib/python3.8/site-packages/langsmith/utils.py:152\u001b[0m, in \u001b[0;36mraise_for_status_with_text\u001b[0;34m(response)\u001b[0m\n\u001b[1;32m    151\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m requests\u001b[38;5;241m.\u001b[39mHTTPError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m--> 152\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m requests\u001b[38;5;241m.\u001b[39mHTTPError(\u001b[38;5;28mstr\u001b[39m(e), response\u001b[38;5;241m.\u001b[39mtext) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01me\u001b[39;00m\n", "\u001b[0;31mHTTPError\u001b[0m: [Errno 409 Client Error: Conflict for url: https://api.smith.langchain.com/datasets] {\"detail\":\"Dataset with this name already exists.\"}", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mLangSmithConflictError\u001b[0m                    <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[15], line 7\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# For other dataset creation methods, see:\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;66;03m# https://docs.smith.langchain.com/evaluation/how_to_guides/manage_datasets_programmatically\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# https://docs.smith.langchain.com/evaluation/how_to_guides/manage_datasets_in_application\u001b[39;00m\n\u001b[1;32m      4\u001b[0m \n\u001b[1;32m      5\u001b[0m \n\u001b[1;32m      6\u001b[0m \u001b[38;5;66;03m# Programmatically create a dataset in LangSmith\u001b[39;00m\n\u001b[0;32m----> 7\u001b[0m dataset \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_dataset\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      8\u001b[0m \u001b[43m  \u001b[49m\u001b[43mdataset_name\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mSample dataset\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdescription\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mA sample dataset in LangSmith.\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\n\u001b[1;32m      9\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m     11\u001b[0m \u001b[38;5;66;03m# Create examples\u001b[39;00m\n\u001b[1;32m     12\u001b[0m examples \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m     13\u001b[0m   {\n\u001b[1;32m     14\u001b[0m       \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minputs\u001b[39m\u001b[38;5;124m\"\u001b[39m: {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mquestion\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mWhich country is Mount Kilimanjaro located in?\u001b[39m\u001b[38;5;124m\"\u001b[39m},\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     20\u001b[0m   },\n\u001b[1;32m     21\u001b[0m ]\n", "File \u001b[0;32m~/anaconda3/envs/pyani_env/lib/python3.8/site-packages/langsmith/client.py:2817\u001b[0m, in \u001b[0;36mClient.create_dataset\u001b[0;34m(self, dataset_name, description, data_type, inputs_schema, outputs_schema, metadata)\u001b[0m\n\u001b[1;32m   2814\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m outputs_schema \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   2815\u001b[0m     dataset[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124moutputs_schema_definition\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m outputs_schema\n\u001b[0;32m-> 2817\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest_with_retries\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   2818\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mPOST\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2819\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/datasets\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2820\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m{\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_headers\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mContent-Type\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mapplication/json\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m}\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2821\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m_orjson\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdumps\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdataset\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   2822\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2823\u001b[0m ls_utils\u001b[38;5;241m.\u001b[39mraise_for_status_with_text(response)\n\u001b[1;32m   2825\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m ls_schemas\u001b[38;5;241m.\u001b[39mDataset(\n\u001b[1;32m   2826\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mresponse\u001b[38;5;241m.\u001b[39mjson(),\n\u001b[1;32m   2827\u001b[0m     _host_url\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_host_url,\n\u001b[1;32m   2828\u001b[0m     _tenant_id\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_get_optional_tenant_id(),\n\u001b[1;32m   2829\u001b[0m )\n", "File \u001b[0;32m~/anaconda3/envs/pyani_env/lib/python3.8/site-packages/langsmith/client.py:808\u001b[0m, in \u001b[0;36mClient.request_with_retries\u001b[0;34m(self, method, pathname, request_kwargs, stop_after_attempt, retry_on, to_ignore, handle_response, _context, **kwargs)\u001b[0m\n\u001b[1;32m    803\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ls_utils\u001b[38;5;241m.\u001b[39mLangSmithNotFoundError(\n\u001b[1;32m    804\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mResource not found for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpathname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m. \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mrepr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    805\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m_context\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    806\u001b[0m     )\n\u001b[1;32m    807\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m response\u001b[38;5;241m.\u001b[39mstatus_code \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m409\u001b[39m:\n\u001b[0;32m--> 808\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ls_utils\u001b[38;5;241m.\u001b[39mLangSmithConflictError(\n\u001b[1;32m    809\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConflict for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpathname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m. \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mrepr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m_context\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    810\u001b[0m     )\n\u001b[1;32m    811\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    812\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ls_utils\u001b[38;5;241m.\u001b[39mLangSmithError(\n\u001b[1;32m    813\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmethod\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpathname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m in LangSmith\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    814\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m API. \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mrepr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    815\u001b[0m     )\n", "\u001b[0;31mLangSmithConflictError\u001b[0m: Conflict for /datasets. HTTPError('409 Client Error: Conflict for url: https://api.smith.langchain.com/datasets', '{\"detail\":\"Dataset with this name already exists.\"}')"]}], "source": ["# For other dataset creation methods, see:\n", "# https://docs.smith.langchain.com/evaluation/how_to_guides/manage_datasets_programmatically\n", "# https://docs.smith.langchain.com/evaluation/how_to_guides/manage_datasets_in_application\n", "\n", "\n", "# Programmatically create a dataset in LangSmith\n", "dataset = client.create_dataset(\n", "  dataset_name=\"<PERSON>ple dataset\", description=\"A sample dataset in LangSmith.\"\n", ")\n", "\n", "# Create examples\n", "examples = [\n", "  {\n", "      \"inputs\": {\"question\": \"Which country is Mount Kilimanjaro located in?\"},\n", "      \"outputs\": {\"answer\": \"Mount Kilimanjaro is located in Tanzania.\"},\n", "  },\n", "  {\n", "      \"inputs\": {\"question\": \"What is Earth's lowest point?\"},\n", "      \"outputs\": {\"answer\": \"Earth's lowest point is The Dead Sea.\"},\n", "  },\n", "]\n", "\n", "# Add examples to the dataset\n", "for example in examples:\n", "    client.create_example(\n", "        dataset_id=dataset.id,\n", "        inputs=example[\"inputs\"],\n", "        outputs=example[\"outputs\"]\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.Define what you're evaluating"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["# Define the application logic you want to evaluate inside a target function\n", "# The SDK will automatically send the inputs from the dataset to your target function\n", "def target(inputs: dict) -> dict:\n", "    local_client = OpenAI(\n", "        api_key=\"sk-MJvRwaS8HbXDWqrPA7692eBeC1454448A0661eF96b482c42\",\n", "        base_url=\"https://api.yesapikey.com/v1\"\n", "    )\n", "    response = local_client.chat.completions.create(\n", "        model=\"gpt-4o-mini\",\n", "        messages=[\n", "            {\"role\":\"system\",\"content\":\"Answer the follwing question accurately\"},\n", "            {\"role\":\"user\",\"content\":inputs[\"question\"]},\n", "        ]\n", "    )\n", "    return {\"response\": response.choices[0].message.content.strip()}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 7.Define evaluator"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["# Define instructions for the LLM judge evaluator\n", "instructions = \"\"\"Evaluate Student Answer against Ground Truth for conceptual similarity and classify true or false: \n", "- False: No conceptual match and similarity\n", "- True: Most or full conceptual match and similarity\n", "- Key criteria: Concept should match, not exact wording.\n", "\"\"\"\n", "\n", "# Define output schema for the LLM judge\n", "class Grade(BaseModel):\n", "    score: bool = Field(\n", "        description=\"Boolean that indicates whether the response is accurate relative to the reference answer\"\n", "    )\n", "\n", "# Define LLM judge the grades the accuracy of the response relative to reference output\n", "def accuracy(outputs: dict, reference_outputs: dict) -> bool:\n", "    local_client = OpenAI(\n", "        api_key=\"sk-MJvRwaS8HbXDWqrPA7692eBeC1454448A0661eF96b482c42\",\n", "        base_url=\"https://api.yesapikey.com/v1\"\n", "    )\n", "    response = local_client.beta.chat.completions.parse(\n", "        model=\"gpt-4o-mini\",\n", "        messages=[\n", "            {\"role\":\"system\", \"content\":instructions},\n", "            {\n", "                \"role\":\"user\",\n", "                \"content\":f\"\"\"Ground Truth answer: {reference_outputs[\"answer\"]};\n", "                Student's Answer:{outputs[\"response\"]}\"\"\"\n", "            },\n", "        ],\n", "        response_format=Grade\n", "    )\n", "    return response.choices[0].message.parsed.score\n", "    "]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["View the evaluation results for project 'pertinent-scale-43' at:\n", "https://smith.langchain.com/o/1148017e-b480-513c-bbfc-22947a0fbdc6/datasets/650a6ab4-d896-4e6a-8c2a-3db31064f6a3/compare?selectedSessions=b371d847-fba8-4952-bbd1-125db27e55ae\n", "\n", "View all tests for Dataset Sample dataset at:\n", "https://smith.langchain.com/o/1148017e-b480-513c-bbfc-22947a0fbdc6/datasets/650a6ab4-d896-4e6a-8c2a-3db31064f6a3\n", "[------------------------------------------------->] 2/2"]}], "source": ["\n", "from langchain.evaluation import EvaluatorType\n", "from langchain.smith import RunEvalConfig\n", "\n", "# 创建正确的评估配置\n", "eval_config = RunEvalConfig(\n", "    evaluators=[\n", "        # 使用您的自定义评估器\n", "        accuracy,\n", "        # 或使用内置评估器\n", "        # EvaluatorType.QA,\n", "        # EvaluatorType.CORRECTNESS\n", "    ],\n", "    # 其他设置\n", "    custom_name=\"first-eval-in-langsmith\",  # 替代 experiment_prefix\n", ")\n", "\n", "# 使用正确的参数结构\n", "experiment_results = client.run_on_dataset(\n", "    dataset_name=\"Sample dataset\",\n", "    llm_or_chain_factory=target,\n", "    evaluation=eval_config,  # 传入评估配置对象，而不是函数\n", "    concurrency_level=2\n", ")"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting beautifulsoup4\n", "  Downloading beautifulsoup4-4.13.3-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting soupsieve>1.2 (from beautifulsoup4)\n", "  Downloading soupsieve-2.6-py3-none-any.whl.metadata (4.6 kB)\n", "Requirement already satisfied: typing-extensions>=4.0.0 in /Users/<USER>/anaconda3/envs/pyani_env/lib/python3.8/site-packages (from beautifulsoup4) (4.12.2)\n", "Downloading beautifulsoup4-4.13.3-py3-none-any.whl (186 kB)\n", "Downloading soupsieve-2.6-py3-none-any.whl (36 kB)\n", "Installing collected packages: soupsieve, beautifulsoup4\n", "Successfully installed beautifulsoup4-4.13.3 soupsieve-2.6\n"]}], "source": ["!pip install beautifulsoup4"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"error\": \"name 'extract_author_affiliations' is not defined\"\n", "}\n"]}], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "import re\n", "import json\n", "\n", "def get_complete_pubmed_info(pmid):\n", "    \"\"\"获取PubMed文章的完整信息，包括标题、作者、DOI、通讯作者和摘要\"\"\"\n", "    url = f\"https://pubmed.ncbi.nlm.nih.gov/{pmid}/\"\n", "    headers = {\n", "        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n", "    }\n", "    \n", "    try:\n", "        response = requests.get(url, headers=headers)\n", "        response.raise_for_status()\n", "        soup = BeautifulSoup(response.text, 'html.parser')\n", "        \n", "        # 提取标题\n", "        title = \"\"\n", "        title_tag = soup.find('h1', class_='heading-title')\n", "        if title_tag:\n", "            title = title_tag.text.strip()\n", "        \n", "        # 提取DOI\n", "        doi = \"\"\n", "        doi_tag = soup.find('a', href=lambda x: x and x.startswith('https://doi.org/'))\n", "        if doi_tag:\n", "            doi = doi_tag.text.strip()\n", "        \n", "        # 提取摘要\n", "        abstract = \"\"\n", "        abstract_div = soup.find('div', id='abstract')\n", "        if abstract_div:\n", "            abstract_text = abstract_div.find('div', class_='abstract-content')\n", "            if abstract_text:\n", "                abstract = abstract_text.text.strip()\n", "        \n", "        # 提取作者和单位信息\n", "        authors_info = extract_author_affiliations(response.text)\n", "        \n", "        # 获取所有作者姓名列表\n", "        authors = []\n", "        for author in authors_info.get('authors', []):\n", "            authors.append(author['name'])\n", "        \n", "        # 找出通讯作者\n", "        corresponding_author = \"\"\n", "        corresponding_info = \"\"\n", "        \n", "        # 寻找标记为通讯作者的作者\n", "        corresponding_tag = soup.find('a', attrs={'class': 'corresp-author-link'})\n", "        if corresponding_tag:\n", "            parent_tag = corresponding_tag.find_parent('span', class_='authors-list-item')\n", "            if parent_tag:\n", "                author_name_tag = parent_tag.find('a', class_='full-name')\n", "                if author_name_tag:\n", "                    corresponding_author = author_name_tag.text.strip()\n", "                    \n", "                    # 获取该作者的单位信息\n", "                    for author in authors_info.get('authors', []):\n", "                        if author['name'] == corresponding_author:\n", "                            affiliations = [aff['text'] for aff in author['affiliations']]\n", "                            corresponding_info = \"; \".join(affiliations)\n", "                            break\n", "        \n", "        # 获取所有单位信息的完整列表\n", "        all_affiliations = []\n", "        for affiliation in authors_info.get('affiliations', []):\n", "            all_affiliations.append(f\"{affiliation['number']}: {affiliation['text']}\")\n", "        affiliations_text = \"; \".join(all_affiliations)\n", "        \n", "        # 获取作者与单位的完整对应关系\n", "        author_affiliations = []\n", "        for author in authors_info.get('authors', []):\n", "            author_name = author['name']\n", "            author_affs = []\n", "            for aff in author['affiliations']:\n", "                author_affs.append(aff['text'])\n", "            if author_affs:\n", "                author_affiliations.append(f\"{author_name}: {', '.join(author_affs)}\")\n", "        author_affiliations_text = \"; \".join(author_affiliations)\n", "        \n", "        return {\n", "            \"Title\": title,\n", "            \"Author\": \", \".join(authors),\n", "            \"Doi\": doi,\n", "            \"Corresponding\": corresponding_info,\n", "            \"Abstract\": abstract,\n", "            \"PMID\": pmid,  # 保留PMID用于匹配\n", "            \"Affiliations\": affiliations_text,  # 所有单位的列表\n", "            \"Author_Affiliations\": author_affiliations_text  # 作者与单位的对应关系\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"Error retrieving info for PMID {pmid}: {str(e)}\")\n", "        return {\n", "            \"Title\": \"\",\n", "            \"Author\": \"\",\n", "            \"Doi\": \"\",\n", "            \"Corresponding\": \"\",\n", "            \"Abstract\": \"\",\n", "            \"PMID\": pmid,  # 保留PMID用于匹配\n", "            \"Affiliations\": \"\",\n", "            \"Author_Affiliations\": \"\"\n", "        }\n", "\n", "def get_pubmed_article_info(pmid):\n", "    url = f\"https://pubmed.ncbi.nlm.nih.gov/{pmid}/\"\n", "    headers = {\n", "        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n", "    }\n", "    \n", "    try:\n", "        response = requests.get(url, headers=headers)\n", "        response.raise_for_status()\n", "        \n", "        # 解析HTML\n", "        info = extract_author_affiliations(response.text)\n", "        return info\n", "        \n", "    except Exception as e:\n", "        return {\"error\": str(e)}\n", "\n", "# 直接使用get_pubmed_article_info函数测试一个示例PMID\n", "if __name__ == \"__main__\":\n", "    # 使用一个示例PubMed ID进行测试\n", "    sample_pmid = \"25108525\"  # 替换为您想要测试的PMID\n", "    \n", "    result = get_pubmed_article_info(sample_pmid)\n", "    print(json.dumps(result, indent=2, ensure_ascii=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 123 PMIDs in the Excel file\n", "Processing 1/123: PMID 26338483\n", "Processing 2/123: PMID 25263593\n", "Processing 3/123: PMID 26878238\n", "Processing 4/123: PMID 25108525\n", "Processing 5/123: PMID 25070500\n", "Pausing for 3 seconds...\n", "Processing 6/123: PMID 27153541\n"]}], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "import re\n", "import json\n", "import pandas as pd\n", "import time\n", "\n", "def get_complete_pubmed_info(pmid):\n", "    \"\"\"获取PubMed文章的完整信息，包括标题、作者、DOI、通讯作者和摘要\"\"\"\n", "    url = f\"https://pubmed.ncbi.nlm.nih.gov/{pmid}/\"\n", "    headers = {\n", "        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n", "    }\n", "    \n", "    try:\n", "        response = requests.get(url, headers=headers)\n", "        response.raise_for_status()\n", "        soup = BeautifulSoup(response.text, 'html.parser')\n", "        \n", "        # 提取标题\n", "        title = \"\"\n", "        title_tag = soup.find('h1', class_='heading-title')\n", "        if title_tag:\n", "            title = title_tag.text.strip()\n", "        \n", "        # 提取DOI\n", "        doi = \"\"\n", "        doi_tag = soup.find('a', href=lambda x: x and x.startswith('https://doi.org/'))\n", "        if doi_tag:\n", "            doi = doi_tag.text.strip()\n", "        \n", "        # 提取摘要\n", "        abstract = \"\"\n", "        abstract_div = soup.find('div', id='abstract')\n", "        if abstract_div:\n", "            abstract_text = abstract_div.find('div', class_='abstract-content')\n", "            if abstract_text:\n", "                abstract = abstract_text.text.strip()\n", "        \n", "        # 提取作者和单位信息\n", "        authors_info = extract_author_affiliations(response.text)\n", "        \n", "        # 获取所有作者姓名列表\n", "        authors = []\n", "        for author in authors_info.get('authors', []):\n", "            authors.append(author['name'])\n", "        \n", "        # 找出通讯作者\n", "        corresponding_author = \"\"\n", "        corresponding_info = \"\"\n", "        \n", "        # 寻找标记为通讯作者的作者\n", "        corresponding_tag = soup.find('a', attrs={'class': 'corresp-author-link'})\n", "        if corresponding_tag:\n", "            parent_tag = corresponding_tag.find_parent('span', class_='authors-list-item')\n", "            if parent_tag:\n", "                author_name_tag = parent_tag.find('a', class_='full-name')\n", "                if author_name_tag:\n", "                    corresponding_author = author_name_tag.text.strip()\n", "                    \n", "                    # 获取该作者的单位信息\n", "                    for author in authors_info.get('authors', []):\n", "                        if author['name'] == corresponding_author:\n", "                            affiliations = [aff['text'] for aff in author['affiliations']]\n", "                            corresponding_info = \"; \".join(affiliations)\n", "                            break\n", "        \n", "        # 获取所有单位信息的完整列表\n", "        all_affiliations = []\n", "        for affiliation in authors_info.get('affiliations', []):\n", "            all_affiliations.append(f\"{affiliation['number']}: {affiliation['text']}\")\n", "        affiliations_text = \"; \".join(all_affiliations)\n", "        \n", "        # 获取作者与单位的完整对应关系\n", "        author_affiliations = []\n", "        for author in authors_info.get('authors', []):\n", "            author_name = author['name']\n", "            author_affs = []\n", "            for aff in author['affiliations']:\n", "                author_affs.append(f\"{aff['number']}: {aff['text']}\")\n", "            if author_affs:\n", "                author_affiliations.append(f\"{author_name} [{', '.join(author_affs)}]\")\n", "        author_affiliations_text = \"; \".join(author_affiliations)\n", "        \n", "        return {\n", "            \"Title\": title,\n", "            \"Author\": \", \".join(authors),\n", "            \"Doi\": doi,\n", "            \"Corresponding\": corresponding_info,\n", "            \"Abstract\": abstract,\n", "            \"PMID\": pmid,  # 保留PMID用于匹配\n", "            \"Affiliations\": affiliations_text,  # 所有单位的列表\n", "            \"Author_Affiliations\": author_affiliations_text  # 作者与单位的对应关系\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"Error retrieving info for PMID {pmid}: {str(e)}\")\n", "        return {\n", "            \"Title\": \"\",\n", "            \"Author\": \"\",\n", "            \"Doi\": \"\",\n", "            \"Corresponding\": \"\",\n", "            \"Abstract\": \"\",\n", "            \"PMID\": pmid,\n", "            \"Affiliations\": \"\",\n", "            \"Author_Affiliations\": \"\"\n", "        }\n", "def extract_author_affiliations(html_content):\n", "    soup = BeautifulSoup(html_content, 'html.parser')\n", "    \n", "    # 提取作者信息\n", "    authors_data = []\n", "    authors_div = soup.find('div', class_='authors-list')\n", "    \n", "    if authors_div:\n", "        author_spans = authors_div.find_all('span', class_='authors-list-item')\n", "        \n", "        for author_span in author_spans:\n", "            author_name_tag = author_span.find('a', class_='full-name')\n", "            if not author_name_tag:\n", "                continue\n", "                \n", "            author_name = author_name_tag.text.strip()\n", "            \n", "            # 获取作者的单位编号\n", "            affiliation_numbers = []\n", "            affiliation_links = author_span.find_all('a', class_='affiliation-link')\n", "            \n", "            for aff_link in affiliation_links:\n", "                aff_num = aff_link.text.strip()\n", "                if aff_num:\n", "                    affiliation_numbers.append(aff_num)\n", "            \n", "            authors_data.append({\n", "                'name': author_name,\n", "                'affiliation_numbers': affiliation_numbers\n", "            })\n", "    \n", "    # 提取单位信息\n", "    affiliations = {}\n", "    affiliation_list = soup.find('ul', class_='item-list')\n", "    \n", "    if affiliation_list:\n", "        affiliation_items = affiliation_list.find_all('li')\n", "        \n", "        for item in affiliation_items:\n", "            sup_tag = item.find('sup', class_='key')\n", "            if not sup_tag:\n", "                continue\n", "                \n", "            aff_number = sup_tag.text.strip()\n", "            # 移除sup标签内容后获取完整的单位信息\n", "            sup_tag.extract()\n", "            affiliation_text = item.text.strip()\n", "            \n", "            affiliations[aff_number] = affiliation_text\n", "    \n", "    # 关联作者和单位信息\n", "    result = []\n", "    for author in authors_data:\n", "        author_info = {\n", "            'name': author['name'],\n", "            'affiliations': []\n", "        }\n", "        \n", "        for aff_num in author['affiliation_numbers']:\n", "            if aff_num in affiliations:\n", "                author_info['affiliations'].append({\n", "                    'number': aff_num,\n", "                    'text': affiliations[aff_num]\n", "                })\n", "        \n", "        result.append(author_info)\n", "    \n", "    return {\n", "        'authors': result,\n", "        'affiliations': [{'number': k, 'text': v} for k, v in affiliations.items()]\n", "    }\n", "\n", "def update_paper_info_from_pmid():\n", "    \"\"\"从Excel文件中读取PMID，获取相关信息，并更新Excel文件\"\"\"\n", "    # 读取Excel文件\n", "    file_path = 'paper_abstract.xlsx'\n", "    try:\n", "        df = pd.read_excel(file_path)\n", "        \n", "        # 确保PMID列存在\n", "        if 'PMID' not in df.columns:\n", "            print(\"Error: PMID column not found in the Excel file\")\n", "            return\n", "        \n", "        # 确保所有需要的列都存在\n", "        required_columns = ['Title', 'Author', 'Doi', 'Corresponding', 'Abstract', 'Affiliations', 'Author_Affiliations']\n", "        for column in required_columns:\n", "            if column not in df.columns:\n", "                df[column] = \"\"  # 如果列不存在，创建空列\n", "        \n", "        # 记录总条数和进度\n", "        total = len(df)\n", "        print(f\"Found {total} PMIDs in the Excel file\")\n", "        \n", "        # 对每个PMID获取信息并更新DataFrame\n", "        for index, row in df.iterrows():\n", "            if pd.isna(row['PMID']):\n", "                continue\n", "                \n", "            pmid = str(int(row['PMID']))  # 确保PMID是字符串格式，且没有小数点\n", "            print(f\"Processing {index+1}/{total}: PMID {pmid}\")\n", "            \n", "            # 获取PubMed信息\n", "            info = get_complete_pubmed_info(pmid)\n", "            \n", "            # 更新DataFrame\n", "            for column, value in info.items():\n", "                if column != 'PMID' and column in df.columns:  # 不更新PMID列\n", "                    df.at[index, column] = value\n", "            \n", "            # 每5个请求暂停一下，避免被PubMed限制\n", "            if (index + 1) % 5 == 0:\n", "                print(\"Pausing for 3 seconds...\")\n", "                time.sleep(0.5)\n", "        \n", "        # 保存更新后的Excel文件\n", "        output_path = 'paper_abstract_updated.csv'\n", "        df.to_excel(output_path, index=False)\n", "        print(f\"Updated information saved to {output_path}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error: {str(e)}\")\n", "# 运行函数\n", "if __name__ == \"__main__\":\n", "    update_paper_info_from_pmid()"]}], "metadata": {"kernelspec": {"display_name": "pyani_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 2}