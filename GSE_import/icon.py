import requests
import os
from urllib.parse import urlparse

def download_icon(url, filename, save_dir):
    """下载单个图标"""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        filepath = os.path.join(save_dir, filename)
        with open(filepath, 'wb') as f:
            f.write(response.content)
        print(f"✓ 已下载 {filename}")
        return True
    except requests.exceptions.RequestException as e:
        print(f"✗ 下载失败 {filename}: {e}")
        return False

def main():
    # 图标名称映射
    icons = {
        "home": "home.svg",
        "eye": "eye.svg", 
        "file-text": "file-text.svg",
        "bar-chart-3": "bar-chart-3.svg",
        "book-open": "book-open.svg",
        "search": "search.svg",
        "clipboard-list": "clipboard-list.svg",
        "download": "download.svg"
    }
    
    # 多个可能的CDN源
    cdn_sources = [
        "https://cdn.jsdelivr.net/npm/lucide@latest/icons/",
        "https://unpkg.com/lucide@latest/icons/",
        "https://raw.githubusercontent.com/lucide-icons/lucide/main/icons/",
    ]
    
    # 创建保存目录
    save_dir = "lucide_icons"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
        print(f"创建目录: {save_dir}")
    
    success_count = 0
    
    # 尝试每个CDN源
    for base_url in cdn_sources:
        print(f"\n尝试从 {base_url} 下载...")
        temp_success = 0
        
        for icon_name, filename in icons.items():
            if os.path.exists(os.path.join(save_dir, filename)):
                print(f"⏭ 跳过 {filename} (已存在)")
                temp_success += 1
                continue
                
            url = f"{base_url}{icon_name}.svg"
            if download_icon(url, filename, save_dir):
                temp_success += 1
        
        if temp_success == len(icons):
            success_count = temp_success
            print(f"\n🎉 所有图标下载完成！({success_count}/{len(icons)})")
            break
        else:
            print(f"此源下载了 {temp_success}/{len(icons)} 个图标")
    
    if success_count < len(icons):
        print(f"\n⚠️  部分图标下载失败，已下载 {success_count}/{len(icons)} 个")
        print("建议尝试手动下载或使用其他方法")

if __name__ == "__main__":
    main()
