import csv
import mysql.connector
from mysql.connector import Error

# 数据库配置信息
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'utr_database',
    'port': 3306
}

def create_connection():
    """创建数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        print(f"数据库连接错误: {e}")
        return None

def create_table(connection):
    """创建GSE匹配数据表，如果存在则先删除"""
    try:
        cursor = connection.cursor()

        # 删除已存在的表
        drop_table_query = "DROP TABLE IF EXISTS gseMatch;"
        cursor.execute(drop_table_query)
        print("已删除现有的gseMatch表（如果存在）")

        # 创建新表
        create_table_query = """
        CREATE TABLE gseMatch (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sraAccession VARCHAR(50) NOT NULL,
            projectId VARCHAR(50) NOT NULL,
            bioProjectId VARCHAR(50) NOT NULL,
            strategy VARCHAR(20) NOT NULL,
            INDEX idx_sra (sraAccession),
            INDEX idx_project (projectId),
            INDEX idx_bioproject (bioProjectId)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """
        cursor.execute(create_table_query)
        connection.commit()
        print("gseMatch表创建成功")
        return True
    except Error as e:
        print(f"创建表错误: {e}")
        return False

def import_gse_match_data(connection, file_path="GSE_match_new.csv"):
    """导入GSE匹配数据"""
    try:
        cursor = connection.cursor()
        
        # 表已经在create_table函数中重新创建，无需清空
        
        # 读取CSV文件
        with open(file_path, 'r') as csv_file:
            csv_reader = csv.reader(csv_file)
            
            # 跳过标题行
            header = next(csv_reader)
            print(f"读取文件: {file_path}, 标题: {header}")
            
            # 插入数据
            insert_query = """
            INSERT INTO gseMatch (sraAccession, projectId, bioProjectId, strategy)
            VALUES (%s, %s, %s, %s)
            """
            
            # 批量处理以提高性能
            batch_size = 1000
            records = []
            count = 0
            
            for row in csv_reader:
                # 去除引号
                row = [field.strip('"') for field in row]
                
                if len(row) >= 10:  # 确保有足够的列
                    # CSV列顺序: SRA Accession, Project ID, GEO_Accession, BioProject ID, BioSample ID, Tissue, Cell Type, Cell line, Condition, Strategy
                    # 数据库字段: sraAccession, projectId, bioProjectId, strategy
                    records.append((row[0], row[1], row[3], row[9]))
                    count += 1
                    
                    # 批量插入
                    if len(records) >= batch_size:
                        cursor.executemany(insert_query, records)
                        connection.commit()
                        print(f"已插入 {count} 条记录")
                        records = []
            
            # 插入剩余记录
            if records:
                cursor.executemany(insert_query, records)
                connection.commit()
                
            print(f"GSE匹配数据导入完成，共 {count} 条记录")
            return True
            
    except Error as e:
        print(f"导入数据错误: {e}")
        return False
    except Exception as e:
        print(f"处理文件错误: {e}")
        return False

def main():
    """主函数"""
    # 创建数据库连接
    connection = create_connection()
    if connection is None:
        return
    
    # 创建数据表
    if create_table(connection):
        # 导入GSE匹配数据
        import_gse_match_data(connection)
    
    # 关闭数据库连接
    if connection.is_connected():
        connection.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    main() 