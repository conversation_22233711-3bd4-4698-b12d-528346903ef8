# 基因翻译效率统计分析总结报告

## 分析概述

本分析对 `gene/gene_with_translation_indices.csv` 文件进行了全面的统计分析，比较了不同疾病组与正常组之间的基因翻译效率差异。

## 数据概况

- **原始数据**: 1,349,245 行记录
- **分析基因数**: 14,795 个独特基因
- **疾病类型**: 11 种疾病 vs Normal对照组
- **分析变量**: TE (翻译效率) 和 TR (翻译速率)
- **总统计检验数**: 117,765 个

## 分析方法

### 数据预处理
1. 按照 `geneSymbol` 和 `geneId` 进行分组
2. 识别包含非空 TE、TR、EVI 值的记录
3. 以 "Normal" 作为对照组，与各疾病组进行配对比较

### 统计检验
对每个基因-疾病-变量组合执行以下统计检验：

1. **T检验**: 比较两组均值差异
2. **Wilcoxon秩和检验**: 非参数检验，比较分布差异
3. **Kolmogorov-Smirnov检验**: 比较累积分布函数差异

### 多重检验校正
- 使用 Benjamini-Hochberg 方法进行 FDR (False Discovery Rate) 校正
- 显著性阈值: FDR校正后 p < 0.05

## 主要发现

### 1. 整体统计结果

| 检验方法 | 显著结果数 | 总检验数 | 显著率 |
|---------|-----------|---------|--------|
| T检验 (FDR校正) | 2,649 | 117,765 | 2.25% |
| Wilcoxon检验 (FDR校正) | 0 | 117,765 | 0.00% |
| KS检验 (FDR校正) | 0 | 117,765 | 0.00% |

### 2. 表达变化方向分布

- **Lower** (疾病组表达低于正常组): 72,363 (61.45%)
- **Higher** (疾病组表达高于正常组): 45,402 (38.55%)

### 3. 按疾病类型的显著结果分布

| 疾病类型 | 总检验数 | T检验显著数 | 显著率 |
|---------|---------|------------|--------|
| Hepatocellular Carcinoma | 20,818 | 1,157 | 5.56% |
| Osteosarcoma | 9,629 | 400 | 4.15% |
| Chronic Myeloid Leukemia | 8,981 | 290 | 3.23% |
| Ovarian Cancer | 9,145 | 248 | 2.71% |
| Breast Cancer | 10,604 | 186 | 1.75% |
| Prostate Cancer | 8,440 | 173 | 2.05% |
| Tuberous Sclerosis Complex | 11,112 | 80 | 0.72% |
| Cervical Cancer | 11,023 | 49 | 0.44% |
| Lung Adenocarcinoma; Swine Influenza | 9,403 | 12 | 0.13% |
| Lung Adenocarcinoma | 10,229 | 32 | 0.31% |
| Breast Ductal Carcinoma | 8,381 | 22 | 0.26% |

### 4. 按变量类型分布

- **TE (翻译效率)**: 108,151 个检验
- **TR (翻译速率)**: 9,614 个检验

### 5. 最显著的基因发现

显著结果最多的基因（前10个）：

1. **EEIG1**: 5/12 检验显著 (41.7%)
2. **PEDS1**: 5/12 检验显著 (41.7%)
3. **GBA2**: 4/12 检验显著 (33.3%)
4. **ZNF232**: 4/8 检验显著 (50.0%)
5. **MAP3K14**: 3/9 检验显著 (33.3%)
6. **TRIO**: 3/11 检验显著 (27.3%)
7. **GRIPAP1**: 3/12 检验显著 (25.0%)
8. **ATXN7L3**: 3/12 检验显著 (25.0%)
9. **KIF16B**: 3/9 检验显著 (33.3%)
10. **HM13**: 3/12 检验显著 (25.0%)

## 生成的文件

1. **gene_statistical_analysis_results.csv**: 完整的统计分析结果
   - 包含所有117,765个统计检验的详细结果
   - 列包括: geneId, geneSymbol, disease_category, p_t, fdr_t, p_wilcox, fdr_wilcox, p_ks, fdr_ks, direction, variable

2. **significant_gene_results.csv**: 显著结果汇总
   - 包含2,649个FDR校正后显著的结果 (p < 0.05)
   - 按显著性水平排序

3. **gene_analysis_summary_report.txt**: 详细分析报告
   - 包含完整的统计摘要和模式分析

## 技术说明

### 统计方法实现
- 使用Python内置库实现统计检验算法
- T检验: 双样本独立t检验，假设等方差
- Wilcoxon检验: Mann-Whitney U检验的实现
- KS检验: 双样本Kolmogorov-Smirnov检验
- FDR校正: Benjamini-Hochberg方法

### 数据质量控制
- 自动过滤缺失值和无效数据
- 要求每组至少2个有效观测值才进行统计检验
- 处理数值转换异常和边界情况

## 结论

1. **肝细胞癌 (Hepatocellular Carcinoma)** 显示出最多的翻译效率差异，有1,157个基因显著改变
2. **骨肉瘤 (Osteosarcoma)** 和 **慢性粒细胞白血病 (Chronic Myeloid Leukemia)** 也显示出较多的翻译效率变化
3. 大多数显著变化表现为疾病组翻译效率**降低** (61.45%)
4. TR (翻译速率) 相比TE (翻译效率) 显示出更少但可能更特异的变化
5. 某些基因如 EEIG1、PEDS1 在多种疾病中都显示出翻译效率的显著变化

这些发现为理解疾病状态下的翻译调控机制提供了重要线索，可为进一步的功能研究和治疗靶点发现提供参考。
