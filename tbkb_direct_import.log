2025-06-23 18:42:00,581 - INFO - === tbkb 直接导入工具 (使用 mysql 命令) ===
2025-06-23 18:42:00,652 - INFO - 找到 MySQL 客户端: /usr/local/mysql/bin/mysql
2025-06-23 18:42:00,652 - INFO - 版本: /usr/local/mysql/bin/mysql  Ver 8.4.2 for macos14 on arm64 (MySQL Community Server - GPL)
2025-06-23 18:42:00,782 - INFO - ✅ 数据库连接测试成功!
2025-06-23 18:42:00,793 - INFO - 找到 MySQL 客户端: /usr/local/mysql/bin/mysql
2025-06-23 18:42:00,793 - INFO - 版本: /usr/local/mysql/bin/mysql  Ver 8.4.2 for macos14 on arm64 (MySQL Community Server - GPL)
2025-06-23 18:42:00,793 - INFO - 开始使用 mysql 命令导入...
2025-06-23 18:42:00,793 - INFO - 目标数据库: 192.168.129.38:3306/tbkb
2025-06-23 18:42:02,877 - INFO - 执行导入命令...
2025-06-23 18:42:02,877 - INFO - 这可能需要几分钟时间，请耐心等待...
2025-06-23 18:42:02,887 - ERROR - ❌ 导入失败，返回码: 2
2025-06-23 18:42:02,887 - ERROR - 错误信息: mysql: [Warning] Using a password on the command line interface can be insecure.
mysql: [ERROR] unknown option '--single-transaction'.

2025-06-23 18:42:02,887 - ERROR - 💥 数据库导入失败!
2025-06-23 18:43:14,057 - INFO - === tbkb 直接导入工具 (使用 mysql 命令) ===
2025-06-23 18:43:14,074 - INFO - 找到 MySQL 客户端: /usr/local/mysql/bin/mysql
2025-06-23 18:43:14,074 - INFO - 版本: /usr/local/mysql/bin/mysql  Ver 8.4.2 for macos14 on arm64 (MySQL Community Server - GPL)
2025-06-23 18:43:14,266 - INFO - ✅ 数据库连接测试成功!
2025-06-23 18:43:14,275 - INFO - 找到 MySQL 客户端: /usr/local/mysql/bin/mysql
2025-06-23 18:43:14,275 - INFO - 版本: /usr/local/mysql/bin/mysql  Ver 8.4.2 for macos14 on arm64 (MySQL Community Server - GPL)
2025-06-23 18:43:14,275 - INFO - 开始使用 mysql 命令导入...
2025-06-23 18:43:14,275 - INFO - 目标数据库: 192.168.129.38:3306/tbkb
2025-06-23 18:43:17,463 - INFO - 执行导入命令...
2025-06-23 18:43:17,463 - INFO - 这可能需要几分钟时间，请耐心等待...
2025-06-23 18:46:45,261 - INFO - ✅ 数据库导入成功!
2025-06-23 18:46:45,267 - INFO - 🎉 数据库导入完成!
