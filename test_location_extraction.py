#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试位置信息提取功能
"""

import requests
import re
from bs4 import BeautifulSoup

# 请求头
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

def extract_location_from_html(html_content):
    """从HTML内容中提取位置信息"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找包含Location的行
        location_divs = soup.find_all('div', class_='lhs')
        for div in location_divs:
            if div.get_text().strip() == 'Location':
                # 找到对应的rhs div
                rhs_div = div.find_next_sibling('div', class_='rhs')
                if rhs_div:
                    # 提取位置文本
                    location_link = rhs_div.find('a', class_='constant dynamic-link')
                    if location_link:
                        location_text = location_link.get_text().strip()
                        # 移除" forward strand."或" reverse strand."后缀
                        location_text = re.sub(r'\s+(forward|reverse)\s+strand\.$', '', location_text)
                        return location_text
                    else:
                        # 如果没有链接，尝试提取纯文本
                        text = rhs_div.get_text().strip()
                        # 使用正则表达式提取染色体位置信息
                        match = re.search(r'Chromosome\s+[^:]+:\s*[\d,]+-[\d,]+', text)
                        if match:
                            return match.group(0)
        
        return None
        
    except Exception as e:
        print(f"解析HTML时出错: {e}")
        return None

def test_with_sample_html():
    """使用提供的示例HTML测试"""
    sample_html = '''
    <div class="twocol">
        <div class="row">
            <div class="lhs">Description</div>
            <div class="rhs"><p>proteolipid protein 1 [Source:HGNC Symbol;Acc:<a href="https://www.genenames.org/data/gene-symbol-report/#!/hgnc_id/HGNC:9086" class="constant" rel="external">HGNC:9086</a>]</p></div>
        </div>
        <div class="row">
            <div class="lhs">Gene Synonyms</div>
            <div class="rhs"><p>GPM6C, PLP, SPG2</p></div>
        </div>
        <div class="row">
            <div class="lhs">Location</div>
            <div class="rhs"><p><a href="/Homo_sapiens/Location/View?db=core;g=ENSG00000123560;r=X:103776874-103786583;t=ENST00000464776" class="constant dynamic-link">Chromosome X: 103,776,874-103,786,583</a> forward strand.</p></div>
        </div>
    </div>
    '''
    
    location = extract_location_from_html(sample_html)
    print(f"从示例HTML提取的位置信息: {location}")
    return location

def test_with_real_url():
    """测试真实URL"""
    test_url = "https://www.ensembl.org/Homo_sapiens/Transcript/Summary?t=ENST00000464776"
    
    try:
        print(f"正在访问: {test_url}")
        response = requests.get(test_url, headers=HEADERS, timeout=30)
        response.raise_for_status()
        
        location = extract_location_from_html(response.text)
        print(f"从真实URL提取的位置信息: {location}")
        return location
        
    except Exception as e:
        print(f"访问URL时出错: {e}")
        return None

if __name__ == "__main__":
    print("=== 测试位置信息提取 ===")
    
    print("\n1. 测试示例HTML:")
    sample_result = test_with_sample_html()
    
    print("\n2. 测试真实URL:")
    real_result = test_with_real_url()
    
    print(f"\n=== 测试结果 ===")
    print(f"示例HTML结果: {sample_result}")
    print(f"真实URL结果: {real_result}")
    
    if sample_result == "Chromosome X: 103,776,874-103,786,583":
        print("✓ 示例HTML测试通过")
    else:
        print("✗ 示例HTML测试失败")
    
    if real_result:
        print("✓ 真实URL测试通过")
    else:
        print("✗ 真实URL测试失败")
