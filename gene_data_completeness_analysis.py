#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基因数据完整性分析脚本
分析 gene_count_by_project_results_with_chromosome.csv 文件中每个基因的数据完整性
"""

import pandas as pd
import numpy as np
import sys
from datetime import datetime

def analyze_gene_data_completeness():
    """
    分析基因数据的完整性
    """
    print("=" * 60)
    print("基因数据完整性分析")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 读取数据文件
    input_file = 'gene/gene_count_by_project_results_with_chromosome.csv'
    print(f"\n正在读取文件: {input_file}")
    
    try:
        # 分块读取大文件以节省内存
        chunk_size = 50000
        chunks = []
        total_rows = 0
        
        print("正在分块读取文件...")
        for i, chunk in enumerate(pd.read_csv(input_file, chunksize=chunk_size)):
            chunks.append(chunk)
            total_rows += len(chunk)
            if (i + 1) % 10 == 0:
                print(f"已读取 {(i + 1) * chunk_size} 行...")
        
        # 合并所有块
        print("正在合并数据块...")
        df = pd.concat(chunks, ignore_index=True)
        print(f"成功读取文件，总共 {total_rows} 行，{len(df.columns)} 列")
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        return
    
    # 显示基本信息
    print(f"\n文件列名: {df.columns.tolist()}")
    print(f"数据形状: {df.shape}")
    
    # 检查必要的列是否存在
    required_columns = ['geneId', 'TR', 'EVI', 'TE']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必要的列: {missing_columns}")
        return
    
    print(f"\n开始分析数据完整性...")
    print("分析目标: 检查每个 geneId 的 TR、EVI、TE 三列数据完整性")
    
    # 按 geneId 分组进行分析
    print("\n正在按 geneId 分组...")
    
    # 创建一个函数来检查数据完整性
    def check_completeness(group):
        """检查一个基因组的数据完整性"""
        total_count = len(group)
        
        # 检查 TR、EVI、TE 三列都有非空值的行
        # 使用 notna() 检查非 NaN 值，并且不是空字符串
        tr_valid = group['TR'].notna() & (group['TR'] != '') & (group['TR'] != 0)
        evi_valid = group['EVI'].notna() & (group['EVI'] != '') & (group['EVI'] != 0)
        te_valid = group['TE'].notna() & (group['TE'] != '') & (group['TE'] != 0)
        
        # 三列都有有效值的行
        complete_rows = tr_valid & evi_valid & te_valid
        complete_count = complete_rows.sum()
        
        # 计算百分比
        if total_count > 0:
            percentage = (complete_count / total_count) * 100
        else:
            percentage = 0
        
        return pd.Series({
            'total_count': total_count,
            'complete_count': complete_count,
            'complete_data_percentage': percentage
        })
    
    # 执行分组分析
    print("正在计算每个基因的数据完整性...")
    result = df.groupby('geneId').apply(check_completeness).reset_index()
    
    print(f"分析完成！共分析了 {len(result)} 个唯一的基因")
    
    # 显示统计摘要
    print("\n" + "=" * 50)
    print("统计摘要")
    print("=" * 50)
    
    total_genes = len(result)
    genes_with_complete_data = len(result[result['complete_data_percentage'] > 0])
    genes_with_zero_complete = len(result[result['complete_data_percentage'] == 0])
    
    print(f"总基因数: {total_genes:,}")
    print(f"有完整数据的基因数: {genes_with_complete_data:,}")
    print(f"完全没有完整数据的基因数: {genes_with_zero_complete:,}")
    print(f"有完整数据的基因占比: {(genes_with_complete_data/total_genes)*100:.2f}%")
    
    # 完整性百分比分布
    print(f"\n完整性百分比分布:")
    print(f"0%: {len(result[result['complete_data_percentage'] == 0]):,} 个基因")
    print(f"0-25%: {len(result[(result['complete_data_percentage'] > 0) & (result['complete_data_percentage'] <= 25)]):,} 个基因")
    print(f"25-50%: {len(result[(result['complete_data_percentage'] > 25) & (result['complete_data_percentage'] <= 50)]):,} 个基因")
    print(f"50-75%: {len(result[(result['complete_data_percentage'] > 50) & (result['complete_data_percentage'] <= 75)]):,} 个基因")
    print(f"75-100%: {len(result[(result['complete_data_percentage'] > 75) & (result['complete_data_percentage'] <= 100)]):,} 个基因")
    print(f"100%: {len(result[result['complete_data_percentage'] == 100]):,} 个基因")
    
    # 数据过滤：排除完整性为0%的基因
    print(f"\n正在过滤数据...")
    filtered_result = result[result['complete_data_percentage'] > 0].copy()
    excluded_count = len(result) - len(filtered_result)
    
    print(f"排除了 {excluded_count:,} 个完整性为0%的基因")
    print(f"最终结果包含 {len(filtered_result):,} 个基因")
    
    # 格式化百分比列
    filtered_result['complete_data_percentage'] = filtered_result['complete_data_percentage'].round(2)
    
    # 按完整性百分比降序排序
    filtered_result = filtered_result.sort_values('complete_data_percentage', ascending=False)
    
    # 显示前10个结果作为预览
    print(f"\n前10个完整性最高的基因:")
    print(filtered_result.head(10).to_string(index=False))
    
    # 保存结果
    output_file = f"gene_data_completeness_analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    print(f"\n正在保存结果到: {output_file}")
    
    try:
        # 只保存需要的三列
        final_output = filtered_result[['geneId', 'total_count', 'complete_data_percentage']].copy()
        final_output.to_csv(output_file, index=False, encoding='utf-8')
        print(f"结果已成功保存到: {output_file}")
        print(f"输出文件包含 {len(final_output)} 行数据")
        
        # 显示输出文件的统计信息
        print(f"\n输出文件统计:")
        print(f"平均完整性: {final_output['complete_data_percentage'].mean():.2f}%")
        print(f"中位数完整性: {final_output['complete_data_percentage'].median():.2f}%")
        print(f"最高完整性: {final_output['complete_data_percentage'].max():.2f}%")
        print(f"最低完整性: {final_output['complete_data_percentage'].min():.2f}%")
        
    except Exception as e:
        print(f"保存文件失败: {e}")
        return
    
    print(f"\n分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

if __name__ == "__main__":
    analyze_gene_data_completeness()
