import pandas as pd
import mysql.connector
import csv
from datetime import datetime
import sys
import re

# 数据库配置信息
DB_CONFIG = {
    'host': '**************',
    'user': 'luty',
    'password': 'vb70e57o7U!G',
    'database': 'utr_database',
    'port': 3306
}

def clean_text_field(text):
    """清理文本字段，去除换行符和多余空格；同时将 NaN、'NA'、'nan' 统一视为空"""
    # 处理 pandas 的 NaN / None / 空字符串 / 'NA' / 'nan'
    try:
        if pd.isna(text):
            return None
    except ImportError:
        pass  # pandas 已经在环境中，理论上不会导入失败

    if text is None or str(text).strip() == '' or str(text).strip().upper() in {'NA', 'NAN'}:
        return None
    # 将换行符替换为空格，并清理多余空格
    cleaned = re.sub(r'\s+', ' ', str(text).replace('\n', ' ').replace('\r', ' '))
    return cleaned.strip()

def create_project_table(cursor):
    """创建项目信息表（使用驼峰命名法，根据Project_processed.csv的列名）"""
    
    # 检查表是否存在，如果存在则删除
    check_table_sql = """
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_schema = 'utr_database' 
    AND table_name = 'projectInfo'
    """
    cursor.execute(check_table_sql)
    table_exists = cursor.fetchone()[0] > 0
    
    if table_exists:
        print("检测到projectInfo表已存在，正在删除...")
        cursor.execute("DROP TABLE projectInfo")
        print("旧表已删除")

    # 创建新表，Project ID作为主键
    create_table_sql = """
    CREATE TABLE projectInfo (
        projectId VARCHAR(100) PRIMARY KEY,
        bioProjectId VARCHAR(100),
        geoAccession VARCHAR(100),
        strategy VARCHAR(100),
        tissueOrCellType VARCHAR(200),
        cellLine VARCHAR(100),
        disease VARCHAR(200),
        srrNumber INT,
        pmid VARCHAR(50),
        translatedTranscriptsNumber INT
    )
    """
    cursor.execute(create_table_sql)
    print("项目信息表创建成功！(Project ID为主键)")

def create_indexes(cursor):
    """为每一列创建普通索引"""
    indexes = [
        ('idx_projectId', 'projectId'),
        ('idx_bioProjectId', 'bioProjectId'),
        ('idx_geoAccession', 'geoAccession'),
        ('idx_strategy', 'strategy'),
        ('idx_tissueOrCellType', 'tissueOrCellType'),
        ('idx_cellLine', 'cellLine'),
        ('idx_disease', 'disease'),
        ('idx_srrNumber', 'srrNumber'),
        ('idx_pmid', 'pmid'),
        ('idx_translatedTranscriptsNumber', 'translatedTranscriptsNumber')
    ]
    
    for index_name, column_spec in indexes:
        try:
            # 检查索引是否已存在
            cursor.execute(f"SHOW INDEX FROM projectInfo WHERE Key_name = '{index_name}'")
            index_exists = cursor.fetchone() is not None
            
            if index_exists:
                print(f"索引 {index_name} 已存在，跳过创建")
                continue
                
            cursor.execute(f"CREATE INDEX {index_name} ON projectInfo ({column_spec})")
            print(f"索引 {index_name} 创建成功！")
        except mysql.connector.Error as e:
            if "Duplicate key name" in str(e):
                print(f"索引 {index_name} 已存在，跳过创建")
            else:
                print(f"创建索引 {index_name} 时出错: {e}")

def import_project_data(cursor, conn):
    """导入项目数据"""
    # CSV文件路径
    csv_path = 'Browse-Project/Project_processed.csv'
    
    # 准备SQL语句（使用驼峰命名法）
    insert_sql = """
    INSERT INTO projectInfo (
        projectId, bioProjectId, geoAccession, strategy, 
        tissueOrCellType, cellLine, disease, srrNumber, pmid, 
        translatedTranscriptsNumber
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    
    # 读取CSV并处理数据
    projects = []
    line_count = 0
    skipped_lines = 0
    valid_project_ids = set()
    
    # 使用pandas读取CSV文件，更好地处理带引号的字段
    try:
        df = pd.read_csv(csv_path, encoding='utf-8')
        print(f"CSV文件列名: {list(df.columns)}")
        print(f"读取到 {len(df)} 条记录")
        
        for index, row in df.iterrows():
            line_count += 1
            
            # 检查必要的主键值是否存在
            project_id = clean_text_field(row.get('Project ID'))
            if not project_id:
                print(f"跳过第 {index+1} 行: 缺少Project ID")
                skipped_lines += 1
                continue
            
            # 检查这个Project ID是否已经处理过（防止重复）
            if project_id in valid_project_ids:
                print(f"跳过第 {index+1} 行: Project ID '{project_id}' 重复")
                skipped_lines += 1
                continue
            
            valid_project_ids.add(project_id)
            
            # 处理所有字段
            try:
                # 清理所有文本字段
                bioproject_id = clean_text_field(row.get('BioProject ID'))
                geo_accession = clean_text_field(row.get('GEO_Accession'))
                strategy = clean_text_field(row.get('Strategy'))
                tissue_or_cell_type = clean_text_field(row.get('Tissue/Cell Type'))
                cell_line = clean_text_field(row.get('Cell line'))
                disease = clean_text_field(row.get('Disease'))
                
                # 处理数字字段
                srr_number = None
                if pd.notna(row.get('SRR Number')) and str(row.get('SRR Number')).strip() != '':
                    try:
                        srr_number = int(float(row.get('SRR Number')))
                    except (ValueError, TypeError):
                        srr_number = None
                
                translated_transcripts_number = None
                if pd.notna(row.get('Translated transcripts Number')) and str(row.get('Translated transcripts Number')).strip() != '':
                    try:
                        translated_transcripts_number = int(float(row.get('Translated transcripts Number')))
                    except (ValueError, TypeError):
                        translated_transcripts_number = None

                # 处理PMID字段
                pmid = clean_text_field(row.get('PMID'))
                if pmid and pmid.isdigit():
                    pmid = pmid
                elif pmid:
                    pmid = pmid  # 保持原样，可能是多个PMID或其他格式
                else:
                    pmid = None
                
                # 组装数据元组
                project_data = (
                    project_id,
                    bioproject_id,
                    geo_accession,
                    strategy,
                    tissue_or_cell_type,
                    cell_line,
                    disease,
                    srr_number,
                    pmid,
                    translated_transcripts_number
                )
                projects.append(project_data)
                
            except Exception as e:
                print(f"处理第 {index+1} 行时出错: {e}")
                print(f"行数据: {dict(row)}")
                skipped_lines += 1
                
    except Exception as e:
        print(f"读取CSV文件时出错: {e}")
        return
    
    print(f"CSV文件总行数: {line_count}，跳过行数: {skipped_lines}，有效行数: {len(projects)}")
    
    # 批量插入数据
    successful = 0
    failed = 0
    
    for i, project_data in enumerate(projects):
        try:
            cursor.execute(insert_sql, project_data)
            successful += 1
            
            # 每100条记录提交一次
            if successful % 100 == 0:
                conn.commit()
                print(f"已处理 {successful} 条记录...")
                
        except mysql.connector.Error as err:
            print(f"无法导入第 {i+1} 条数据 (Project ID: {project_data[0]}): {err}")
            failed += 1
            continue
    
    # 提交剩余的事务
    conn.commit()
    
    # 检查最终表中的记录数
    cursor.execute("SELECT COUNT(*) FROM projectInfo")
    final_count = cursor.fetchone()[0]
    
    print(f"成功导入 {successful} 条项目数据")
    print(f"失败 {failed} 条记录")
    print(f"数据库中总记录数: {final_count}")

def update_project_data_from_dataset_update(cursor, conn, csv_path='Browse-Project/Dataset update.csv'):
    """根据 Dataset update.csv 更新 tissueOrCellType、cellLine 和 disease 三列的数据"""
    try:
        df = pd.read_csv(csv_path, encoding='utf-8')
        print(f"正在根据 {csv_path} 更新项目数据，共 {len(df)} 条记录……")
    except Exception as e:
        print(f"读取 {csv_path} 时出错: {e}")
        return

    update_sql = """
    UPDATE projectInfo
    SET tissueOrCellType = %s,
        cellLine = %s,
        disease = %s
    WHERE projectId = %s
    """

    updated = 0
    skipped = 0

    for index, row in df.iterrows():
        project_id = clean_text_field(row.get('Dataset ID'))  # Dataset update.csv 中的主键列
        if not project_id:
            skipped += 1
            continue

        tissue_or_cell_type = clean_text_field(row.get('Tissue/Cell Type'))
        # Dataset update.csv 里列名为 "Cell Line"，但为了兼容其它大小写差异，再尝试一次
        cell_line = clean_text_field(row.get('Cell Line')) if 'Cell Line' in row else clean_text_field(row.get('Cell line'))
        disease = clean_text_field(row.get('Disease'))

        try:
            cursor.execute(update_sql, (tissue_or_cell_type, cell_line, disease, project_id))
            if cursor.rowcount > 0:
                updated += 1
            else:
                # 如果没有匹配行，记录跳过
                skipped += 1
        except mysql.connector.Error as e:
            print(f"更新 Project ID {project_id} 时出错: {e}")
            skipped += 1
            continue

    conn.commit()
    print(f"更新完成！成功更新 {updated} 条记录，跳过 {skipped} 条记录。")

def main():
    """主函数：仅更新项目表中的 tissueOrCellType、cellLine、disease 三列"""
    try:
        # 连接 MySQL 数据库
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor()

        print("更新项目数据...")
        update_project_data_from_dataset_update(cursor, conn)

        print("项目数据更新完毕！")

    except Exception as e:
        print(f"更新过程中发生错误: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

if __name__ == '__main__':
    main()